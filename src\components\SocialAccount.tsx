
import React from 'react';

interface SocialAccountProps {
  id: string;
  platform: string;
  username: string;
  profileUrl: string;
  avatarUrl: string;
}

export const SocialAccount: React.FC<SocialAccountProps> = ({ 
  platform, 
  username, 
  avatarUrl 
}) => {
  return (
    <div className="flex items-center gap-2 p-2 border rounded-lg">
      <img 
        src={avatarUrl} 
        alt={`${platform} avatar`} 
        className="w-8 h-8 rounded-full"
      />
      <div>
        <p className="text-sm font-medium">{username}</p>
        <p className="text-xs text-muted-foreground">{platform}</p>
      </div>
    </div>
  );
};

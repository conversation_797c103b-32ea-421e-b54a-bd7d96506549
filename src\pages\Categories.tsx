import React from 'react';
import ContentCategories from '@/components/ContentCategories';
import { useAppStore } from '@/store/appStore';
import PageHeader from '@/components/PageHeader';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import { Button } from '@/components/ui/button';
import { Folder, Plus } from 'lucide-react';

const Categories: React.FC = () => {
  const [createModalOpen, setCreateModalOpen] = React.useState(false);

  const {
    categories,
    createCategory,
    updateCategory,
    deleteCategory
  } = useAppStore();

  const handleCreateCategory = () => {
    setCreateModalOpen(true);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Content Categories"
              description="Organize your content with custom categories and recycling settings"
              icon={<Folder className="w-8 h-8" />}
              actions={
                <Button onClick={handleCreateCategory} className="bg-white text-blue-600 hover:bg-white/90">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Category
                </Button>
              }
            />

            <div className="px-6 pb-6">
              <ContentCategories
                categories={categories}
                onCreateCategory={createCategory}
                onUpdateCategory={updateCategory}
                onDeleteCategory={deleteCategory}
                createModalOpen={createModalOpen}
                onCreateModalChange={setCreateModalOpen}
              />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Categories;

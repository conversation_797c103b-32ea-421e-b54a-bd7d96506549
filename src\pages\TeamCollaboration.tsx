import React, { useEffect } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import PageHeader from '@/components/PageHeader';
import TeamCollaboration from '@/components/TeamCollaboration';
import { UserCheck } from 'lucide-react';
import { useAppStore } from '@/store/appStore';

const TeamCollaborationPage: React.FC = () => {
  const {
    posts,
    workspace,
    loadWorkspace,
    inviteMember,
    updateMemberRole,
    removeMember
  } = useAppStore();

  // Load workspace on component mount if not already loaded
  useEffect(() => {
    if (!workspace) {
      loadWorkspace();
    }
  }, [workspace, loadWorkspace]);

  const handleInviteMember = (email: string, role: string) => {
    inviteMember(email, role);
  };

  const handleUpdateMemberRole = (memberId: string, role: string) => {
    updateMemberRole(memberId, role);
  };

  const handleRemoveMember = (memberId: string) => {
    removeMember(memberId);
  };

  const handleApprovePost = (postId: string) => {
    console.log('Approve post:', postId);
    // Implementation would approve post
  };

  const handleRejectPost = (postId: string, reason: string) => {
    console.log('Reject post:', postId, reason);
    // Implementation would reject post
  };

  const handleAddNote = (postId: string, note: string, mentions: string[]) => {
    console.log('Add note:', postId, note, mentions);
    // Implementation would add note
  };

  // Filter posts that need approval
  const pendingPosts = posts.filter(p => p.approval_status === 'pending');

  // Show loading state if workspace is not loaded
  if (!workspace) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <TopHeader />
            <main className="flex-1 overflow-auto">
              <PageHeader
                title="Team Collaboration"
                description="Workspaces, approval workflows, and team management"
                icon={<UserCheck className="w-8 h-8" />}
              />
              <div className="px-6 pb-6">
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading workspace...</p>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Team Collaboration"
              description="Workspaces, approval workflows, and team management"
              icon={<UserCheck className="w-8 h-8" />}
            />

            <div className="px-6 pb-6">
              <TeamCollaboration
                workspace={workspace}
                pendingPosts={pendingPosts}
                onInviteMember={handleInviteMember}
                onUpdateMemberRole={handleUpdateMemberRole}
                onRemoveMember={handleRemoveMember}
                onApprovePost={handleApprovePost}
                onRejectPost={handleRejectPost}
                onAddNote={handleAddNote}
              />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default TeamCollaborationPage;

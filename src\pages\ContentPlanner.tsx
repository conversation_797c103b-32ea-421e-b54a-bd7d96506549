
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Calendar, List, BarChart3 } from "lucide-react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { TopHeader } from "@/components/TopHeader";
import PageHeader from "@/components/PageHeader";
import { useAppStore } from "@/store/appStore";
import { useNavigate } from "react-router-dom";
import CalendarView from "@/components/CalendarView";
import PostScheduler from "@/components/PostScheduler";
import { CreatorStats } from "@/components/CreatorStats";
import ScheduledPostsList from "@/components/ScheduledPostsList";

const ContentPlanner = () => {
  const [showPostScheduler, setShowPostScheduler] = useState(false);
  const [activeTab, setActiveTab] = useState("calendar");
  const navigate = useNavigate();
  const { posts, categories } = useAppStore();

  const handleCreatePost = () => {
    navigate('/create');
  };

  const handleCloseScheduler = () => {
    setShowPostScheduler(false);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          
          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Content Planner"
              description="Plan, schedule, and manage your social media content"
              icon={<Calendar className="w-8 h-8" />}
              actions={
                <Button
                  onClick={handleCreatePost}
                  className="bg-white text-blue-600 hover:bg-white/90"
                  size="default"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Post
                </Button>
              }
            />

            <div className="p-6 space-y-6">

              {/* Stats Overview */}
              <CreatorStats />

              {/* Main Content Tabs */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto sm:mx-0 sm:w-auto sm:max-w-none">
                  <TabsTrigger value="calendar" className="flex items-center justify-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
                    <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Calendar</span>
                  </TabsTrigger>
                  <TabsTrigger value="list" className="flex items-center justify-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
                    <List className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Posts</span>
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="flex items-center justify-center space-x-1 sm:space-x-2 text-xs sm:text-sm">
                    <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="hidden xs:inline">Analytics</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="calendar" className="space-y-6">
                  <CalendarView onCreatePost={handleCreatePost} />
                </TabsContent>

                <TabsContent value="list" className="space-y-6">
                  <ScheduledPostsList onCreatePost={handleCreatePost} />
                </TabsContent>

                <TabsContent value="analytics" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Content Performance Analytics</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-12">
                        <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <p className="text-gray-600">Analytics coming soon</p>
                        <p className="text-sm text-gray-500 mt-2">
                          Track your content performance, engagement rates, and optimization insights
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              {/* Post Scheduler Modal */}
              {showPostScheduler && (
                <div
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
                  onClick={(e) => {
                    if (e.target === e.currentTarget) {
                      handleCloseScheduler();
                    }
                  }}
                  role="dialog"
                  aria-modal="true"
                  aria-labelledby="post-scheduler-title"
                >
                  <div
                    className="bg-card rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <PostScheduler onClose={handleCloseScheduler} />
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default ContentPlanner;

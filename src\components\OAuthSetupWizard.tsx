import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Key,
  Globe,
  Shield,
  Zap,
  Save,
  Eye,
  EyeOff,
  Database
} from 'lucide-react';
import { config, getPlatformInfo } from '@/config/oauth';
import { useToast } from '@/hooks/use-toast';
import { useOAuthCredentials, oauthCredentialsService, type OAuthCredential } from '@/services/oauthCredentialsService';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import OAuthCredentialsTest from './OAuthCredentialsTest';

interface OAuthSetupWizardProps {
  onClose?: () => void;
}

const OAuthSetupWizard: React.FC<OAuthSetupWizardProps> = ({ onClose }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedText, setCopiedText] = useState<string>('');
  const [storedCredentials, setStoredCredentials] = useState<OAuthCredential[]>([]);
  const [loading, setLoading] = useState(false);
  const [credentialInputs, setCredentialInputs] = useState<Record<string, { clientId: string; clientSecret: string; showSecret: boolean }>>({});

  const platforms = Object.keys(config.oauth) as Array<keyof typeof config.oauth>;

  // Initialize OAuth credentials service and load stored credentials
  useEffect(() => {
    const initializeCredentials = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        
        // Initialize encryption service
        const { initializeEncryption } = useOAuthCredentials();
        await initializeEncryption();
        
        // Set workspace (assuming user has a default workspace)
        const workspaceId = user.user_metadata?.workspace_id || user.id; // Fallback to user ID
        oauthCredentialsService.setWorkspace(workspaceId);
        
        // Load existing credentials
        const credentials = await oauthCredentialsService.getCredentials();
        setStoredCredentials(credentials);
        
      } catch (error) {
        console.error('Failed to initialize OAuth credentials:', error);
        toast({
          title: "Initialization Error",
          description: "Failed to load OAuth credentials. Please refresh the page.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    initializeCredentials();
  }, [user, toast]);

  // Check if a platform has stored credentials
  const hasStoredCredentials = (platform: string): boolean => {
    return storedCredentials.some(cred => 
      cred.platform === platform && 
      cred.isActive && 
      cred.verificationStatus === 'verified'
    );
  };

  // Handle credential input changes
  const handleCredentialChange = (platform: string, field: 'clientId' | 'clientSecret', value: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
  };

  // Toggle password visibility
  const toggleSecretVisibility = (platform: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        showSecret: !prev[platform]?.showSecret
      }
    }));
  };

  // Save credentials to secure storage
  const saveCredentials = async (platform: string) => {
    const input = credentialInputs[platform];
    if (!input?.clientId || !input?.clientSecret) {
      toast({
        title: "Missing Credentials",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      const platformInfo = getPlatformInfo(platform);
      await oauthCredentialsService.saveCredentials({
        platform,
        platformDisplayName: platformInfo.displayName,
        clientId: input.clientId,
        clientSecret: input.clientSecret
      });

      // Refresh stored credentials
      const credentials = await oauthCredentialsService.getCredentials();
      setStoredCredentials(credentials);

      // Clear input fields
      setCredentialInputs(prev => ({
        ...prev,
        [platform]: { clientId: '', clientSecret: '', showSecret: false }
      }));

      toast({
        title: "Credentials Saved",
        description: `${platformInfo.displayName} credentials saved securely`,
      });

    } catch (error) {
      console.error('Failed to save credentials:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save credentials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      setTimeout(() => setCopiedText(''), 2000);
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const getSetupStatus = () => {
    const configured = platforms.filter(platform => hasStoredCredentials(platform));
    return {
      total: platforms.length,
      configured: configured.length,
      platforms: configured
    };
  };

  const status = getSetupStatus();

  const PlatformSetupCard = ({ platform }: { platform: keyof typeof config.oauth }) => {
    const platformInfo = getPlatformInfo(platform);
    const isConfigured = hasStoredCredentials(platform);
    const currentInput = credentialInputs[platform] || { clientId: '', clientSecret: '', showSecret: false };

    const getRedirectUri = () => {
      const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8080';
      return `${origin}/auth/${platform}/callback`;
    };

    const getDeveloperUrl = () => {
      const urls = {
        instagram: 'https://developers.facebook.com/apps/',
        facebook: 'https://developers.facebook.com/apps/',
        linkedin: 'https://www.linkedin.com/developers/apps',
        twitter: 'https://developer.twitter.com/en/portal/dashboard'
      };
      return urls[platform];
    };

    return (
      <Card className={isConfigured ? 'border-green-200 bg-green-50' : ''}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              {platformInfo.icon} {platformInfo.displayName}
            </span>
            {isConfigured ? (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Configured
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                <AlertCircle className="w-3 h-3 mr-1" />
                Setup Required
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Developer Console</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(getDeveloperUrl(), '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open Console
                </Button>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Redirect URI</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={getRedirectUri()}
                  readOnly
                  className="text-xs"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(getRedirectUri(), `${platformInfo.displayName} Redirect URI`)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>

          {/* Credential Input Section */}
          {!isConfigured && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <h4 className="font-medium text-sm">Enter OAuth Credentials</h4>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label className="text-sm font-medium">Client ID</Label>
                  <Input
                    value={currentInput.clientId}
                    onChange={(e) => handleCredentialChange(platform, 'clientId', e.target.value)}
                    placeholder="Enter your client ID"
                    className="text-sm"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Client Secret</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={currentInput.clientSecret}
                      onChange={(e) => handleCredentialChange(platform, 'clientSecret', e.target.value)}
                      placeholder="Enter your client secret"
                      type={currentInput.showSecret ? "text" : "password"}
                      className="text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSecretVisibility(platform)}
                    >
                      {currentInput.showSecret ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                    </Button>
                  </div>
                </div>
                <Button
                  onClick={() => saveCredentials(platform)}
                  disabled={loading || !currentInput.clientId || !currentInput.clientSecret}
                  className="w-full"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Credentials'}
                </Button>
              </div>
            </div>
          )}

          {/* Configured Credentials Display */}
          {isConfigured && (
            <div className="space-y-4 p-4 border rounded-lg bg-green-50">
              <h4 className="font-medium text-sm text-green-800">✓ Credentials Configured</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Client ID</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toast({ title: "Secure Storage", description: "Credentials are encrypted and secure" })}
                    >
                      <Shield className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Client Secret</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toast({ title: "Secure Storage", description: "Credentials are encrypted and secure" })}
                    >
                      <Shield className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">OAuth Setup Wizard</h1>
            <p className="text-muted-foreground">Configure social media platform integrations</p>
          </div>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>

        <div className="flex items-center gap-2 mt-4">
          <Badge variant={status.configured === status.total ? "default" : "secondary"}>
            {status.configured}/{status.total} Platforms Configured
          </Badge>
          {status.configured > 0 && (
            <Badge variant="outline">
              <Zap className="w-3 h-3 mr-1" />
              Real OAuth Enabled
            </Badge>
          )}
          <Badge variant="outline">
            <Database className="w-3 h-3 mr-1" />
            Secure Database Storage
          </Badge>
        </div>
      </div>

      {/* Custom Tab Navigation */}
      <div className="border-b border-border mb-6">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'platforms', label: 'Platforms' },
            { id: 'testing', label: 'Test Credentials' },
            { id: 'security', label: 'Security' }
          ].map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "ghost"}
              className={`px-4 py-2 rounded-t-lg rounded-b-none border-b-2 ${
                activeTab === tab.id
                  ? 'border-primary bg-background text-foreground'
                  : 'border-transparent hover:border-muted-foreground/50'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        {activeTab === 'overview' && (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="w-5 h-5 mr-2" />
                  Setup Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <Globe className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                    <h3 className="font-medium">Create OAuth Apps</h3>
                    <p className="text-sm text-muted-foreground">Register apps on each platform</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <Key className="w-8 h-8 mx-auto mb-2 text-green-500" />
                    <h3 className="font-medium">Enter Credentials</h3>
                    <p className="text-sm text-muted-foreground">Securely store client IDs and secrets</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <Shield className="w-8 h-8 mx-auto mb-2 text-purple-500" />
                    <h3 className="font-medium">Test Integration</h3>
                    <p className="text-sm text-muted-foreground">Verify OAuth connections</p>
                  </div>
                </div>

                <Alert>
                  <Database className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Secure Storage:</strong> Your OAuth credentials are encrypted and stored securely in the database.
                    No need to manage environment files - just enter your credentials through the app interface.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'platforms' && (
          <div className="space-y-4">
            {platforms.map(platform => (
              <PlatformSetupCard key={platform} platform={platform} />
            ))}
          </div>
        )}

        {activeTab === 'testing' && (
          <div className="space-y-4">
            <OAuthCredentialsTest />
          </div>
        )}

        {activeTab === 'security' && (
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="w-5 h-5 mr-2" />
                  Security Features
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Database Encryption</h4>
                      <p className="text-sm text-muted-foreground">
                        All OAuth credentials are encrypted before storage using industry-standard encryption
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">User-Specific Storage</h4>
                      <p className="text-sm text-muted-foreground">
                        Credentials are isolated per user and workspace for maximum security
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">HTTPS in Production</h4>
                      <p className="text-sm text-muted-foreground">
                        Always use HTTPS for OAuth redirects in production
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium">No Environment Files</h4>
                      <p className="text-sm text-muted-foreground">
                        No need to manage .env files - credentials are managed through the secure UI
                      </p>
                    </div>
                  </div>
                </div>

                <Alert>
                  <Shield className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Production Ready:</strong> This secure credential management system is designed for production use
                    with enterprise-grade security features.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default OAuthSetupWizard;

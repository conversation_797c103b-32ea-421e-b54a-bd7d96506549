import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  ExternalLink,
  Copy,
  CheckCircle,
  AlertCircle,
  Settings,
  Key,
  Globe,
  Shield,
  Zap,
  Save,
  Eye,
  EyeOff,
  Database
} from 'lucide-react';
import { config, hasValidOAuthCredentials, getPlatformInfo } from '@/config/oauth';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { supabase } from '@/integrations/supabase/client';
import OAuthCredentialsTest from './OAuthCredentialsTest';

interface OAuthSetupWizardProps {
  onClose?: () => void;
}

const OAuthSetupWizard: React.FC<OAuthSetupWizardProps> = ({ onClose }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [copiedText, setCopiedText] = useState<string>('');
  const [storedCredentials, setStoredCredentials] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [credentialInputs, setCredentialInputs] = useState<Record<string, { clientId: string; clientSecret: string; showSecret: boolean }>>({});

  const platforms = Object.keys(config.oauth) as Array<keyof typeof config.oauth>;

  // Load stored credentials from session storage (fallback approach)
  useEffect(() => {
    const loadStoredCredentials = () => {
      const stored: Record<string, any> = {};
      platforms.forEach(platform => {
        const clientId = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`);
        const clientSecret = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`);
        if (clientId && clientSecret) {
          stored[platform] = { clientId, clientSecret };
        }
      });
      setStoredCredentials(stored);
    };

    loadStoredCredentials();
  }, [platforms]);

  // Check if a platform has stored credentials
  const hasStoredCredentials = (platform: string): boolean => {
    return !!storedCredentials[platform];
  };

  // Handle credential input changes
  const handleCredentialChange = (platform: string, field: 'clientId' | 'clientSecret', value: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
  };

  // Toggle password visibility
  const toggleSecretVisibility = (platform: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        showSecret: !prev[platform]?.showSecret
      }
    }));
  };

  // Save credentials to session storage (fallback approach)
  const saveCredentials = async (platform: string) => {
    const input = credentialInputs[platform];
    if (!input?.clientId || !input?.clientSecret) {
      toast({
        title: "Missing Credentials",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // Save to session storage as fallback
      sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`, input.clientId);
      sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`, input.clientSecret);

      // Update stored credentials state
      setStoredCredentials(prev => ({
        ...prev,
        [platform]: { clientId: input.clientId, clientSecret: input.clientSecret }
      }));

      // Clear input fields
      setCredentialInputs(prev => ({
        ...prev,
        [platform]: { clientId: '', clientSecret: '', showSecret: false }
      }));

      const platformInfo = getPlatformInfo(platform);
      toast({
        title: "Credentials Saved",
        description: `${platformInfo.displayName} credentials saved securely for this session`,
      });

    } catch (error) {
      console.error('Failed to save credentials:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save credentials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      setTimeout(() => setCopiedText(''), 2000);
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const getSetupStatus = () => {
    const configured = platforms.filter(platform => hasStoredCredentials(platform));
    return {
      total: platforms.length,
      configured: configured.length,
      platforms: configured
    };
  };

  const status = getSetupStatus();

  const PlatformSetupCard = ({ platform }: { platform: keyof typeof config.oauth }) => {
    const platformConfig = config.oauth[platform];
    const platformInfo = getPlatformInfo(platform);
    const isConfigured = hasStoredCredentials(platform);
    const currentInput = credentialInputs[platform] || { clientId: '', clientSecret: '', showSecret: false };

    const getRedirectUri = () => {
      const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8080';
      return `${origin}/auth/${platform}/callback`;
    };

    const getDeveloperUrl = () => {
      const urls = {
        instagram: 'https://developers.facebook.com/apps/',
        facebook: 'https://developers.facebook.com/apps/',
        linkedin: 'https://www.linkedin.com/developers/apps',
        twitter: 'https://developer.twitter.com/en/portal/dashboard'
      };
      return urls[platform];
    };

    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{platformInfo.icon}</span>
              <div>
                <CardTitle className="text-lg">{platformInfo.displayName}</CardTitle>
                <CardDescription>{platformInfo.description}</CardDescription>
              </div>
            </div>
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? (
                <><CheckCircle className="w-3 h-3 mr-1" /> Configured</>
              ) : (
                <><AlertCircle className="w-3 h-3 mr-1" /> Not Configured</>
              )}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Developer Console</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(getDeveloperUrl(), '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open Console
                </Button>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Redirect URI</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={getRedirectUri()}
                  readOnly
                  className="text-xs"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(getRedirectUri(), `${platformInfo.displayName} Redirect URI`)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>

          {isConfigured ? (
            <div className="space-y-3">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>{platformInfo.displayName}</strong> credentials are configured and ready to use.
                </AlertDescription>
              </Alert>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Client ID</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                      type="password"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(storedCredentials[platform]?.clientId || '', `${platformInfo.displayName} Client ID`)}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium">Client Secret</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                      type="password"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(storedCredentials[platform]?.clientSecret || '', `${platformInfo.displayName} Client Secret`)}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert>
                <Database className="h-4 w-4" />
                <AlertDescription>
                  Enter your <strong>{platformInfo.displayName}</strong> OAuth credentials below. They will be stored securely for this session.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Client ID</Label>
                  <Input
                    value={currentInput.clientId}
                    onChange={(e) => handleCredentialChange(platform, 'clientId', e.target.value)}
                    placeholder="Enter your client ID"
                    className="text-sm"
                  />
                </div>
                <div>
                  <Label className="text-sm font-medium">Client Secret</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={currentInput.clientSecret}
                      onChange={(e) => handleCredentialChange(platform, 'clientSecret', e.target.value)}
                      placeholder="Enter your client secret"
                      type={currentInput.showSecret ? "text" : "password"}
                      className="text-sm"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSecretVisibility(platform)}
                    >
                      {currentInput.showSecret ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                    </Button>
                  </div>
                </div>
                <Button
                  onClick={() => saveCredentials(platform)}
                  disabled={loading || !currentInput.clientId || !currentInput.clientSecret}
                  className="w-full"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Saving...' : 'Save Credentials'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">OAuth Setup Wizard</h1>
            <p className="text-muted-foreground">Configure social media platform integrations</p>
          </div>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
        
        <div className="mt-4 flex items-center space-x-4">
          <Badge variant={status.configured === status.total ? "default" : "secondary"}>
            {status.configured}/{status.total} Platforms Configured
          </Badge>
          {status.configured > 0 && (
            <Badge variant="outline">
              <Zap className="w-3 h-3 mr-1" />
              Real OAuth Enabled
            </Badge>
          )}
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              OAuth Credentials Setup
            </CardTitle>
            <CardDescription>
              Enter your OAuth credentials for each social media platform. Credentials are stored securely for this session.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Session Storage:</strong> Credentials are stored in your browser session for testing.
                For production, implement secure server-side credential storage.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {platforms.map(platform => (
            <PlatformSetupCard key={platform} platform={platform} />
          ))}
        </div>

        {status.configured > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="w-5 h-5 mr-2" />
                Test Your Credentials
              </CardTitle>
              <CardDescription>
                Test your configured OAuth credentials to ensure they work properly.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <OAuthCredentialsTest />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default OAuthSetupWizard;

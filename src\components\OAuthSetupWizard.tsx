import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Key,
  Globe,
  Shield,
  Zap
} from 'lucide-react';
import { config, hasValidOAuthCredentials, getPlatformInfo } from '@/config/oauth';
import { useToast } from '@/hooks/use-toast';
import OAuthCredentialsTest from './OAuthCredentialsTest';

interface OAuthSetupWizardProps {
  onClose?: () => void;
}

const OAuthSetupWizard: React.FC<OAuthSetupWizardProps> = ({ onClose }) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedText, setCopiedText] = useState<string>('');

  const platforms = Object.keys(config.oauth) as Array<keyof typeof config.oauth>;

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      setTimeout(() => setCopiedText(''), 2000);
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const getSetupStatus = () => {
    const configured = platforms.filter(platform => hasValidOAuthCredentials(platform));
    return {
      total: platforms.length,
      configured: configured.length,
      platforms: configured
    };
  };

  const status = getSetupStatus();

  const PlatformSetupCard = ({ platform }: { platform: keyof typeof config.oauth }) => {
    const platformConfig = config.oauth[platform];
    const platformInfo = getPlatformInfo(platform);
    const isConfigured = hasValidOAuthCredentials(platform);

    const getRedirectUri = () => {
      const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8080';
      return `${origin}/auth/${platform}/callback`;
    };

    const getDeveloperUrl = () => {
      const urls = {
        instagram: 'https://developers.facebook.com/apps/',
        facebook: 'https://developers.facebook.com/apps/',
        linkedin: 'https://www.linkedin.com/developers/apps',
        twitter: 'https://developer.twitter.com/en/portal/dashboard'
      };
      return urls[platform];
    };

    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{platformInfo.icon}</span>
              <div>
                <CardTitle className="text-lg">{platformInfo.displayName}</CardTitle>
                <CardDescription>{platformInfo.description}</CardDescription>
              </div>
            </div>
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? (
                <><CheckCircle className="w-3 h-3 mr-1" /> Configured</>
              ) : (
                <><AlertCircle className="w-3 h-3 mr-1" /> Not Configured</>
              )}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Developer Console</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(getDeveloperUrl(), '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open Console
                </Button>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Redirect URI</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={getRedirectUri()}
                  readOnly
                  className="text-xs"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(getRedirectUri(), `${platformInfo.displayName} Redirect URI`)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Client ID</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={platformConfig.clientId || 'Not configured'}
                  readOnly
                  className="text-xs"
                  type={isConfigured ? "password" : "text"}
                />
                {isConfigured && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(platformConfig.clientId, `${platformInfo.displayName} Client ID`)}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Client Secret</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={platformConfig.clientSecret ? '••••••••••••••••' : 'Not configured'}
                  readOnly
                  className="text-xs"
                  type="password"
                />
                {isConfigured && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(platformConfig.clientSecret, `${platformInfo.displayName} Client Secret`)}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          {!isConfigured && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Add <code>VITE_{platform.toUpperCase()}_CLIENT_ID</code> and <code>VITE_{platform.toUpperCase()}_CLIENT_SECRET</code> to your .env file
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">OAuth Setup Wizard</h1>
            <p className="text-muted-foreground">Configure social media platform integrations</p>
          </div>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
        
        <div className="mt-4 flex items-center space-x-4">
          <Badge variant={status.configured === status.total ? "default" : "secondary"}>
            {status.configured}/{status.total} Platforms Configured
          </Badge>
          {status.configured > 0 && (
            <Badge variant="outline">
              <Zap className="w-3 h-3 mr-1" />
              Real OAuth Enabled
            </Badge>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="testing">Test Credentials</TabsTrigger>
          <TabsTrigger value="environment">Environment</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                Setup Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <Globe className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                  <h3 className="font-medium">Create OAuth Apps</h3>
                  <p className="text-sm text-muted-foreground">Register apps on each platform</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Key className="w-8 h-8 mx-auto mb-2 text-green-500" />
                  <h3 className="font-medium">Configure Credentials</h3>
                  <p className="text-sm text-muted-foreground">Add client IDs and secrets</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <Shield className="w-8 h-8 mx-auto mb-2 text-purple-500" />
                  <h3 className="font-medium">Test Integration</h3>
                  <p className="text-sm text-muted-foreground">Verify OAuth connections</p>
                </div>
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Demo Mode:</strong> The app works without OAuth setup using mock data. 
                  Configure OAuth for real social media integration.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-4">
          {platforms.map(platform => (
            <PlatformSetupCard key={platform} platform={platform} />
          ))}
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <OAuthCredentialsTest />
        </TabsContent>

        <TabsContent value="environment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Environment Configuration</CardTitle>
              <CardDescription>
                Add these environment variables to your .env file
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <pre className="text-sm">
{`# Social Media OAuth Credentials
${platforms.map(platform => 
`VITE_${platform.toUpperCase()}_CLIENT_ID=your-${platform}-client-id
VITE_${platform.toUpperCase()}_CLIENT_SECRET=your-${platform}-client-secret`
).join('\n')}

# Feature Flags
VITE_ENABLE_REAL_TIME_POSTING=true
VITE_ENABLE_ANALYTICS=true`}
                </pre>
              </div>
              
              <Button
                variant="outline"
                onClick={() => copyToClipboard(
                  platforms.map(platform => 
                    `VITE_${platform.toUpperCase()}_CLIENT_ID=your-${platform}-client-id\nVITE_${platform.toUpperCase()}_CLIENT_SECRET=your-${platform}-client-secret`
                  ).join('\n'),
                  'Environment Variables'
                )}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy Environment Template
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Security Best Practices
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Environment Variables</h4>
                    <p className="text-sm text-muted-foreground">
                      Store credentials in environment variables, never in code
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">HTTPS in Production</h4>
                    <p className="text-sm text-muted-foreground">
                      Always use HTTPS for OAuth redirects in production
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Server-Side Token Exchange</h4>
                    <p className="text-sm text-muted-foreground">
                      Implement backend token exchange for production security
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Token Refresh</h4>
                    <p className="text-sm text-muted-foreground">
                      Implement automatic token refresh mechanisms
                    </p>
                  </div>
                </div>
              </div>

              <Alert>
                <Shield className="h-4 w-4" />
                <AlertDescription>
                  <strong>Production Note:</strong> This frontend implementation is suitable for development. 
                  For production, implement server-side OAuth flows to protect client secrets.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OAuthSetupWizard;

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useAppStore } from '@/store/appStore';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Plus, RefreshCw, Settings, CheckCircle2, AlertCircle } from 'lucide-react';
import { getPlatformIcon } from '@/utils/socialIcons';

interface SocialAccount {
  id: string;
  platform: string;
  is_connected: boolean;
  username: string;
  display_name: string;
  followers_count: number;
  profile_url?: string;
  avatar_url?: string;
  hasCredentials: boolean;
  verification_status?: string;
}

const SocialAccounts = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { workspace } = useAppStore();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);

  // Initialize accounts data
  useEffect(() => {
    const initializeAccounts = async () => {
      setIsLoading(true);
      
      try {
        // Check for saved OAuth credentials in session storage
        const platforms = [
          { key: 'linkedin', name: 'LinkedIn', icon: '💼' },
          { key: 'instagram', name: 'Instagram', icon: '📷' },
          { key: 'facebook', name: 'Facebook', icon: '📘' },
          { key: 'twitter', name: 'Twitter/X', icon: '🐦' }
        ];

        const accountsData = platforms.map(platform => {
          const clientId = sessionStorage.getItem(`VITE_${platform.key.toUpperCase()}_CLIENT_ID`);
          const clientSecret = sessionStorage.getItem(`VITE_${platform.key.toUpperCase()}_CLIENT_SECRET`);
          const hasCredentials = !!(clientId && clientSecret);
          
          return {
            id: platform.key,
            platform: platform.name,
            is_connected: hasCredentials && platform.key === 'linkedin', // LinkedIn connected if credentials exist
            username: hasCredentials ? `@${platform.name.toLowerCase()}-user` : '',
            display_name: hasCredentials ? `${platform.name} Account` : '',
            followers_count: hasCredentials ? Math.floor(Math.random() * 10000) : 0,
            profile_url: hasCredentials ? `https://${platform.key}.com/profile` : '',
            avatar_url: '',
            hasCredentials,
            verification_status: hasCredentials ? 'verified' : 'pending'
          };
        });

        setAccounts(accountsData);
      } catch (error) {
        console.error('Failed to initialize accounts:', error);
        toast({
          title: "Initialization Failed",
          description: "Failed to load social accounts",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializeAccounts();
  }, [workspace?.id, toast]);

  const handleConnectAccount = async (platform: string) => {
    setIsConnecting(platform);
    
    try {
      const account = accounts.find(acc => acc.platform === platform);
      
      if (!account?.hasCredentials) {
        toast({
          title: "Setup Required",
          description: `Please set up OAuth credentials for ${platform} first`,
          variant: "destructive",
        });
        // Open OAuth setup page
        window.open('/oauth-setup', '_blank');
        return;
      }

      // Simulate OAuth flow
      toast({
        title: "Starting OAuth Flow",
        description: `Opening ${platform} authorization window...`,
      });

      // For LinkedIn, open real OAuth URL
      if (platform === 'LinkedIn') {
        const clientId = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_ID');
        if (clientId) {
          const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent('http://localhost:8080/auth/linkedin/callback')}&scope=openid%20profile%20email%20w_member_social&state=${Math.random().toString(36)}`;
          
          const popup = window.open(authUrl, 'oauth', 'width=600,height=600');
          
          // Monitor popup
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              // Update account status
              setAccounts(prev => prev.map(acc => 
                acc.platform === platform 
                  ? { ...acc, is_connected: true, verification_status: 'verified' }
                  : acc
              ));
              
              toast({
                title: "OAuth Connection Successful!",
                description: `Successfully connected your ${platform} account`,
              });
            }
          }, 1000);
        }
      } else {
        // For other platforms, simulate connection
        setTimeout(() => {
          setAccounts(prev => prev.map(acc => 
            acc.platform === platform 
              ? { ...acc, is_connected: true, verification_status: 'verified' }
              : acc
          ));
          
          toast({
            title: "Account Connected",
            description: `Successfully connected your ${platform} account`,
          });
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to connect account:', error);
      toast({
        title: "Connection Failed",
        description: `Failed to connect ${platform} account`,
        variant: "destructive",
      });
    } finally {
      setIsConnecting(null);
    }
  };

  const handleRefreshAccount = async (platform: string) => {
    toast({
      title: "Refreshing Account",
      description: `Updating ${platform} account information...`,
    });

    // Simulate refresh
    setTimeout(() => {
      toast({
        title: "Account Refreshed",
        description: `${platform} account information updated successfully`,
      });
    }, 1000);
  };

  const handleAccountSettings = (platform: string) => {
    // Open OAuth setup page
    window.open('/oauth-setup', '_blank');
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading Social Accounts...</p>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  if (!user) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
                  <p className="text-muted-foreground">Please log in to manage your social accounts.</p>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  const connectedAccounts = accounts.filter(account => account.is_connected);
  const availableAccounts = accounts.filter(account => !account.is_connected);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
                <h1 className="text-2xl font-bold mb-2">Social Media Accounts</h1>
                <p className="text-blue-100">
                  Connect and manage your social media accounts for seamless content publishing
                </p>
              </div>

              {/* Connected Accounts */}
              {connectedAccounts.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle2 className="h-5 w-5 text-green-600" />
                      Connected Accounts ({connectedAccounts.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                      {connectedAccounts.map((account) => (
                        <div key={account.id} className="border rounded-lg p-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="relative">
                                {getPlatformIcon(account.platform, 'lg')}
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                  <CheckCircle2 className="h-2 w-2 text-white" />
                                </div>
                              </div>
                              <div>
                                <p className="font-medium">{account.platform}</p>
                                <p className="text-sm text-muted-foreground">{account.username}</p>
                              </div>
                            </div>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              Connected
                            </Badge>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRefreshAccount(account.platform)}
                              className="flex-1"
                            >
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Refresh
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAccountSettings(account.platform)}
                              className="flex-1"
                            >
                              <Settings className="h-4 w-4 mr-2" />
                              Settings
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Available Platforms */}
              <Card>
                <CardHeader>
                  <CardTitle>Available Platforms</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {availableAccounts.map((account) => (
                      <div key={account.platform} className="border rounded-lg p-4 text-center space-y-3">
                        <div className="flex justify-center">{getPlatformIcon(account.platform, 'lg')}</div>
                        <div>
                          <p className="font-medium">{account.platform}</p>
                          <div className="flex items-center justify-center gap-2 mb-1">
                            <p className="text-sm text-muted-foreground">Not connected</p>
                            {account.hasCredentials ? (
                              <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                                OAuth Ready
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                                Setup Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            className="w-full"
                            onClick={() => handleConnectAccount(account.platform)}
                            disabled={isConnecting === account.platform}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            {isConnecting === account.platform ? 'Connecting...' : 'Connect'}
                          </Button>
                          {!account.hasCredentials && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="w-full text-xs"
                              onClick={() => window.open('/oauth-setup', '_blank')}
                            >
                              Setup OAuth
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default SocialAccounts;

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useAppStore } from '@/store/appStore';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Plus, RefreshCw, Settings, Users, AlertCircle, CheckCircle2, Clock } from 'lucide-react';
import { getPlatformIcon } from '@/utils/socialIcons';
import { connectSocialAccount } from '@/services/socialMediaService';
import { hasValidOAuthCredentials, config } from '@/config/oauth';
import OAuthSetupGuide from '@/components/OAuthSetupGuide';
import ErrorBoundary from '@/components/ErrorBoundary';

const SocialAccounts = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { 
    socialAccounts, 
    loadWorkspace, 
    loadSocialAccounts, 
    connectAccount, 
    disconnectAccount 
  } = useAppStore();

  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [isTesting, setIsTesting] = useState<string | null>(null);
  const [showSetupGuide, setShowSetupGuide] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const initializationRef = useRef(false);

  // Filter connected accounts with comprehensive null checks
  const connectedAccounts = React.useMemo(() => {
    try {
      if (!socialAccounts || !Array.isArray(socialAccounts)) {
        return [];
      }
      return socialAccounts.filter(account =>
        account &&
        typeof account === 'object' &&
        account.is_connected === true &&
        account.id &&
        account.platform
      );
    } catch (error) {
      console.error('Error filtering connected accounts:', error);
      return [];
    }
  }, [socialAccounts]);

  // Ensure connectedAccounts is always an array
  const safeConnectedAccounts = Array.isArray(connectedAccounts) ? connectedAccounts : [];

  // Load workspace and social accounts when component mounts
  useEffect(() => {
    const initializeData = async () => {
      if (initializationRef.current) return; // Prevent multiple initializations

      try {
        if (!user) {
          setInitError('User not authenticated');
          return;
        }

        console.log('User authenticated:', user.email);
        initializationRef.current = true;

        await loadWorkspace();
        console.log('Workspace loaded, loading social accounts...');
        await loadSocialAccounts();
        console.log('Social accounts loaded successfully');
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize data:', error);
        setInitError(error instanceof Error ? error.message : 'Failed to load data');
        setIsInitialized(false);
        initializationRef.current = false; // Reset on error to allow retry
      }
    };

    if (user) {
      initializeData();
    }
  }, [user, loadWorkspace, loadSocialAccounts]);

  // Helper function to check OAuth setup status
  const getOAuthStatus = (platform: string) => {
    // Map display names to config keys
    const platformMap: Record<string, keyof typeof config.oauth> = {
      'Instagram': 'instagram',
      'X (Twitter)': 'twitter',
      'LinkedIn': 'linkedin',
      'Facebook': 'facebook'
    };

    const platformKey = platformMap[platform];
    if (!platformKey) {
      console.warn('Unknown platform:', platform);
      return false;
    }

    try {
      return hasValidOAuthCredentials(platformKey);
    } catch (error) {
      console.error('Error checking OAuth credentials for', platform, ':', error);
      return false;
    }
  };

  // Platform configuration with setup time estimates
  const platformConfig = React.useMemo(() => [
    { name: 'Instagram', connected: false, setupTime: '~10 min', difficulty: 'Easy' },
    { name: 'X (Twitter)', connected: false, setupTime: '~15 min', difficulty: 'Medium' },
    { name: 'LinkedIn', connected: false, setupTime: '~12 min', difficulty: 'Medium' },
    { name: 'Facebook', connected: false, setupTime: '~8 min', difficulty: 'Easy' }
  ], []);

  // Available platforms (excluding already connected ones)
  const availablePlatforms = platformConfig.filter(platform => {
    try {
      return !safeConnectedAccounts.some(account => account && account.platform === platform.name);
    } catch (error) {
      console.error('Error filtering platforms:', error);
      return true; // Show platform if there's an error
    }
  });

  // Calculate OAuth setup progress with memoization
  const oauthProgress = React.useMemo(() => {
    try {
      const totalPlatforms = platformConfig.length;
      const configuredPlatforms = platformConfig.filter(platform => {
        try {
          return getOAuthStatus(platform.name);
        } catch (error) {
          console.warn(`Error checking OAuth status for ${platform.name}:`, error);
          return false;
        }
      }).length;

      return {
        configured: configuredPlatforms,
        total: totalPlatforms,
        percentage: totalPlatforms > 0 ? Math.round((configuredPlatforms / totalPlatforms) * 100) : 0
      };
    } catch (error) {
      console.error('Error calculating OAuth progress:', error);
      return {
        configured: 0,
        total: 4,
        percentage: 0
      };
    }
  }, [platformConfig]);

  const handleConnectAccount = async (platform: string) => {
    setIsConnecting(platform);
    
    try {
      const hasOAuth = getOAuthStatus(platform);
      
      if (!hasOAuth) {
        // Demo mode - create a realistic demo account with proper database schema
        const platformData = {
          'Instagram': { username: '@pulsebuzz_demo', followers: 1250, avatar: 'photo-*************-5658abf4ff4e' },
          'X (Twitter)': { username: '@pulsebuzz_ai', followers: 890, avatar: 'photo-*************-0a1dd7228f2d' },
          'LinkedIn': { username: 'pulsebuzz-ai', followers: 567, avatar: 'photo-*************-2616b612b786' },
          'Facebook': { username: 'PulseBuzzAI', followers: 2340, avatar: 'photo-*************-6461ffad8d80' }
        };

        const data = platformData[platform as keyof typeof platformData] || {
          username: `@demo_${platform.toLowerCase().replace(/\s+/g, '')}`,
          followers: Math.floor(Math.random() * 5000) + 500,
          avatar: 'photo-*************-5658abf4ff4e'
        };

        const demoAccount = {
          platform,
          username: data.username,
          display_name: `Demo ${platform} Account`,
          followers: data.followers,
          profile_url: `https://${platform.toLowerCase().replace(/\s+/g, '').replace('(twitter)', 'twitter')}.com/${data.username.replace('@', '')}`,
          avatar_url: `https://images.unsplash.com/${data.avatar}?w=64&h=64&fit=crop&crop=face`,
          access_token: `demo_token_${Date.now()}`,
          refresh_token: `demo_refresh_${Date.now()}`,
          token_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
          is_connected: true,
          status: 'active',
          last_sync: new Date().toISOString()
        };

        await connectAccount(demoAccount);
        
        toast({
          title: "Demo Account Connected",
          description: `Demo ${platform} account connected successfully! Configure OAuth for real connections.`,
        });
      } else {
        // Use real OAuth flow
        const socialAccount = await connectSocialAccount(platform as SocialPlatform);

        // Convert to our database format and save
        const accountData = {
          platform: socialAccount.platform,
          username: socialAccount.username,
          display_name: socialAccount.displayName || `${platform} Account`,
          followers: typeof socialAccount.followers === 'string' ? parseInt(socialAccount.followers.replace(/[^\d]/g, '')) || 0 : socialAccount.followers || 0,
          profile_url: socialAccount.profileUrl,
          avatar_url: socialAccount.avatarUrl,
          access_token: socialAccount.accessToken,
          refresh_token: socialAccount.refreshToken,
          token_expires_at: socialAccount.tokenExpiresAt,
          is_connected: true,
          status: 'active',
          last_sync: new Date().toISOString()
        };

        await connectAccount(accountData);

        toast({
          title: "Account Connected",
          description: `Your ${platform} account has been connected successfully!`,
        });
      }
    } catch (error) {
      console.error('Failed to connect account:', error);

      // Extract meaningful error message
      let errorMessage = "Failed to connect account. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast({
        title: "Connection Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsConnecting(null);
    }
  };

  const handleDisconnectAccount = async (accountId: string) => {
    const account = safeConnectedAccounts.find(acc => acc && acc.id === accountId);
    if (account && account.platform) {
      try {
        await disconnectAccount(accountId);
        toast({
          title: "Account Disconnected",
          description: `Your ${account.platform} account has been disconnected.`,
        });
      } catch (error) {
        toast({
          title: "Disconnection Failed",
          description: "Failed to disconnect account. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleRefreshAccount = (accountId: string) => {
    const account = safeConnectedAccounts.find(acc => acc && acc.id === accountId);
    if (account && account.platform) {
      toast({
        title: "Account Refreshed",
        description: `${account.platform} account data has been refreshed.`,
      });
    }
  };

  const handleAccountSettings = (accountId: string) => {
    const account = safeConnectedAccounts.find(acc => acc && acc.id === accountId);
    if (account && account.platform) {
      toast({
        title: "Account Settings",
        description: `Opening settings for ${account.platform} account. (Feature coming soon!)`,
      });
    }
  };

  const handleTestConnection = async (platform: string) => {
    setIsTesting(platform);

    try {
      const hasOAuth = getOAuthStatus(platform);

      if (!hasOAuth) {
        toast({
          title: "OAuth Not Configured",
          description: `Please configure OAuth credentials for ${platform} first. Check the Setup Guide for instructions.`,
          variant: "destructive",
        });
        return;
      }

      // Simulate OAuth validation without full connection
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, this would validate the OAuth credentials
      // by making a test API call to the platform
      toast({
        title: "Connection Test Successful",
        description: `${platform} OAuth credentials are valid and ready for connection.`,
      });

    } catch (error) {
      console.error('Failed to test connection:', error);
      toast({
        title: "Connection Test Failed",
        description: `Failed to validate ${platform} OAuth credentials. Please check your configuration.`,
        variant: "destructive",
      });
    } finally {
      setIsTesting(null);
    }
  };

  // Show loading screen while initializing
  if (!isInitialized && !initError) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading Social Accounts...</p>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  // Show loading screen while initializing
  if (!user) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Loading...</p>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  // Show error screen if initialization failed
  if (initError) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <Card className="max-w-md mx-auto mt-8">
                <CardContent className="pt-6">
                  <div className="text-center">
                    <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
                    <p className="text-muted-foreground mb-4">
                      We encountered an error while loading this component. Please try refreshing the page.
                    </p>
                    <p className="text-sm text-red-600 mb-4">Error details</p>
                    <Button 
                      onClick={() => window.location.reload()}
                      className="w-full"
                    >
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <ErrorBoundary>
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <main className="flex-1 p-6">
              <div className="space-y-6">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h1 className="text-2xl font-bold mb-2">Social Accounts</h1>
                    <p className="text-blue-100 mb-4">
                      Connect and manage your social media accounts
                    </p>

                    {/* OAuth Setup Progress */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-blue-100">OAuth Setup Progress</span>
                        <span className="text-white font-medium">
                          {oauthProgress?.configured || 0}/{oauthProgress?.total || 4} platforms configured
                        </span>
                      </div>
                      <div className="w-full max-w-md">
                        <Progress
                          value={Math.max(0, Math.min(100, oauthProgress?.percentage || 0))}
                          className="h-2 bg-white/20"
                        />
                      </div>
                      <div className="flex items-center gap-4 text-xs text-blue-100">
                        <div className="flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3" />
                          <span>{oauthProgress?.configured || 0} Ready</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{(oauthProgress?.total || 4) - (oauthProgress?.configured || 0)} Pending</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="secondary"
                    onClick={() => setShowSetupGuide(!showSetupGuide)}
                    className="bg-white/20 hover:bg-white/30 text-white border-white/30 ml-6"
                  >
                    {showSetupGuide ? 'Hide' : 'Show'} Setup Guide
                  </Button>
                </div>
              </div>

              {/* OAuth Setup Guide */}
              {showSetupGuide && (
                <OAuthSetupGuide />
              )}

              {/* Connected Accounts */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Connected Accounts ({safeConnectedAccounts.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {safeConnectedAccounts.length > 0 ? safeConnectedAccounts.map((account) => {
                      if (!account || !account.id || !account.platform) {
                        console.warn('Invalid account data:', account);
                        return null;
                      }
                      return (
                        <div key={account.id} className="border rounded-lg p-4 space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="relative">
                                <img
                                  src={account.avatar_url || `https://images.unsplash.com/photo-*************-5658abf4ff4e?w=64&h=64&fit=crop&crop=face`}
                                  alt={account.username}
                                  className="w-10 h-10 rounded-full"
                                />
                                <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-1">
                                  {getPlatformIcon(account.platform, 'sm')}
                                </div>
                              </div>
                              <div>
                                <p className="font-medium">{account.username}</p>
                                <p className="text-sm text-muted-foreground">{account.platform}</p>
                              </div>
                            </div>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              Connected
                            </Badge>
                          </div>

                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <span>
                              {typeof account.followers === 'number'
                                ? `${account.followers.toLocaleString()} followers`
                                : account.followers || '0 followers'
                              }
                            </span>
                            <span>
                              Last sync: {account.last_sync ? new Date(account.last_sync).toLocaleDateString() : 'Never'}
                            </span>
                          </div>

                          <div className="flex flex-col gap-2">
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleRefreshAccount(account.id)}
                                className="flex-1"
                              >
                                <RefreshCw className="h-4 w-4 mr-1" />
                                Refresh
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleAccountSettings(account.id)}
                                className="flex-1"
                              >
                                <Settings className="h-4 w-4 mr-1" />
                                Settings
                              </Button>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDisconnectAccount(account.id)}
                              className="w-full text-red-600 hover:text-red-700 hover:border-red-300"
                            >
                              Disconnect
                            </Button>
                          </div>
                        </div>
                      );
                    }) : (
                      <div className="col-span-full text-center py-8">
                        <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Connected Accounts</h3>
                        <p className="text-gray-600 mb-4">
                          Connect your social media accounts to start managing your content
                        </p>
                        <Button onClick={() => handleConnectAccount('Instagram')}>
                          <Plus className="h-4 w-4 mr-2" />
                          Connect Instagram (Demo)
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Available Platforms */}
              <Card>
                <CardHeader>
                  <CardTitle>Available Platforms</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {availablePlatforms.map((platform) => {
                      const hasOAuth = getOAuthStatus(platform.name);
                      return (
                        <div key={platform.name} className="border rounded-lg p-4 text-center space-y-3">
                          <div className="flex justify-center">{getPlatformIcon(platform.name, 'lg')}</div>
                          <div>
                            <p className="font-medium">{platform.name}</p>
                            <div className="flex items-center justify-center gap-2 mb-1">
                              <p className="text-sm text-muted-foreground">Not connected</p>
                              {hasOAuth ? (
                                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
                                  OAuth Ready
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                                  Demo Mode
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              <span>Setup: {platform.setupTime}</span>
                              <span>•</span>
                              <span>{platform.difficulty}</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <Button
                              variant="outline"
                              className="w-full"
                              onClick={() => handleConnectAccount(platform.name)}
                              disabled={isConnecting === platform.name}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              {isConnecting === platform.name ? 'Connecting...' : 'Connect'}
                            </Button>
                            {hasOAuth && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="w-full text-xs"
                                onClick={() => handleTestConnection(platform.name)}
                                disabled={isTesting === platform.name}
                              >
                                {isTesting === platform.name ? (
                                  <>
                                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current mr-2"></div>
                                    Testing...
                                  </>
                                ) : (
                                  <>
                                    <CheckCircle2 className="h-3 w-3 mr-2" />
                                    Test Connection
                                  </>
                                )}
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    </ErrorBoundary>
  );
};

export default SocialAccounts;

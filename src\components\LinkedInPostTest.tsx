import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Send, 
  User, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  RefreshCw,
  MessageSquare
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { linkedinService, type LinkedInProfile } from '@/services/linkedinService';

const LinkedInPostTest: React.FC = () => {
  const { toast } = useToast();
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [profile, setProfile] = useState<LinkedInProfile | null>(null);
  const [postText, setPostText] = useState('');
  const [isExchangingToken, setIsExchangingToken] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [manualToken, setManualToken] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');

  // Check if we have an authorization code to exchange
  useEffect(() => {
    const authCode = sessionStorage.getItem('OAUTH_CODE_LINKEDIN');
    const storedToken = sessionStorage.getItem('LINKEDIN_ACCESS_TOKEN');

    console.log('LinkedIn OAuth Check:', { authCode: !!authCode, storedToken: !!storedToken });

    if (storedToken) {
      setAccessToken(storedToken);

      // Only test connection for real tokens, not mock tokens
      if (!storedToken.startsWith('mock_token_')) {
        testConnection(storedToken);
      } else {
        // For mock tokens, set up the mock profile
        setConnectionStatus('connected');
        setProfile({
          id: 'mock_user_123',
          firstName: { localized: { 'en_US': 'Test' } },
          lastName: { localized: { 'en_US': 'User' } }
        });
      }
    } else if (authCode && !accessToken) {
      exchangeCodeForToken(authCode);
    }
  }, [accessToken]);

  const exchangeCodeForToken = async (code: string) => {
    setIsExchangingToken(true);
    console.log('Starting token exchange for code:', code.substring(0, 10) + '...');

    try {
      const redirectUri = `${window.location.origin}/auth/linkedin/callback`;
      console.log('Using redirect URI:', redirectUri);

      const tokenResponse = await linkedinService.exchangeCodeForToken(code, redirectUri);
      console.log('Token exchange successful:', { expires_in: tokenResponse.expires_in });

      setAccessToken(tokenResponse.access_token);
      sessionStorage.setItem('LINKEDIN_ACCESS_TOKEN', tokenResponse.access_token);

      // Test the connection immediately
      await testConnection(tokenResponse.access_token);

      toast({
        title: "Token Exchange Successful",
        description: "Successfully obtained LinkedIn access token",
      });
    } catch (error) {
      console.error('Token exchange error:', error);

      let errorMessage = "Failed to exchange authorization code";
      if (error instanceof Error) {
        errorMessage = error.message;

        // Check for common CORS error
        if (error.message.includes('CORS') || error.message.includes('fetch')) {
          errorMessage = "CORS Error: LinkedIn token exchange must be done server-side. This is expected for client-side apps.";
        }
      }

      toast({
        title: "Token Exchange Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsExchangingToken(false);
    }
  };

  const testConnection = async (token: string) => {
    setIsTestingConnection(true);
    try {
      // Check if this is a mock token
      if (token.startsWith('mock_token_')) {
        // For mock tokens, skip the API call and set success
        setConnectionStatus('connected');
        console.log('Mock token connection test - success');
        return;
      }

      // Real token - test actual LinkedIn API
      const result = await linkedinService.testConnection(token);

      if (result.success && result.profile) {
        setProfile(result.profile);
        setConnectionStatus('connected');
        toast({
          title: "Connection Successful",
          description: `Connected as ${result.profile.firstName.localized['en_US']} ${result.profile.lastName.localized['en_US']}`,
        });
      } else {
        setConnectionStatus('error');
        toast({
          title: "Connection Failed",
          description: result.error || "Failed to connect to LinkedIn",
          variant: "destructive",
        });
      }
    } catch (error) {
      setConnectionStatus('error');
      console.error('Connection test error:', error);
      toast({
        title: "Connection Test Failed",
        description: error instanceof Error ? error.message : "Failed to test LinkedIn connection",
        variant: "destructive",
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const startLinkedInOAuth = () => {
    const clientId = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_ID');

    if (!clientId) {
      toast({
        title: "Setup Required",
        description: "Please set up LinkedIn OAuth credentials first. Go to OAuth Setup page.",
        variant: "destructive",
      });
      // Open OAuth setup page
      window.open('/oauth-setup', '_blank');
      return;
    }

    // Generate OAuth URL
    const redirectUri = encodeURIComponent(`${window.location.origin}/auth/linkedin/callback`);
    const scope = encodeURIComponent('openid profile email w_member_social');
    const state = Math.random().toString(36).substring(2);

    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}`;

    console.log('Starting LinkedIn OAuth with URL:', authUrl);

    toast({
      title: "Starting OAuth Flow",
      description: "Opening LinkedIn authorization window...",
    });

    // Open OAuth popup
    const popup = window.open(authUrl, 'linkedin-oauth', 'width=600,height=700,scrollbars=yes,resizable=yes');

    // Monitor popup for completion
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        // Check if we got an authorization code
        setTimeout(() => {
          const authCode = sessionStorage.getItem('OAUTH_CODE_LINKEDIN');
          if (authCode) {
            toast({
              title: "Authorization Received",
              description: "Click 'Exchange for Real Token' to complete the process",
            });
          }
        }, 1000);
      }
    }, 1000);
  };

  const handlePost = async () => {
    if (!accessToken || !profile || !postText.trim()) {
      toast({
        title: "Cannot Post",
        description: "Please ensure you're connected and have entered post text",
        variant: "destructive",
      });
      return;
    }

    setIsPosting(true);
    try {
      // Check if this is a mock token
      if (accessToken.startsWith('mock_token_')) {
        // Simulate posting delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        toast({
          title: "Mock Post Successful!",
          description: "This was a test post (not published to real LinkedIn)",
        });

        console.log('Mock post content:', postText.trim());
      } else {
        // Real LinkedIn API call
        const result = await linkedinService.createPost(accessToken, postText.trim(), profile.id);

        toast({
          title: "Post Successful!",
          description: "Your LinkedIn post has been published successfully",
        });

        console.log('Post result:', result);
      }

      setPostText(''); // Clear the text area
    } catch (error) {
      console.error('Posting error:', error);
      toast({
        title: "Post Failed",
        description: error instanceof Error ? error.message : "Failed to publish LinkedIn post",
        variant: "destructive",
      });
    } finally {
      setIsPosting(false);
    }
  };

  const getConnectionStatusBadge = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Badge className="bg-green-600"><CheckCircle className="w-3 h-3 mr-1" /> Connected</Badge>;
      case 'error':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" /> Error</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const hasAuthCode = !!sessionStorage.getItem('OAUTH_CODE_LINKEDIN');
  const hasAccessToken = !!sessionStorage.getItem('LINKEDIN_ACCESS_TOKEN');

  console.log('LinkedIn Status Check:', {
    hasAuthCode,
    hasAccessToken,
    accessToken: !!accessToken,
    connectionStatus
  });

  if (!hasAuthCode && !hasAccessToken) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="w-5 h-5 mr-2" />
            LinkedIn Post Test
          </CardTitle>
          <CardDescription>
            Test posting to LinkedIn using OAuth credentials
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please complete LinkedIn OAuth authorization first. Go to the OAuth Test page and authorize LinkedIn.
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/oauth-test'}
            >
              Go to OAuth Test Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="w-5 h-5 mr-2" />
            LinkedIn Post Test
          </CardTitle>
          <CardDescription>
            Test posting to LinkedIn using OAuth credentials
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <User className="w-5 h-5 text-gray-600" />
              <div>
                <div className="font-medium">
                  {profile 
                    ? `${profile.firstName.localized['en_US']} ${profile.lastName.localized['en_US']}`
                    : 'LinkedIn Account'
                  }
                </div>
                <div className="text-sm text-gray-600">
                  {profile?.id ? `ID: ${profile.id}` : 'Not connected'}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getConnectionStatusBadge()}
              {accessToken && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => testConnection(accessToken)}
                  disabled={isTestingConnection}
                >
                  {isTestingConnection ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Token Exchange Status */}
          {isExchangingToken && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>
                Exchanging authorization code for access token...
              </AlertDescription>
            </Alert>
          )}

          {/* LinkedIn OAuth Credentials Check */}
          {!sessionStorage.getItem('VITE_LINKEDIN_CLIENT_ID') && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription>
                <div className="mb-2">
                  <strong>LinkedIn OAuth Setup Required</strong>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  To test real LinkedIn posting, you need to set up OAuth credentials first.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('/oauth-setup', '_blank')}
                >
                  📋 Setup OAuth Credentials
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* OAuth Flow Options */}
          {!accessToken && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between mb-2">
                  <span>Choose how to connect to LinkedIn:</span>
                </div>
                <div className="flex space-x-2 flex-wrap gap-2">
                  {hasAuthCode && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const authCode = sessionStorage.getItem('OAUTH_CODE_LINKEDIN');
                        if (authCode) exchangeCodeForToken(authCode);
                      }}
                      disabled={isExchangingToken}
                    >
                      {isExchangingToken ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                          Exchanging...
                        </>
                      ) : (
                        'Exchange for Real Token'
                      )}
                    </Button>
                  )}
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => startLinkedInOAuth()}
                  >
                    🔗 Start LinkedIn OAuth
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      console.log('Creating mock token...');

                      // Clear any existing tokens first
                      sessionStorage.removeItem('LINKEDIN_ACCESS_TOKEN');
                      setAccessToken(null);
                      setProfile(null);
                      setConnectionStatus('unknown');

                      // Create a mock token for testing UI
                      const mockToken = 'mock_token_' + Date.now();
                      const mockProfile = {
                        id: 'mock_user_123',
                        firstName: { localized: { 'en_US': 'Test' } },
                        lastName: { localized: { 'en_US': 'User' } }
                      };

                      setAccessToken(mockToken);
                      sessionStorage.setItem('LINKEDIN_ACCESS_TOKEN', mockToken);
                      setProfile(mockProfile);
                      setConnectionStatus('connected');

                      console.log('Mock token created:', { mockToken, mockProfile });
                      toast({
                        title: "Mock Token Created",
                        description: "Using mock token for UI testing (posts won't be real)",
                      });
                    }}
                  >
                    Use Mock Token (Testing)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      sessionStorage.removeItem('LINKEDIN_ACCESS_TOKEN');
                      sessionStorage.removeItem('OAUTH_CODE_LINKEDIN');
                      setAccessToken(null);
                      setProfile(null);
                      setConnectionStatus('unknown');
                      setManualToken('');
                      toast({
                        title: "Tokens Cleared",
                        description: "All LinkedIn tokens have been cleared",
                      });
                    }}
                  >
                    Clear All
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Manual Token Input (for CORS workaround) */}
          {!accessToken && (
            <Alert className="border-blue-200 bg-blue-50">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription>
                <div className="mb-2">
                  <strong>Manual Token Input</strong> (CORS Workaround)
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  If token exchange fails due to CORS, you can manually paste a LinkedIn access token here:
                </p>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    placeholder="Paste LinkedIn access token here..."
                    value={manualToken}
                    onChange={(e) => setManualToken(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      if (!manualToken.trim()) {
                        toast({
                          title: "Token Required",
                          description: "Please enter a LinkedIn access token",
                          variant: "destructive",
                        });
                        return;
                      }

                      setAccessToken(manualToken.trim());
                      sessionStorage.setItem('LINKEDIN_ACCESS_TOKEN', manualToken.trim());
                      await testConnection(manualToken.trim());
                      setManualToken('');
                    }}
                    disabled={!manualToken.trim()}
                  >
                    Use Token
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const guideUrl = 'https://docs.microsoft.com/en-us/linkedin/shared/authentication/authorization-code-flow';
                      window.open(guideUrl, '_blank');
                      toast({
                        title: "Token Guide Opened",
                        description: "Follow the LinkedIn OAuth guide to get an access token",
                      });
                    }}
                  >
                    📖 Token Guide
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Post Composer */}
          {connectionStatus === 'connected' && (
            <div className="space-y-4">
              <div>
                <label htmlFor="post-text" className="block text-sm font-medium mb-2">
                  Post Content
                </label>
                <Textarea
                  id="post-text"
                  value={postText}
                  onChange={(e) => setPostText(e.target.value)}
                  placeholder="What would you like to share on LinkedIn?"
                  rows={4}
                  className="resize-none"
                />
                <div className="text-xs text-gray-500 mt-1">
                  {postText.length}/3000 characters
                </div>
              </div>

              <Button
                onClick={handlePost}
                disabled={!postText.trim() || isPosting}
                className="w-full"
              >
                {isPosting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Publishing...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Publish to LinkedIn
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Debug Information */}
          <details className="text-xs">
            <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
              Debug Information
            </summary>
            <div className="mt-2 space-y-1 font-mono text-xs bg-gray-100 p-2 rounded">
              <div>Auth Code: {sessionStorage.getItem('OAUTH_CODE_LINKEDIN') ? '✅ Present' : '❌ Missing'}</div>
              <div>Access Token: {accessToken ? '✅ Present' : '❌ Missing'}</div>
              <div>Profile: {profile ? '✅ Loaded' : '❌ Not loaded'}</div>
              <div>Connection: {connectionStatus}</div>
            </div>
          </details>
        </CardContent>
      </Card>
    </div>
  );
};

export default LinkedInPostTest;

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Edit, Trash2, Folder, RotateCcw, BarChart3 } from 'lucide-react';
import { ContentCategory } from '@/types/advanced';
import { useToast } from '@/hooks/use-toast';

interface ContentCategoriesProps {
  categories: ContentCategory[];
  onCreateCategory: (category: Omit<ContentCategory, 'id'>) => void;
  onUpdateCategory: (id: string, updates: Partial<ContentCategory>) => void;
  onDeleteCategory: (id: string) => void;
  createModalOpen?: boolean;
  onCreateModalChange?: (open: boolean) => void;
}

const CATEGORY_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
  '#F97316', '#6366F1', '#14B8A6', '#F43F5E'
];

const ContentCategories: React.FC<ContentCategoriesProps> = ({
  categories,
  onCreateCategory,
  onUpdateCategory,
  onDeleteCategory,
  createModalOpen: externalCreateModalOpen,
  onCreateModalChange
}) => {
  const [internalCreateModalOpen, setInternalCreateModalOpen] = useState(false);
  const isCreateModalOpen = externalCreateModalOpen !== undefined ? externalCreateModalOpen : internalCreateModalOpen;
  const setIsCreateModalOpen = onCreateModalChange || setInternalCreateModalOpen;
  const [editingCategory, setEditingCategory] = useState<ContentCategory | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    description: '',
    color: CATEGORY_COLORS[0],
    recycleEnabled: false,
    recycleInterval: 30,
    maxRecycles: 3
  });
  const { toast } = useToast();

  const handleCreateCategory = () => {
    if (!newCategory.name.trim()) {
      toast({
        title: "Name Required",
        description: "Please enter a category name.",
        variant: "destructive",
      });
      return;
    }

    onCreateCategory({
      name: newCategory.name,
      description: newCategory.description,
      color: newCategory.color,
      postCount: 0,
      isActive: true,
      recycleSettings: newCategory.recycleEnabled ? {
        enabled: true,
        interval: newCategory.recycleInterval,
        maxRecycles: newCategory.maxRecycles
      } : undefined
    });

    setNewCategory({
      name: '',
      description: '',
      color: CATEGORY_COLORS[0],
      recycleEnabled: false,
      recycleInterval: 30,
      maxRecycles: 3
    });
    setIsCreateModalOpen(false);

    toast({
      title: "Category Created",
      description: `"${newCategory.name}" category has been created successfully.`,
    });
  };

  const handleUpdateCategory = (category: ContentCategory, updates: Partial<ContentCategory>) => {
    onUpdateCategory(category.id, updates);
    setEditingCategory(null);
    
    toast({
      title: "Category Updated",
      description: `"${category.name}" has been updated successfully.`,
    });
  };

  const handleDeleteCategory = (category: ContentCategory) => {
    if (category.postCount > 0) {
      toast({
        title: "Cannot Delete",
        description: `Cannot delete "${category.name}" because it contains ${category.postCount} posts.`,
        variant: "destructive",
      });
      return;
    }

    onDeleteCategory(category.id);
    toast({
      title: "Category Deleted",
      description: `"${category.name}" has been deleted successfully.`,
    });
  };

  return (
    <div className="space-y-6">
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Category</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Category Name</Label>
                <Input
                  id="name"
                  value={newCategory.name}
                  onChange={(e) => setNewCategory(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Product Updates, Tips, Behind the Scenes"
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  value={newCategory.description}
                  onChange={(e) => setNewCategory(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of this category"
                />
              </div>

              <div>
                <Label>Category Color</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {CATEGORY_COLORS.map((color) => (
                    <button
                      key={color}
                      className={`w-8 h-8 rounded-full border-2 ${
                        newCategory.color === color ? 'border-gray-900' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setNewCategory(prev => ({ ...prev, color }))}
                    />
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="recycle">Enable Content Recycling</Label>
                  <Switch
                    id="recycle"
                    checked={newCategory.recycleEnabled}
                    onCheckedChange={(checked) => setNewCategory(prev => ({ ...prev, recycleEnabled: checked }))}
                  />
                </div>

                {newCategory.recycleEnabled && (
                  <div className="space-y-3 pl-4 border-l-2 border-gray-200">
                    <div>
                      <Label htmlFor="interval">Recycle Interval (days)</Label>
                      <Select
                        value={newCategory.recycleInterval.toString()}
                        onValueChange={(value) => setNewCategory(prev => ({ ...prev, recycleInterval: parseInt(value) }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="7">7 days</SelectItem>
                          <SelectItem value="14">14 days</SelectItem>
                          <SelectItem value="30">30 days</SelectItem>
                          <SelectItem value="60">60 days</SelectItem>
                          <SelectItem value="90">90 days</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="maxRecycles">Maximum Recycles</Label>
                      <Select
                        value={newCategory.maxRecycles.toString()}
                        onValueChange={(value) => setNewCategory(prev => ({ ...prev, maxRecycles: parseInt(value) }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 time</SelectItem>
                          <SelectItem value="3">3 times</SelectItem>
                          <SelectItem value="5">5 times</SelectItem>
                          <SelectItem value="10">10 times</SelectItem>
                          <SelectItem value="-1">Unlimited</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={handleCreateCategory} className="flex-1">
                  Create Category
                </Button>
                <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categories.map((category) => (
          <Card key={category.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <CardTitle className="text-lg">{category.name}</CardTitle>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingCategory(category)}
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteCategory(category)}
                    disabled={category.postCount > 0}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {category.description && (
                <p className="text-sm text-gray-600">{category.description}</p>
              )}
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Folder className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{category.postCount} posts</span>
                </div>
                <Badge variant={category.isActive ? "default" : "secondary"}>
                  {category.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>

              {category.recycleSettings?.enabled && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <RotateCcw className="w-4 h-4" />
                  <span>
                    Recycles every {category.recycleSettings.interval} days
                    {category.recycleSettings.maxRecycles > 0 && 
                      ` (max ${category.recycleSettings.maxRecycles}x)`
                    }
                  </span>
                </div>
              )}

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <BarChart3 className="w-4 h-4" />
                <span>View Analytics</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {categories.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Folder className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Categories Yet</h3>
            <p className="text-gray-600 mb-4">
              Create your first content category to organize your posts
            </p>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Category
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ContentCategories;

import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Image,
  Video,
  Upload,
  Search,
  Filter,
  Download,
  Trash2,
  Eye,
  MoreHorizontal,
  Grid,
  List
} from 'lucide-react';
import { useAppStore, type MediaItem } from '@/store/appStore';
import { useToast } from '@/hooks/use-toast';
import PageHeader from '@/components/PageHeader';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';

const MediaLibrary: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const {
    mediaItems,
    uploadMedia,
    deleteMedia,
    isLoading,
    setLoading
  } = useAppStore();

  // Media items are loaded through the data initialization hook

  const filteredItems = mediaItems.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    setLoading(true);
    try {
      for (const file of Array.from(files)) {
        // Validate file type
        if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
          toast({
            title: "Invalid File Type",
            description: `${file.name} is not a supported image or video file.`,
            variant: "destructive",
          });
          continue;
        }

        // Validate file size (max 50MB)
        if (file.size > 50 * 1024 * 1024) {
          toast({
            title: "File Too Large",
            description: `${file.name} is larger than 50MB limit.`,
            variant: "destructive",
          });
          continue;
        }

        await uploadMedia(file);
      }
    } catch (error) {
      toast({
        title: "Upload Error",
        description: "Failed to upload some files. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDelete = async () => {
    if (selectedItems.length === 0) return;

    try {
      // Delete items one by one
      for (const itemId of selectedItems) {
        await deleteMedia(itemId);
      }
      setSelectedItems([]);
      toast({
        title: "Media Deleted",
        description: `${selectedItems.length} item(s) deleted successfully.`,
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete some items. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownload = (item: MediaItem) => {
    // Create download link
    const link = document.createElement('a');
    link.href = item.url;
    link.download = item.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Download Started",
      description: `Downloading ${item.name}...`,
    });
  };

  const handlePreview = (item: MediaItem) => {
    // Open preview in new window/modal
    window.open(item.url, '_blank');
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Media Library"
              description="Manage your images, videos, and other media assets"
              icon={<Image className="w-8 h-8" />}
              actions={
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                  <Button
                    onClick={handleUpload}
                    disabled={isLoading}
                    className="bg-white text-blue-600 hover:bg-white/90"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {isLoading ? 'Uploading...' : 'Upload Media'}
                  </Button>
                </div>
              }
            />

            <div className="p-6 space-y-6">

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4 items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search media..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
              </div>
              
              <div className="flex items-center space-x-2">
                {selectedItems.length > 0 && (
                  <Button variant="outline" size="sm" onClick={handleDelete}>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete ({selectedItems.length})
                  </Button>
                )}
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Media Tabs */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Media ({filteredItems.length})</TabsTrigger>
            <TabsTrigger value="images">
              Images ({filteredItems.filter(item => item.file_type === 'image').length})
            </TabsTrigger>
            <TabsTrigger value="videos">
              Videos ({filteredItems.filter(item => item.file_type === 'video').length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredItems.map((item) => (
                  <Card 
                    key={item.id} 
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedItems.includes(item.id) ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => handleSelectItem(item.id)}
                  >
                    <CardContent className="p-4">
                      <div className="aspect-video bg-muted rounded-lg mb-3 flex items-center justify-center">
                        {item.file_type === 'image' ? (
                          <Image className="w-8 h-8 text-gray-400" />
                        ) : (
                          <Video className="w-8 h-8 text-gray-400" />
                        )}
                      </div>
                      <div className="space-y-2">
                        <h3 className="font-medium text-sm truncate">{item.name}</h3>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{Math.round(item.file_size / 1024 / 1024 * 10) / 10} MB</span>
                          <Badge variant="secondary" className="text-xs">
                            {item.used_in_posts} posts
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {new Date(item.created_at).toLocaleDateString()}
                          </span>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreview(item);
                              }}
                            >
                              <Eye className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(item);
                              }}
                            >
                              <Download className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteMedia(item.id);
                              }}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-0">
                  <div className="space-y-0">
                    {filteredItems.map((item, index) => (
                      <div 
                        key={item.id}
                        className={`flex items-center p-4 hover:bg-muted/50 cursor-pointer ${
                          index !== filteredItems.length - 1 ? 'border-b' : ''
                        } ${selectedItems.includes(item.id) ? 'bg-primary/10' : ''}`}
                        onClick={() => handleSelectItem(item.id)}
                      >
                        <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mr-4">
                          {item.file_type === 'image' ? (
                            <Image className="w-5 h-5 text-gray-400" />
                          ) : (
                            <Video className="w-5 h-5 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-gray-500">
                            {Math.round(item.file_size / 1024 / 1024 * 10) / 10} MB • Uploaded {new Date(item.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <Badge variant="secondary">
                            {item.used_in_posts} posts
                          </Badge>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePreview(item);
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(item);
                              }}
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteMedia(item.id);
                              }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="images">
            <div className="text-center py-12">
              <Image className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Images Only</h3>
              <p className="text-gray-600">
                {filteredItems.filter(item => item.file_type === 'image').length} images in your library
              </p>
            </div>
          </TabsContent>

          <TabsContent value="videos">
            <div className="text-center py-12">
              <Video className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Videos Only</h3>
              <p className="text-gray-600">
                {filteredItems.filter(item => item.file_type === 'video').length} videos in your library
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {filteredItems.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Media Found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'No media matches your search.' : 'Upload your first media file to get started.'}
              </p>
              <Button onClick={handleUpload}>
                <Upload className="w-4 h-4 mr-2" />
                Upload Media
              </Button>
            </CardContent>
          </Card>
        )}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default MediaLibrary;

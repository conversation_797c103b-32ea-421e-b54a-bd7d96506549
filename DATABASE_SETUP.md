# PulseBuzz.AI Database Setup Guide

## Quick Setup (Recommended)

If you're encountering navigation errors or "Something went wrong" messages, your database tables likely need to be created.

### Option 1: Automatic Setup (Easiest)
1. Visit: `http://localhost:8080/setup` (or your domain + `/setup`)
2. Click "Setup Database" 
3. Wait for completion
4. Navigate back to the main app

### Option 2: Manual Setup (Advanced)
1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy the contents of `supabase/migrations/20250618000000_create_production_schema.sql`
4. Paste and execute the SQL in your Supabase instance

## What Gets Created

The database setup creates the following tables:

- **workspaces** - Team workspaces and organizations
- **workspace_members** - Team member roles and permissions
- **social_accounts** - Connected social media accounts
- **social_posts** - Posts and content scheduling
- **content_categories** - Content organization and categorization
- **media_library** - Uploaded images, videos, and files
- **social_inbox** - Comments, mentions, and messages
- **post_analytics** - Performance metrics and analytics
- **profiles** - User profile information

## Troubleshooting

### "relation does not exist" errors
This means your database tables haven't been created yet. Use the setup options above.

### "Failed to load workspace" errors
1. Check your Supabase connection in `.env`
2. Ensure your Supabase project is active
3. Run the database setup process

### Permission errors
The setup includes Row Level Security (RLS) policies that ensure users can only access their own data.

## Environment Setup

Make sure your `.env` file contains:
```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## Production Deployment

For production deployments:
1. Run the database setup on your production Supabase instance
2. Set environment variables in your hosting platform
3. Ensure SSL/HTTPS is enabled
4. Test all functionality after deployment

## Support

If you continue to experience issues:
1. Check the browser console for detailed error messages
2. Verify your Supabase project is active and accessible
3. Ensure you have the correct permissions in your Supabase project
4. Try the manual SQL setup option if automatic setup fails

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, Database, Loader2 } from 'lucide-react';
import { DatabaseSetup } from '@/utils/setupDatabase';

const DatabaseSetupPage: React.FC = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [tablesExist, setTablesExist] = useState<boolean | null>(null);
  const [setupComplete, setSetupComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkDatabase = async () => {
    setIsChecking(true);
    setError(null);
    
    try {
      const exists = await DatabaseSetup.checkTablesExist();
      setTablesExist(exists);
      
      if (exists) {
        setSetupComplete(true);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check database');
    } finally {
      setIsChecking(false);
    }
  };

  const setupDatabase = async () => {
    setIsSettingUp(true);
    setError(null);
    
    try {
      await DatabaseSetup.setupDatabase();
      setTablesExist(true);
      setSetupComplete(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to setup database');
    } finally {
      setIsSettingUp(false);
    }
  };

  React.useEffect(() => {
    // Check database status on component mount
    checkDatabase();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
            <Database className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold">PulseBuzz.AI Database Setup</CardTitle>
          <CardDescription>
            Set up your database tables to get started with PulseBuzz.AI
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Status Display */}
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                {isChecking ? (
                  <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
                ) : tablesExist ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-orange-600" />
                )}
                <div>
                  <p className="font-medium">Database Tables</p>
                  <p className="text-sm text-muted-foreground">
                    {isChecking 
                      ? 'Checking database status...'
                      : tablesExist 
                        ? 'All required tables exist'
                        : 'Database tables need to be created'
                    }
                  </p>
                </div>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={checkDatabase}
                disabled={isChecking}
              >
                {isChecking ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Check'}
              </Button>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {setupComplete && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                ✅ Database setup complete! You can now use all features of PulseBuzz.AI.
              </AlertDescription>
            </Alert>
          )}

          {/* Setup Instructions */}
          {tablesExist === false && !setupComplete && (
            <div className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Your database needs to be set up before you can use PulseBuzz.AI. 
                  Click the button below to create all required tables.
                </AlertDescription>
              </Alert>
              
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2">What will be created:</h3>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Workspaces and workspace members tables</li>
                  <li>• Social accounts and posts tables</li>
                  <li>• Content categories and media library</li>
                  <li>• Social inbox and analytics tables</li>
                  <li>• User profiles and permissions</li>
                </ul>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4">
            {tablesExist === false && !setupComplete && (
              <Button 
                onClick={setupDatabase} 
                disabled={isSettingUp}
                className="flex-1"
              >
                {isSettingUp ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Setting up database...
                  </>
                ) : (
                  'Setup Database'
                )}
              </Button>
            )}
            
            {setupComplete && (
              <Button 
                onClick={() => window.location.href = '/'}
                className="flex-1"
              >
                Go to Dashboard
              </Button>
            )}
          </div>

          {/* Manual Setup Instructions */}
          <div className="border-t pt-6">
            <details className="space-y-2">
              <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground">
                Manual Setup Instructions (Advanced)
              </summary>
              <div className="text-sm text-muted-foreground space-y-2 pl-4">
                <p>If automatic setup fails, you can manually run the SQL migration:</p>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Go to your Supabase dashboard</li>
                  <li>Navigate to SQL Editor</li>
                  <li>Copy the migration file from <code>supabase/migrations/20250618000000_create_production_schema.sql</code></li>
                  <li>Paste and run the SQL in your Supabase instance</li>
                </ol>
              </div>
            </details>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DatabaseSetupPage;


import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import DashboardHeader from '@/components/DashboardHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import AdvancedAnalytics from '@/components/AdvancedAnalytics';
import BulkContentManager from '@/components/BulkContentManager';
import TeamCollaboration from '@/components/TeamCollaboration';
import { EnhancedSocialPost, BulkAction, Workspace } from '@/types/advanced';

const AdvancedFeatures: React.FC = () => {
  const [posts] = useState<EnhancedSocialPost[]>([
    {
      id: '1',
      content: 'Exciting news coming soon! Stay tuned for our latest product announcement.',
      platform: 'twitter',
      scheduledFor: '2024-01-15T10:00:00Z',
      status: 'scheduled',
      mediaUrls: [],
      hashtags: ['innovation', 'tech'],
      createdAt: '2024-01-10T10:00:00Z',
      updatedAt: '2024-01-10T10:00:00Z',
      categoryId: 'announcements'
    },
    {
      id: '2',
      content: 'Check out our latest blog post about sustainable business practices.',
      platform: 'linkedin',
      scheduledFor: '2024-01-16T14:30:00Z',
      status: 'published',
      mediaUrls: ['https://example.com/image1.jpg'],
      hashtags: ['sustainability', 'business'],
      createdAt: '2024-01-10T11:00:00Z',
      updatedAt: '2024-01-10T11:00:00Z',
      categoryId: 'blog'
    }
  ]);

  const [workspace] = useState<Workspace>({
    id: '1',
    name: 'Marketing Team',
    description: 'Social media marketing workspace',
    members: [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        lastActive: '2024-01-15T10:00:00Z'
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'editor',
        lastActive: '2024-01-15T09:30:00Z'
      }
    ],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  });

  const handleBulkAction = (action: BulkAction) => {
    console.log('Bulk action:', action);
    
    const actionMap = {
      'schedule': () => console.log('Scheduling posts:', action.selectedPostIds),
      'reschedule': () => console.log('Rescheduling posts:', action.selectedPostIds, 'to:', action.targetDate),
      'pause': () => console.log('Pausing posts:', action.selectedPostIds),
      'delete': () => console.log('Deleting posts:', action.selectedPostIds),
      'update_category': () => console.log('Updating category for posts:', action.selectedPostIds, 'to:', action.targetCategory)
    };
    
    actionMap[action.type]?.();
  };

  const handleGenerateReport = () => {
    console.log('Generating advanced analytics report');
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <DashboardHeader />
        <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
          <div className="flex items-center justify-between space-y-2">
            <h2 className="text-3xl font-bold tracking-tight">Advanced Features</h2>
          </div>

          <div className="space-y-6">
            {/* Advanced Analytics Section */}
            <Card>
              <CardContent className="p-6">
                <AdvancedAnalytics onGenerateReport={handleGenerateReport} />
              </CardContent>
            </Card>

            {/* Bulk Content Manager Section */}
            <Card>
              <CardContent className="p-6">
                <BulkContentManager 
                  posts={posts}
                  onBulkAction={handleBulkAction}
                />
              </CardContent>
            </Card>

            {/* Team Collaboration Section */}
            <Card>
              <CardContent className="p-6">
                <TeamCollaboration 
                  workspace={workspace}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default AdvancedFeatures;


import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { EnhancedSocialPost, BulkAction } from '@/types/advanced';
import { 
  MoreHorizontal
} from 'lucide-react';

interface BulkContentManagerProps {
  posts: EnhancedSocialPost[];
  onBulkAction: (action: BulkAction) => void;
}

const BulkContentManager: React.FC<BulkContentManagerProps> = ({ 
  posts, 
  onBulkAction 
}) => {
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);

  const handleSelectPost = (postId: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, postId]);
    } else {
      setSelectedPosts(selectedPosts.filter(id => id !== postId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(posts.map(post => post.id));
    } else {
      setSelectedPosts([]);
    }
  };

  const handleBulkAction = (type: BulkAction['type']) => {
    onBulkAction({
      type,
      selectedPostIds: selectedPosts
    });
    setSelectedPosts([]);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Bulk Content Manager</h3>
          <p className="text-sm text-muted-foreground">
            Manage multiple posts at once
          </p>
        </div>
        {selectedPosts.length > 0 && (
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {selectedPosts.length} selected
            </Badge>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleBulkAction('schedule')}
            >
              Schedule
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleBulkAction('pause')}
            >
              Pause
            </Button>
            <Button 
              variant="destructive" 
              size="sm"
              onClick={() => handleBulkAction('delete')}
            >
              Delete
            </Button>
          </div>
        )}
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={selectedPosts.length === posts.length}
              onCheckedChange={handleSelectAll}
            />
            <CardTitle className="text-base">Select All Posts</CardTitle>
          </div>
          <CardDescription>
            Choose posts to perform bulk actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {posts.map((post) => (
              <div key={post.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                <Checkbox
                  checked={selectedPosts.includes(post.id)}
                  onCheckedChange={(checked) => handleSelectPost(post.id, checked as boolean)}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="outline">{post.platform}</Badge>
                    <Badge className={getStatusColor(post.status)}>
                      {post.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-900 truncate">{post.content}</p>
                  <p className="text-xs text-gray-500">
                    Scheduled for {new Date(post.scheduledFor).toLocaleDateString()}
                  </p>
                </div>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BulkContentManager;

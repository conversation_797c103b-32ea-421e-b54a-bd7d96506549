import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/store/appStore';
import { useToast } from '@/hooks/use-toast';
import {
  CheckCircle,
  XCircle,
  Clock,
  Play,
  Users,
  PenTool,
  Calendar
} from 'lucide-react';
import { FaInstagram, FaFacebookF, FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';

interface TestStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'success' | 'error';
  result?: string;
}

const TestSocialFlow: React.FC = () => {
  const {
    socialAccounts,
    connectAccount,
    createPost,
    workspace,
    connectedAccounts
  } = useAppStore();
  const { toast } = useToast();

  const getPlatformIcon = (platform: string) => {
    const iconProps = { size: 16, style: { display: 'inline-block' } };

    switch (platform) {
      case 'Instagram':
        return <FaInstagram {...iconProps} className="text-pink-600" />;
      case 'X (Twitter)':
        return <FaXTwitter {...iconProps} className="text-gray-900" />;
      case 'LinkedIn':
        return <FaLinkedinIn {...iconProps} className="text-blue-700" />;
      case 'Facebook':
        return <FaFacebookF {...iconProps} className="text-blue-600" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded"></div>;
    }
  };
  
  const [testSteps, setTestSteps] = useState<TestStep[]>([
    {
      id: 'connect-instagram',
      name: 'Connect Instagram Account',
      description: 'Test connecting an Instagram account in demo mode',
      status: 'pending'
    },
    {
      id: 'connect-twitter',
      name: 'Connect Twitter Account',
      description: 'Test connecting a Twitter/X account in demo mode',
      status: 'pending'
    },
    {
      id: 'verify-accounts',
      name: 'Verify Connected Accounts',
      description: 'Check that accounts are properly stored and accessible',
      status: 'pending'
    },
    {
      id: 'create-post',
      name: 'Create Test Post',
      description: 'Test creating a post with connected accounts',
      status: 'pending'
    },
    {
      id: 'schedule-post',
      name: 'Schedule Test Post',
      description: 'Test scheduling a post for future publication',
      status: 'pending'
    }
  ]);

  const updateStepStatus = (stepId: string, status: TestStep['status'], result?: string) => {
    setTestSteps(prev => prev.map(step => 
      step.id === stepId ? { ...step, status, result } : step
    ));
  };

  const runTest = async (stepId: string) => {
    updateStepStatus(stepId, 'running');
    
    try {
      switch (stepId) {
        case 'connect-instagram':
          await testConnectAccount('Instagram');
          break;
        case 'connect-twitter':
          await testConnectAccount('X (Twitter)');
          break;
        case 'verify-accounts':
          await testVerifyAccounts();
          break;
        case 'create-post':
          await testCreatePost();
          break;
        case 'schedule-post':
          await testSchedulePost();
          break;
      }
    } catch (error) {
      updateStepStatus(stepId, 'error', error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testConnectAccount = async (platform: string) => {
    const platformData = {
      'Instagram': { username: '@pulsebuzz_demo', followers: 1250, avatar: 'photo-*************-5658abf4ff4e' },
      'X (Twitter)': { username: '@pulsebuzz_ai', followers: 890, avatar: 'photo-*************-0a1dd7228f2d' }
    };

    const data = platformData[platform as keyof typeof platformData];
    if (!data) throw new Error(`No test data for ${platform}`);

    const mockAccount = {
      platform,
      username: data.username,
      display_name: `${platform} Demo Account`,
      followers: data.followers,
      profile_url: `https://${platform.toLowerCase().replace(/\s+/g, '')}.com/${data.username.replace('@', '')}`,
      avatar_url: `https://images.unsplash.com/${data.avatar}?w=64&h=64&fit=crop&crop=face`,
      access_token: `demo_token_${Date.now()}`,
      refresh_token: null,
      token_expires_at: null
    };

    await connectAccount(mockAccount);
    updateStepStatus(`connect-${platform.toLowerCase().replace(/\s+/g, '')}`, 'success', 
      `Successfully connected ${platform} account: ${data.username}`);
    
    toast({
      title: `${platform} Connected! 🎉`,
      description: `Test account ${data.username} connected successfully.`,
    });
  };

  const testVerifyAccounts = async () => {
    const accounts = connectedAccounts;
    if (accounts.length === 0) {
      throw new Error('No connected accounts found');
    }
    
    updateStepStatus('verify-accounts', 'success', 
      `Found ${accounts.length} connected accounts: ${accounts.map(a => a.platform).join(', ')}`);
  };

  const testCreatePost = async () => {
    if (connectedAccounts.length === 0) {
      throw new Error('No connected accounts available for posting');
    }

    const testPost = {
      platform: connectedAccounts[0].platform,
      content: '🧪 This is a test post created by PulseBuzz.AI! Testing the complete social media workflow. #PulseBuzzAI #SocialMediaAutomation #TestPost',
      scheduled_for: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
      status: 'draft' as const,
      category_id: null,
      media_urls: [],
      hashtags: ['PulseBuzzAI', 'SocialMediaAutomation', 'TestPost'],
      mentions: [],
      is_evergreen: false,
      recycle_count: 0,
      max_recycles: 3,
      approval_status: 'approved' as const,
      external_post_id: null,
      published_at: null
    };

    await createPost(testPost);
    updateStepStatus('create-post', 'success', 
      `Successfully created test post for ${testPost.platform}`);
    
    toast({
      title: 'Test Post Created! 📝',
      description: `Post created for ${testPost.platform}`,
    });
  };

  const testSchedulePost = async () => {
    if (connectedAccounts.length === 0) {
      throw new Error('No connected accounts available for scheduling');
    }

    const scheduledTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    const testPost = {
      platform: connectedAccounts[0].platform,
      content: '⏰ This is a scheduled test post from PulseBuzz.AI! Testing the scheduling functionality. #ScheduledPost #PulseBuzzAI',
      scheduled_for: scheduledTime.toISOString(),
      status: 'scheduled' as const,
      category_id: null,
      media_urls: [],
      hashtags: ['ScheduledPost', 'PulseBuzzAI'],
      mentions: [],
      is_evergreen: false,
      recycle_count: 0,
      max_recycles: 3,
      approval_status: 'approved' as const,
      external_post_id: null,
      published_at: null
    };

    await createPost(testPost);
    updateStepStatus('schedule-post', 'success', 
      `Successfully scheduled post for ${testPost.platform} at ${scheduledTime.toLocaleString()}`);
    
    toast({
      title: 'Post Scheduled! ⏰',
      description: `Post scheduled for ${testPost.platform} at ${scheduledTime.toLocaleString()}`,
    });
  };

  const runAllTests = async () => {
    for (const step of testSteps) {
      await runTest(step.id);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const getStatusIcon = (status: TestStep['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'running':
        return <Clock className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestStep['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">🧪 Social Media Flow Test</h1>
          <p className="text-muted-foreground">
            Test the complete social media account connection and post creation workflow
          </p>
        </div>

        {/* Current Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Current Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span>Connected Accounts:</span>
              <Badge variant="secondary">{connectedAccounts.length}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Workspace:</span>
              <Badge variant="secondary">{workspace ? 'Active' : 'None'}</Badge>
            </div>
            {connectedAccounts.length > 0 && (
              <div className="mt-4">
                <p className="text-sm font-medium mb-2">Connected Platforms:</p>
                <div className="flex flex-wrap gap-2">
                  {connectedAccounts.map((account, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      {getPlatformIcon(account.platform)}
                      {account.platform}: {account.username}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={runAllTests} className="w-full" size="lg">
              <Play className="h-4 w-4 mr-2" />
              Run All Tests
            </Button>
          </CardContent>
        </Card>

        {/* Test Steps */}
        <Card>
          <CardHeader>
            <CardTitle>Test Steps</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {testSteps.map((step) => (
              <div key={step.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(step.status)}
                    <div>
                      <h3 className="font-medium">{step.name}</h3>
                      <p className="text-sm text-muted-foreground">{step.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(step.status)}>
                      {step.status}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => runTest(step.id)}
                      disabled={step.status === 'running'}
                    >
                      <Play className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {step.result && (
                  <div className="bg-muted p-2 rounded text-sm">
                    <strong>Result:</strong> {step.result}
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TestSocialFlow;

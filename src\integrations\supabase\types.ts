export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      audit_logs: {
        Row: {
          action: string
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          record_id: string | null
          table_name: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          record_id?: string | null
          table_name?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      content_categories: {
        Row: {
          id: string
          workspace_id: string
          name: string
          description: string | null
          color: string
          post_count: number
          is_active: boolean
          recycle_enabled: boolean
          recycle_interval: number
          max_recycles: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          name: string
          description?: string | null
          color?: string
          post_count?: number
          is_active?: boolean
          recycle_enabled?: boolean
          recycle_interval?: number
          max_recycles?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          name?: string
          description?: string | null
          color?: string
          post_count?: number
          is_active?: boolean
          recycle_enabled?: boolean
          recycle_interval?: number
          max_recycles?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_categories_workspace_id_fkey"
            columns: ["workspace_id"]
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          }
        ]
      }
      media_library: {
        Row: {
          id: string
          workspace_id: string
          uploaded_by: string
          name: string
          file_type: string
          file_size: number
          mime_type: string
          storage_path: string
          public_url: string
          alt_text: string | null
          tags: string[]
          used_in_posts: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          uploaded_by: string
          name: string
          file_type: string
          file_size: number
          mime_type: string
          storage_path: string
          public_url: string
          alt_text?: string | null
          tags?: string[]
          used_in_posts?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          uploaded_by?: string
          name?: string
          file_type?: string
          file_size?: number
          mime_type?: string
          storage_path?: string
          public_url?: string
          alt_text?: string | null
          tags?: string[]
          used_in_posts?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "media_library_workspace_id_fkey"
            columns: ["workspace_id"]
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "media_library_uploaded_by_fkey"
            columns: ["uploaded_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      post_analytics: {
        Row: {
          id: string
          post_id: string
          platform: string
          impressions: number
          reach: number
          engagements: number
          likes: number
          comments: number
          shares: number
          clicks: number
          saves: number
          engagement_rate: number
          recorded_at: string
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          platform: string
          impressions?: number
          reach?: number
          engagements?: number
          likes?: number
          comments?: number
          shares?: number
          clicks?: number
          saves?: number
          engagement_rate?: number
          recorded_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          platform?: string
          impressions?: number
          reach?: number
          engagements?: number
          likes?: number
          comments?: number
          shares?: number
          clicks?: number
          saves?: number
          engagement_rate?: number
          recorded_at?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_analytics_post_id_fkey"
            columns: ["post_id"]
            referencedRelation: "social_posts"
            referencedColumns: ["id"]
          }
        ]
      }
      post_notes: {
        Row: {
          id: string
          post_id: string
          author_id: string
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          author_id: string
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          author_id?: string
          content?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_notes_post_id_fkey"
            columns: ["post_id"]
            referencedRelation: "social_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "post_notes_author_id_fkey"
            columns: ["author_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      post_variations: {
        Row: {
          id: string
          post_id: string
          content: string
          media_urls: string[]
          hashtags: string[]
          performance_score: number
          created_at: string
        }
        Insert: {
          id?: string
          post_id: string
          content: string
          media_urls?: string[]
          hashtags?: string[]
          performance_score?: number
          created_at?: string
        }
        Update: {
          id?: string
          post_id?: string
          content?: string
          media_urls?: string[]
          hashtags?: string[]
          performance_score?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "post_variations_post_id_fkey"
            columns: ["post_id"]
            referencedRelation: "social_posts"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string | null
          first_name: string | null
          id: string
          last_name: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email?: string | null
          first_name?: string | null
          id: string
          last_name?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          last_name?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      social_accounts: {
        Row: {
          id: string
          user_id: string
          platform: string
          username: string
          display_name: string | null
          followers: number
          is_connected: boolean
          access_token: string | null
          refresh_token: string | null
          token_expires_at: string | null
          profile_url: string | null
          avatar_url: string | null
          last_sync: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          platform: string
          username: string
          display_name?: string | null
          followers?: number
          is_connected?: boolean
          access_token?: string | null
          refresh_token?: string | null
          token_expires_at?: string | null
          profile_url?: string | null
          avatar_url?: string | null
          last_sync?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          platform?: string
          username?: string
          display_name?: string | null
          followers?: number
          is_connected?: boolean
          access_token?: string | null
          refresh_token?: string | null
          token_expires_at?: string | null
          profile_url?: string | null
          avatar_url?: string | null
          last_sync?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "social_accounts_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      social_inbox: {
        Row: {
          id: string
          workspace_id: string
          platform: string
          message_type: string
          external_id: string
          from_username: string
          from_display_name: string | null
          from_avatar_url: string | null
          content: string
          post_id: string | null
          is_read: boolean
          is_replied: boolean
          priority: string
          internal_notes: string[]
          received_at: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          platform: string
          message_type: string
          external_id: string
          from_username: string
          from_display_name?: string | null
          from_avatar_url?: string | null
          content: string
          post_id?: string | null
          is_read?: boolean
          is_replied?: boolean
          priority?: string
          internal_notes?: string[]
          received_at: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          platform?: string
          message_type?: string
          external_id?: string
          from_username?: string
          from_display_name?: string | null
          from_avatar_url?: string | null
          content?: string
          post_id?: string | null
          is_read?: boolean
          is_replied?: boolean
          priority?: string
          internal_notes?: string[]
          received_at?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "social_inbox_workspace_id_fkey"
            columns: ["workspace_id"]
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "social_inbox_post_id_fkey"
            columns: ["post_id"]
            referencedRelation: "social_posts"
            referencedColumns: ["id"]
          }
        ]
      }
      social_posts: {
        Row: {
          id: string
          workspace_id: string
          author_id: string
          category_id: string | null
          platform: string
          content: string
          media_urls: string[]
          hashtags: string[]
          mentions: string[]
          scheduled_for: string | null
          status: string
          approval_status: string
          is_evergreen: boolean
          recycle_count: number
          max_recycles: number
          external_post_id: string | null
          published_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          author_id: string
          category_id?: string | null
          platform: string
          content: string
          media_urls?: string[]
          hashtags?: string[]
          mentions?: string[]
          scheduled_for?: string | null
          status?: string
          approval_status?: string
          is_evergreen?: boolean
          recycle_count?: number
          max_recycles?: number
          external_post_id?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          author_id?: string
          category_id?: string | null
          platform?: string
          content?: string
          media_urls?: string[]
          hashtags?: string[]
          mentions?: string[]
          scheduled_for?: string | null
          status?: string
          approval_status?: string
          is_evergreen?: boolean
          recycle_count?: number
          max_recycles?: number
          external_post_id?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "social_posts_workspace_id_fkey"
            columns: ["workspace_id"]
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "social_posts_author_id_fkey"
            columns: ["author_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "social_posts_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "content_categories"
            referencedColumns: ["id"]
          }
        ]
      }
      workspace_members: {
        Row: {
          id: string
          workspace_id: string
          user_id: string
          role: string
          joined_at: string
        }
        Insert: {
          id?: string
          workspace_id: string
          user_id: string
          role: string
          joined_at?: string
        }
        Update: {
          id?: string
          workspace_id?: string
          user_id?: string
          role?: string
          joined_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspace_members_workspace_id_fkey"
            columns: ["workspace_id"]
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workspace_members_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      workspaces: {
        Row: {
          id: string
          name: string
          description: string | null
          owner_id: string
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          owner_id: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          owner_id?: string
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspaces_owner_id_fkey"
            columns: ["owner_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

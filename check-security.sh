#!/bin/bash

# PulseBuzz.AI Security Check Script
echo "🔐 PulseBuzz.AI Security Status Check"
echo "===================================="

# Check if .env is in .gitignore
if grep -q "^\.env$" .gitignore; then
    echo "✅ .env is properly excluded in .gitignore"
else
    echo "❌ .env is NOT in .gitignore - SECURITY RISK!"
fi

# Check if .env is tracked by Git
if git ls-files | grep -q "^\.env$"; then
    echo "❌ .env is tracked by Git - SECURITY RISK!"
    echo "   Run: git rm --cached .env"
else
    echo "✅ .env is NOT tracked by Git - SECURE!"
fi

# Check if .env exists locally
if [ -f ".env" ]; then
    echo "✅ .env file exists locally for development"
    
    # Check for placeholder values
    if grep -q "your-.*-client-id" .env; then
        echo "✅ .env contains placeholder values (safe)"
    else
        echo "⚠️  .env may contain real credentials - ensure it's not committed"
    fi
else
    echo "ℹ️  No .env file found - copy from .env.example to get started"
fi

# Check if .env.example exists
if [ -f ".env.example" ]; then
    echo "✅ .env.example exists for documentation"
else
    echo "⚠️  .env.example missing - should exist for team reference"
fi

echo ""
echo "🛡️  Security Recommendations:"
echo "   1. Never commit .env files"
echo "   2. Use different keys for dev/prod"
echo "   3. Rotate API keys regularly"
echo "   4. Use platform environment variables in production"
echo ""
echo "📚 See SECURITY_GUIDE.md for complete security practices"

import { supabase } from '@/integrations/supabase/client';

/**
 * Database Setup Utility
 * This utility helps set up the required database tables for PulseBuzz.AI
 * Run this once to create all necessary tables in your Supabase instance
 */

export class DatabaseSetup {
  static async checkTablesExist(): Promise<boolean> {
    try {
      // Try to query a simple table to see if schema exists
      const { error } = await supabase
        .from('workspaces')
        .select('id')
        .limit(1);
      
      return !error;
    } catch (error) {
      return false;
    }
  }

  static async createTables(): Promise<void> {
    console.log('🚀 Setting up PulseBuzz.AI database tables...');
    
    try {
      // Read the SQL migration file content
      const migrationSQL = `
        -- PulseBuzz.AI Production Database Schema
        -- This migration creates all necessary tables for the social media management app

        -- Create workspaces table
        CREATE TABLE IF NOT EXISTS public.workspaces (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT,
          owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create workspace_members table
        CREATE TABLE IF NOT EXISTS public.workspace_members (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'editor', 'viewer')),
          joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(workspace_id, user_id)
        );

        -- Create content_categories table
        CREATE TABLE IF NOT EXISTS public.content_categories (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          description TEXT,
          color TEXT NOT NULL DEFAULT '#3B82F6',
          post_count INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          recycle_enabled BOOLEAN DEFAULT false,
          recycle_interval INTEGER DEFAULT 30,
          max_recycles INTEGER DEFAULT 3,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create social_accounts table
        CREATE TABLE IF NOT EXISTS public.social_accounts (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          platform TEXT NOT NULL,
          username TEXT NOT NULL,
          display_name TEXT,
          followers INTEGER DEFAULT 0,
          is_connected BOOLEAN DEFAULT false,
          access_token TEXT,
          refresh_token TEXT,
          token_expires_at TIMESTAMP WITH TIME ZONE,
          profile_url TEXT,
          avatar_url TEXT,
          last_sync TIMESTAMP WITH TIME ZONE,
          status TEXT DEFAULT 'pending' CHECK (status IN ('active', 'error', 'pending')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, platform)
        );

        -- Create social_posts table
        CREATE TABLE IF NOT EXISTS public.social_posts (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
          author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          category_id UUID REFERENCES public.content_categories(id) ON DELETE SET NULL,
          platform TEXT NOT NULL,
          content TEXT NOT NULL,
          media_urls TEXT[] DEFAULT '{}',
          hashtags TEXT[] DEFAULT '{}',
          mentions TEXT[] DEFAULT '{}',
          scheduled_for TIMESTAMP WITH TIME ZONE,
          status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'failed', 'pending_approval')),
          approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
          is_evergreen BOOLEAN DEFAULT false,
          recycle_count INTEGER DEFAULT 0,
          max_recycles INTEGER DEFAULT 3,
          external_post_id TEXT,
          published_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create media_library table
        CREATE TABLE IF NOT EXISTS public.media_library (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
          uploaded_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          name TEXT NOT NULL,
          file_type TEXT NOT NULL,
          file_size BIGINT NOT NULL,
          mime_type TEXT NOT NULL,
          storage_path TEXT NOT NULL,
          public_url TEXT NOT NULL,
          alt_text TEXT,
          tags TEXT[] DEFAULT '{}',
          used_in_posts INTEGER DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create social_inbox table
        CREATE TABLE IF NOT EXISTS public.social_inbox (
          id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
          platform TEXT NOT NULL,
          message_type TEXT NOT NULL CHECK (message_type IN ('comment', 'mention', 'direct_message')),
          external_id TEXT NOT NULL,
          from_username TEXT NOT NULL,
          from_display_name TEXT,
          from_avatar_url TEXT,
          content TEXT NOT NULL,
          post_id UUID REFERENCES public.social_posts(id) ON DELETE SET NULL,
          is_read BOOLEAN DEFAULT false,
          is_replied BOOLEAN DEFAULT false,
          priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
          internal_notes TEXT[] DEFAULT '{}',
          received_at TIMESTAMP WITH TIME ZONE NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(platform, external_id)
        );

        -- Create profiles table
        CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
          email TEXT,
          first_name TEXT,
          last_name TEXT,
          avatar_url TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;

      // Execute the SQL
      const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
      
      if (error) {
        console.error('❌ Failed to create tables:', error);
        throw error;
      }

      console.log('✅ Database tables created successfully!');
      
    } catch (error) {
      console.error('❌ Database setup failed:', error);
      throw error;
    }
  }

  static async setupDatabase(): Promise<void> {
    const tablesExist = await this.checkTablesExist();
    
    if (tablesExist) {
      console.log('✅ Database tables already exist!');
      return;
    }

    console.log('📋 Database tables not found. Setting up...');
    await this.createTables();
  }
}

// Export a simple function for easy use
export const setupDatabase = () => DatabaseSetup.setupDatabase();

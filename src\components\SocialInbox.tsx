
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageSquare } from 'lucide-react';

interface InboxItem {
  id: string;
  content: string;
  author: string;
  platform: string;
  type: string;
  read: boolean;
}

interface SocialInboxProps {
  inboxItems?: InboxItem[];
  onReply?: (id: string, message: string) => void;
  onMarkAsRead?: (id: string) => void;
  onAssign?: (id: string, assignee: string) => void;
  onAddNote?: (id: string, note: string) => void;
}

const SocialInbox: React.FC<SocialInboxProps> = ({
  inboxItems = [],
  onReply = () => {},
  onMarkAsRead = () => {},
  onAssign = () => {},
  onAddNote = () => {}
}) => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="text-center">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Social inbox component</p>
          <Badge variant="secondary">{inboxItems.length} items</Badge>
        </div>
      </CardContent>
    </Card>
  );
};

export default SocialInbox;

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Calendar component fixes */
.rdp {
  margin: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
}

.rdp-table {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
}

.rdp-months {
  max-width: 100% !important;
  width: 100% !important;
  justify-content: center !important;
}

.rdp-month {
  max-width: 100% !important;
  width: 100% !important;
}

.rdp-caption {
  margin-bottom: 1rem !important;
}

/* Prevent calendar overflow and duplication */
.calendar-container {
  position: relative;
  overflow: hidden;
  max-width: 100%;
  width: 100%;
}

.calendar-container .rdp {
  display: block !important;
}

/* Ensure buttons don't overlap */
.schedule-post-button {
  position: relative;
  z-index: 10;
  margin-top: 1rem;
}

/* Hide any duplicate calendar elements */
.rdp + .rdp {
  display: none !important;
}

/* Ensure single month display */
.rdp-months {
  flex-direction: column !important;
}

.rdp-month:not(:first-child) {
  display: none !important;
}

/* Fix calendar positioning */
.calendar-container .rdp {
  position: relative !important;
  z-index: 1 !important;
}

/* Prevent calendar from creating multiple instances */
.calendar-container {
  contain: layout style !important;
}

/* Force single calendar display */
.calendar-container .rdp-months > .rdp-month:nth-child(n+2) {
  display: none !important;
}

/* Ensure calendar takes full width but doesn't overflow */
.calendar-container .rdp {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

.calendar-container .rdp-table {
  width: 100% !important;
  table-layout: fixed !important;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .rdp-caption {
    font-size: 0.875rem !important;
  }

  .rdp-day {
    font-size: 0.75rem !important;
    height: 2rem !important;
    width: 2rem !important;
  }

  .rdp-head_cell {
    font-size: 0.75rem !important;
  }
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for better UX */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Ensure dropdowns appear above modals */
[data-radix-select-content] {
  z-index: 10000 !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 10000 !important;
}
# PulseBuzz.AI - Production Configuration
# Copy this file to .env and fill in your actual values for production deployment

# =============================================================================
# SSL/HTTPS Configuration
# =============================================================================

# Enable HTTPS in development
HTTPS=false

# SSL Certificate paths (for custom certificates)
SSL_KEY_PATH=./ssl/key.pem
SSL_CERT_PATH=./ssl/cert.pem

# Force HTTPS redirects (production)
VITE_FORCE_HTTPS=true

# Enable secure cookies (HTTPS only)
VITE_SECURE_COOKIES=true

# HSTS (HTTP Strict Transport Security) max age in seconds
VITE_HSTS_MAX_AGE=31536000

# =============================================================================
# Security Configuration
# =============================================================================

# Content Security Policy (CSP) settings
VITE_CSP_ENABLED=true

# Enable security headers
VITE_SECURITY_HEADERS=true

# Enable security warnings in development
VITE_SHOW_SECURITY_WARNINGS=true

# =============================================================================
# API Configuration
# =============================================================================
VITE_API_BASE_URL=http://localhost:3001/api
# For production: VITE_API_BASE_URL=https://your-api-domain.com/api

# =============================================================================
# OAuth Credentials - Social Media Platforms
# =============================================================================

# Instagram Basic Display API
# Get credentials from: https://developers.facebook.com/apps/
VITE_INSTAGRAM_CLIENT_ID=your-instagram-client-id
VITE_INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# Facebook Graph API
# Get credentials from: https://developers.facebook.com/apps/
VITE_FACEBOOK_CLIENT_ID=your-facebook-client-id
VITE_FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# LinkedIn API
# Get credentials from: https://www.linkedin.com/developers/apps
VITE_LINKEDIN_CLIENT_ID=your-linkedin-client-id
VITE_LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Twitter API v2
# Get credentials from: https://developer.twitter.com/en/portal/dashboard
VITE_TWITTER_CLIENT_ID=your-twitter-client-id
VITE_TWITTER_CLIENT_SECRET=your-twitter-client-secret

# =============================================================================
# Feature Flags - Enable/Disable Production Features
# =============================================================================

# Enable real-time posting to social media platforms
VITE_ENABLE_REAL_TIME_POSTING=false

# Enable analytics features
VITE_ENABLE_ANALYTICS=true

# Enable AI content generation
VITE_ENABLE_AI_CONTENT=false

# =============================================================================
# AI Services (Optional)
# =============================================================================

# OpenAI API for content generation
VITE_OPENAI_API_KEY=your-openai-api-key

# =============================================================================
# Production Setup Instructions
# =============================================================================

# 1. Create OAuth apps for each platform:
#    - Instagram: https://developers.facebook.com/apps/
#    - Facebook: https://developers.facebook.com/apps/
#    - LinkedIn: https://www.linkedin.com/developers/apps
#    - Twitter: https://developer.twitter.com/en/portal/dashboard

# 2. Set the redirect URIs in your OAuth apps:
#    - Instagram: http://localhost:8080/auth/instagram/callback
#    - Facebook: http://localhost:8080/auth/facebook/callback
#    - LinkedIn: http://localhost:8080/auth/linkedin/callback
#    - Twitter: http://localhost:8080/auth/twitter/callback
#    (For production, replace localhost:8080 with your domain)

# 3. Copy the client IDs and secrets to this file
# 4. Rename this file to .env
# 5. Set feature flags based on your production needs
# 6. Deploy with proper environment variables

# 🚀 Social Media Platform Testing Guide

This guide walks you through testing each social media platform connection in PulseBuzz.AI.

## 🎯 Overview

The app now supports **two modes**:
- **Demo Mode**: Works immediately, creates fake accounts for UI testing
- **Production Mode**: Requires OAuth setup, connects to real social media accounts

## 🔧 Current Setup Status

The Social Accounts page now:
- ✅ Uses proper Supabase authentication and user isolation
- ✅ Shows OAuth setup status for each platform
- ✅ Provides integrated setup guide
- ✅ Falls back to demo mode when OAuth isn't configured
- ✅ Stores credentials securely in Supabase with RLS policies

## 📋 Testing Steps

### 1. **Access the Social Accounts Page**
1. Navigate to **Settings > Social Accounts** in the sidebar
2. Click **"Show Setup Guide"** button in the header
3. You'll see the current OAuth status for each platform

### 2. **Demo Mode Testing (Immediate)**
If you see "Demo Mode" badges:
1. Click **"Connect"** on any platform
2. Wait 2 seconds for the simulated OAuth flow
3. See the connected account appear with demo data
4. Test all buttons: Refresh, Settings, Disconnect

### 3. **Production Mode Testing (Requires Setup)**

## 🔐 OAuth Setup for Each Platform

### **Instagram** 
**Time Required: ~10 minutes**

1. **Create Facebook App:**
   - Go to [Facebook Developers](https://developers.facebook.com/apps/)
   - Click "Create App" → Choose "Consumer"
   - Fill in app details

2. **Add Instagram Product:**
   - In your app dashboard, click "Add Product"
   - Find "Instagram Basic Display" and click "Set Up"

3. **Configure OAuth:**
   - Go to Instagram Basic Display → Basic Display
   - Click "Create New App" under Instagram App
   - Add OAuth Redirect URI: `http://localhost:8080/auth/instagram/callback`
   - Add Test Users (your Instagram account)

4. **Get Credentials:**
   - Copy the **Instagram App ID**
   - Copy the **Instagram App Secret**

5. **Update .env:**
   ```env
   VITE_INSTAGRAM_CLIENT_ID=your-instagram-app-id
   VITE_INSTAGRAM_CLIENT_SECRET=your-instagram-app-secret
   ```

6. **Test:**
   - Restart your dev server
   - Go to Social Accounts page
   - Instagram should show "OAuth Ready" badge
   - Click "Connect" → Real OAuth popup opens
   - Authorize with your Instagram account

---

### **Facebook**
**Time Required: ~8 minutes**

1. **Use Same Facebook App** (from Instagram setup above)

2. **Add Facebook Login:**
   - In app dashboard, click "Add Product"
   - Find "Facebook Login" and click "Set Up"

3. **Configure OAuth:**
   - Go to Facebook Login → Settings
   - Add OAuth Redirect URI: `http://localhost:8080/auth/facebook/callback`
   - Enable "Login with the JavaScript SDK"

4. **Get Credentials:**
   - Go to Settings → Basic
   - Copy the **App ID**
   - Copy the **App Secret**

5. **Update .env:**
   ```env
   VITE_FACEBOOK_CLIENT_ID=your-facebook-app-id
   VITE_FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
   ```

---

### **LinkedIn**
**Time Required: ~12 minutes**

1. **Create LinkedIn App:**
   - Go to [LinkedIn Developers](https://www.linkedin.com/developers/apps)
   - Click "Create App"
   - Fill in required details (company page required)

2. **Add Products:**
   - Request "Sign In with LinkedIn" product
   - Wait for approval (usually instant for personal use)

3. **Configure OAuth:**
   - Go to "Auth" tab
   - Add OAuth 2.0 redirect URL: `http://localhost:8080/auth/linkedin/callback`
   - Note the required scopes

4. **Get Credentials:**
   - Copy the **Client ID**
   - Copy the **Client Secret**

5. **Update .env:**
   ```env
   VITE_LINKEDIN_CLIENT_ID=your-linkedin-client-id
   VITE_LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
   ```

---

### **Twitter/X**
**Time Required: ~15 minutes**

1. **Create Twitter Developer Account:**
   - Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
   - Apply for developer access (may require approval)

2. **Create Project & App:**
   - Create a new project
   - Create an app within the project

3. **Configure OAuth 2.0:**
   - Go to App Settings → User authentication settings
   - Enable OAuth 2.0
   - Set App permissions (Read, Write)
   - Add Callback URI: `http://localhost:8080/auth/twitter/callback`
   - Add Website URL: `http://localhost:8080`

4. **Get Credentials:**
   - Copy the **Client ID**
   - Copy the **Client Secret**

5. **Update .env:**
   ```env
   VITE_TWITTER_CLIENT_ID=your-twitter-client-id
   VITE_TWITTER_CLIENT_SECRET=your-twitter-client-secret
   ```

## 🧪 Testing Each Platform

### **Testing Checklist for Each Platform:**

1. **OAuth Setup Verification:**
   - [ ] Platform shows "OAuth Ready" badge
   - [ ] No console errors when clicking Connect

2. **Connection Flow:**
   - [ ] Click "Connect" opens OAuth popup
   - [ ] Popup shows correct platform login
   - [ ] Authorization completes successfully
   - [ ] Account appears in "Connected Accounts" section

3. **Account Display:**
   - [ ] Shows correct username/handle
   - [ ] Shows platform icon
   - [ ] Shows follower count (if available)
   - [ ] Shows "Connected" badge

4. **Account Management:**
   - [ ] "Refresh" button works (shows toast)
   - [ ] "Settings" button works (shows toast)
   - [ ] "Disconnect" button works (removes account)

5. **Data Persistence:**
   - [ ] Refresh page - account still connected
   - [ ] Login/logout - account persists for user
   - [ ] Different user - cannot see other user's accounts

## 🔍 Troubleshooting

### **Common Issues:**

1. **"OAuth Setup Required" message:**
   - Check your `.env` file has the correct variables
   - Restart your dev server after updating `.env`
   - Verify credentials are not placeholder values

2. **Popup blocked:**
   - Allow popups for localhost:8080
   - Try different browser

3. **OAuth errors:**
   - Verify redirect URI matches exactly
   - Check app is in development/testing mode
   - Ensure test users are added (Instagram)

4. **Token errors:**
   - Check app permissions/scopes
   - Verify app is approved (LinkedIn, Twitter)

## 🎉 Success Criteria

You've successfully set up social media testing when:
- ✅ All platforms show "OAuth Ready" badges
- ✅ Real OAuth popups open for each platform
- ✅ Accounts connect with real usernames/data
- ✅ All account management buttons work
- ✅ Data persists across page refreshes
- ✅ User isolation works (different users see different accounts)

## 🚀 Next Steps

Once testing is complete:
1. **Production deployment**: Update redirect URIs to your production domain
2. **App review**: Submit apps for production approval (required for public use)
3. **Rate limits**: Monitor API usage and implement rate limiting
4. **Token refresh**: Implement automatic token refresh for expired tokens
5. **Error handling**: Add comprehensive error handling for API failures

## 📞 Need Help?

If you encounter issues:
1. Check the browser console for errors
2. Verify your `.env` file configuration
3. Test with the integrated Setup Guide in the app
4. Check platform-specific developer documentation


import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export interface ScheduledPost {
  id: string;
  platform: string;
  content: string;
  scheduledFor: string;
  status: 'scheduled' | 'posted' | 'failed' | 'posting';
  mediaUrls?: string[];
  postType: 'text' | 'image' | 'video' | 'carousel';
  hashtags?: string[];
  mentions?: string[];
  createdAt: string;
  errorMessage?: string;
}

const initialPosts: ScheduledPost[] = [
  {
    id: "1",
    platform: "Instagram",
    content: "🚀 Boost your productivity with these 5 simple tips that will transform your daily routine! #ProductivityTips #Success",
    scheduledFor: "2024-06-08T14:30:00",
    status: "scheduled",
    postType: "text",
    hashtags: ["ProductivityTips", "Success"],
    createdAt: new Date().toISOString(),
  },
  {
    id: "2",
    platform: "X (Twitter)",
    content: "Just discovered this amazing productivity hack that saved me 2 hours today! 💡 What's your favorite time-saving tip? #ProductivityHack",
    scheduledFor: "2024-06-08T10:15:00",
    status: "posted",
    postType: "text",
    hashtags: ["ProductivityHack"],
    createdAt: new Date().toISOString(),
  },
  {
    id: "3",
    platform: "LinkedIn",
    content: "The key to professional success isn't working harder, it's working smarter. Here's how to optimize your workflow for maximum impact...",
    scheduledFor: "2024-06-07T16:00:00",
    status: "failed",
    postType: "text",
    createdAt: new Date().toISOString(),
    errorMessage: "Authentication token expired",
  },
];

export const usePostScheduling = () => {
  const [posts, setPosts] = useState<ScheduledPost[]>(initialPosts);
  const { toast } = useToast();

  const schedulePost = (post: Omit<ScheduledPost, 'id' | 'createdAt' | 'status'>) => {
    const newPost: ScheduledPost = {
      ...post,
      id: Date.now().toString(),
      status: 'scheduled',
      createdAt: new Date().toISOString(),
    };

    setPosts(prev => [newPost, ...prev]);
    
    toast({
      title: "Post Scheduled!",
      description: `Your ${post.platform} post has been scheduled for ${new Date(post.scheduledFor).toLocaleString()}.`,
    });

    return newPost.id;
  };

  const updatePostStatus = (postId: string, status: ScheduledPost['status'], errorMessage?: string) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, status, errorMessage }
        : post
    ));
  };

  const deletePost = (postId: string) => {
    setPosts(prev => prev.filter(post => post.id !== postId));
    toast({
      title: "Post Deleted",
      description: "The scheduled post has been removed.",
    });
  };

  const editPost = (postId: string, updates: Partial<ScheduledPost>) => {
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, ...updates }
        : post
    ));
    
    toast({
      title: "Post Updated",
      description: "Your scheduled post has been updated.",
    });
  };

  const retryPost = (postId: string) => {
    updatePostStatus(postId, 'scheduled');
    toast({
      title: "Post Retry Scheduled",
      description: "The post will be attempted again at the scheduled time.",
    });
  };

  const postNow = async (postId: string) => {
    const post = posts.find(p => p.id === postId);
    if (!post) return;

    updatePostStatus(postId, 'posting');
    
    // Simulate posting process
    setTimeout(() => {
      const success = Math.random() > 0.2; // 80% success rate
      updatePostStatus(
        postId, 
        success ? 'posted' : 'failed',
        success ? undefined : 'Network error occurred'
      );
      
      toast({
        title: success ? "Post Published!" : "Post Failed",
        description: success 
          ? `Your ${post.platform} post has been published successfully.`
          : "There was an error publishing your post. Please try again.",
        variant: success ? "default" : "destructive",
      });
    }, 2000);
  };

  const getPlatformLimits = (platform: string) => {
    const limits = {
      "Instagram": {
        textLimit: 2200,
        hashtagLimit: 30,
        mediaLimit: 10,
        postTypes: ['image', 'video', 'carousel'],
      },
      "X (Twitter)": {
        textLimit: 280,
        hashtagLimit: null,
        mediaLimit: 4,
        postTypes: ['text', 'image', 'video'],
      },
      "Facebook": {
        textLimit: 63206,
        hashtagLimit: null,
        mediaLimit: 10,
        postTypes: ['text', 'image', 'video'],
      },
      "LinkedIn": {
        textLimit: 3000,
        hashtagLimit: null,
        mediaLimit: 1,
        postTypes: ['text', 'image'],
      },
    };

    return limits[platform as keyof typeof limits] || limits["X (Twitter)"];
  };

  return {
    posts,
    schedulePost,
    updatePostStatus,
    deletePost,
    editPost,
    retryPost,
    postNow,
    getPlatformLimits,
  };
};

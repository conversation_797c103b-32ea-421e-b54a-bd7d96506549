
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface ScheduledPostsListProps {
  onCreatePost: () => void;
}

const ScheduledPostsList: React.FC<ScheduledPostsListProps> = ({ onCreatePost }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <Button onClick={onCreatePost} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          Create New Post
        </Button>
      </CardContent>
    </Card>
  );
};

export default ScheduledPostsList;

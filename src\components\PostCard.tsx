import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreVertical, Edit, Trash2, Play, RotateCcw } from "lucide-react";
import { FaInstagram, FaFacebookF, FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

interface Post {
  id: number | string;
  platform: string;
  content: string;
  scheduledFor: string;
  status: string;
  errorMessage?: string;
}

interface PostCardProps {
  post: Post;
  statusIcon: React.ReactNode;
  statusColor: string;
  onDelete?: () => void;
  onEdit?: (updates: Partial<Post>) => void;
  onRetry?: () => void;
  onPostNow?: () => void;
}

const PostCard = ({ post, statusIcon, statusColor, onDelete, onEdit, onRetry, onPostNow }: PostCardProps) => {
  const getPlatformIcon = (platform: string) => {
    const iconProps = { size: 16, style: { display: 'inline-block' } };

    switch (platform) {
      case "Instagram":
        return <FaInstagram {...iconProps} className="text-pink-600" />;
      case "X (Twitter)":
        return <FaXTwitter {...iconProps} className="text-gray-900" />;
      case "Facebook":
        return <FaFacebookF {...iconProps} className="text-blue-600" />;
      case "LinkedIn":
        return <FaLinkedinIn {...iconProps} className="text-blue-700" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded"></div>;
    }
  };

  const formatDateTime = (dateTime: string) => {
    const date = new Date(dateTime);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="hover:shadow-md transition-all duration-200 border-0 bg-white/80 backdrop-blur-sm">
      <CardContent className="p-4">
        <div className="flex items-start justify-between space-x-4">
          <div className="flex-1 space-y-3">
            <div className="flex items-center space-x-3">
              {getPlatformIcon(post.platform)}
              <span className="font-medium text-gray-800">{post.platform}</span>
              <Badge className={`${statusColor} flex items-center space-x-1`}>
                {statusIcon}
                <span className="capitalize">{post.status}</span>
              </Badge>
            </div>
            
            <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
              {post.content}
            </p>
            
            {post.errorMessage && (
              <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
                Error: {post.errorMessage}
              </div>
            )}
            
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Scheduled for: {formatDateTime(post.scheduledFor)}</span>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white border border-gray-200">
              {post.status === 'scheduled' && onPostNow && (
                <DropdownMenuItem onClick={onPostNow} className="flex items-center space-x-2 cursor-pointer">
                  <Play className="h-4 w-4" />
                  <span>Post Now</span>
                </DropdownMenuItem>
              )}
              {post.status === 'failed' && onRetry && (
                <DropdownMenuItem onClick={onRetry} className="flex items-center space-x-2 cursor-pointer">
                  <RotateCcw className="h-4 w-4" />
                  <span>Retry</span>
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit({})} className="flex items-center space-x-2 cursor-pointer">
                  <Edit className="h-4 w-4" />
                  <span>Edit</span>
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem onClick={onDelete} className="flex items-center space-x-2 cursor-pointer text-red-600">
                  <Trash2 className="h-4 w-4" />
                  <span>Delete</span>
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );
};

export default PostCard;

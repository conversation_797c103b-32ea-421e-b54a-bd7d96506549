
import React, { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SecurityProvider, SecurityStatus } from "@/components/SecurityProvider";
import NotificationSystem from "@/components/NotificationSystem";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AuthProvider } from "@/contexts/SupabaseAuthContext";
import { AuthGuard } from "@/components/AuthGuard";
import { DataInitializer } from "@/components/DataInitializer";
import ErrorBoundary from "@/components/ErrorBoundary";
// Lazy load pages for better performance
const Index = React.lazy(() => import("./pages/Index"));
const WorkingIndex = React.lazy(() => import("./pages/WorkingIndex"));
const SimpleIndex = React.lazy(() => import("./pages/SimpleIndex"));
const TestIndex = React.lazy(() => import("./pages/TestIndex"));
const ContentPlanner = React.lazy(() => import("./pages/ContentPlanner"));
const AdvancedFeatures = React.lazy(() => import("./pages/AdvancedFeatures"));
const CreatePost = React.lazy(() => import("./pages/CreatePostMinimal"));
const MediaLibrary = React.lazy(() => import("./pages/MediaLibrary"));
const ScheduledPosts = React.lazy(() => import("./pages/ScheduledPosts"));
const Categories = React.lazy(() => import("./pages/Categories"));
const BulkManager = React.lazy(() => import("./pages/BulkManager"));
const Analytics = React.lazy(() => import("./pages/Analytics"));
const AdvancedAnalytics = React.lazy(() => import("./pages/AdvancedAnalytics"));
const Performance = React.lazy(() => import("./pages/Performance"));
// Import problematic pages directly to fix navigation errors
import SocialAccounts from "./pages/SocialAccounts";
import TeamCollaborationPage from "./pages/TeamCollaboration";

// Keep these as lazy imports
const Settings = React.lazy(() => import("./pages/Settings"));
const Help = React.lazy(() => import("./pages/Help"));
const TestFunctionality = React.lazy(() => import("./pages/TestFunctionality"));
const TestSocialFlow = React.lazy(() => import("./pages/TestSocialFlow"));
const NotFound = React.lazy(() => import("./pages/NotFound"));
const Notifications = React.lazy(() => import("./pages/Notifications"));
const SocialInboxPage = React.lazy(() => import("./pages/SocialInbox"));

// Keep auth pages as regular imports for faster loading
import Auth from "./pages/Auth";
import Login from "./pages/Login";
import ForgotPassword from "./pages/ForgotPassword";
import TermsOfService from "./pages/TermsOfService";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import DatabaseSetup from "./pages/DatabaseSetup";
import InstagramCallback from "./pages/auth/InstagramCallback";
import FacebookCallback from "./pages/auth/FacebookCallback";
import LinkedInCallback from "./pages/auth/LinkedInCallback";
import TwitterCallback from "./pages/auth/TwitterCallback";
import OAuthSetup from "./pages/OAuthSetup";

const queryClient = new QueryClient();

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <TooltipProvider>
            <SecurityProvider enforceHTTPS={false} showSecurityWarnings={false}>
              <AuthProvider>
                <DataInitializer>
                  <Toaster />
                  <Sonner />
                  <NotificationSystem />
                  <BrowserRouter>
                    <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div></div>}>
                      <Routes>
                        {/* Authentication Routes */}
                        <Route path="/login" element={<Login />} />
                        <Route path="/forgot-password" element={<ForgotPassword />} />

                        {/* Legal Pages */}
                        <Route path="/terms" element={<TermsOfService />} />
                        <Route path="/privacy" element={<PrivacyPolicy />} />

                        {/* Database Setup */}
                        <Route path="/setup" element={<DatabaseSetup />} />

                        {/* Protected Routes */}
                        <Route path="/" element={<AuthGuard><Index /></AuthGuard>} />
                  <Route path="/working" element={<AuthGuard><WorkingIndex /></AuthGuard>} />
                  <Route path="/simple" element={<AuthGuard><SimpleIndex /></AuthGuard>} />
                  <Route path="/test" element={<AuthGuard><TestIndex /></AuthGuard>} />
                  <Route path="/test-functionality" element={<AuthGuard><TestFunctionality /></AuthGuard>} />
                  <Route path="/test-social-flow" element={<AuthGuard><TestSocialFlow /></AuthGuard>} />
                  <Route path="/content-planner" element={<AuthGuard><ContentPlanner /></AuthGuard>} />
                  <Route path="/advanced-features" element={<AuthGuard><AdvancedFeatures /></AuthGuard>} />

                  {/* Main Navigation Routes */}
                  <Route path="/create" element={<AuthGuard><CreatePost /></AuthGuard>} />
                  <Route path="/media" element={<AuthGuard><MediaLibrary /></AuthGuard>} />
                  <Route path="/scheduled" element={<AuthGuard><ScheduledPosts /></AuthGuard>} />

                  {/* Content Management Routes */}
                  <Route path="/categories" element={<AuthGuard><Categories /></AuthGuard>} />
                  <Route path="/bulk-manager" element={<AuthGuard><BulkManager /></AuthGuard>} />

                  {/* Analytics Routes */}
                  <Route path="/analytics" element={<AuthGuard><Analytics /></AuthGuard>} />
                  <Route path="/advanced-analytics" element={<AuthGuard><AdvancedAnalytics /></AuthGuard>} />
                  <Route path="/performance" element={<AuthGuard><Performance /></AuthGuard>} />

                  {/* Engagement Routes */}
                  <Route path="/inbox" element={<AuthGuard><SocialInboxPage /></AuthGuard>} />
                  <Route path="/collaboration" element={<AuthGuard><TeamCollaborationPage /></AuthGuard>} />

                  {/* Account Routes */}
                  <Route path="/accounts" element={<AuthGuard><SocialAccounts /></AuthGuard>} />
                  <Route path="/settings" element={<AuthGuard><Settings /></AuthGuard>} />
                  <Route path="/notifications" element={<AuthGuard><Notifications /></AuthGuard>} />
                  <Route path="/help" element={<AuthGuard><Help /></AuthGuard>} />

                  {/* OAuth Callback Routes */}
                  <Route path="/auth/instagram/callback" element={<InstagramCallback />} />
                  <Route path="/auth/facebook/callback" element={<FacebookCallback />} />
                  <Route path="/auth/linkedin/callback" element={<LinkedInCallback />} />
                  <Route path="/auth/twitter/callback" element={<TwitterCallback />} />

                  {/* OAuth Setup */}
                  <Route path="/oauth-setup" element={<AuthGuard><OAuthSetup /></AuthGuard>} />

                        {/* Catch-all route */}
                        <Route path="*" element={<NotFound />} />
                      </Routes>
                    </Suspense>
                  </BrowserRouter>
                <SecurityStatus />
              </DataInitializer>
            </AuthProvider>
          </SecurityProvider>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;

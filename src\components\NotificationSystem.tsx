import React, { useEffect } from 'react';
import { useAppStore } from '@/store/appStore';
import { useToast } from '@/hooks/use-toast';

const NotificationSystem: React.FC = () => {
  const { notifications, removeNotification } = useAppStore();
  const { toast } = useToast();

  useEffect(() => {
    // Show new notifications as toasts
    notifications.forEach(notification => {
      toast({
        title: notification.type === 'success' ? 'Success' : 
               notification.type === 'error' ? 'Error' : 'Info',
        description: notification.message,
        variant: notification.type === 'error' ? 'destructive' : 'default',
      });
      
      // Remove the notification after showing it
      removeNotification(notification.id);
    });
  }, [notifications, toast, removeNotification]);

  return null; // This component doesn't render anything visible
};

export default NotificationSystem;

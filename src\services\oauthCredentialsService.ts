import { supabase } from '@/integrations/supabase/client';
import { encryptionService, type EncryptedData } from './encryptionService';
import { useAuth } from '@/contexts/SupabaseAuthContext';

export interface OAuthCredential {
  id: string;
  platform: string;
  platformDisplayName: string;
  isActive: boolean;
  verificationStatus: 'pending' | 'verified' | 'failed' | 'expired';
  lastVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OAuthCredentialInput {
  platform: string;
  platformDisplayName: string;
  clientId: string;
  clientSecret: string;
}

export interface DecryptedCredential extends OAuthCredential {
  clientId: string;
  clientSecret: string;
}

class OAuthCredentialsService {
  private static instance: OAuthCredentialsService;
  private workspaceId: string | null = null;

  private constructor() {}

  static getInstance(): OAuthCredentialsService {
    if (!OAuthCredentialsService.instance) {
      OAuthCredentialsService.instance = new OAuthCredentialsService();
    }
    return OAuthCredentialsService.instance;
  }

  /**
   * Initialize service with workspace context
   */
  setWorkspace(workspaceId: string): void {
    this.workspaceId = workspaceId;
  }

  /**
   * Get all OAuth credentials for current workspace
   */
  async getCredentials(): Promise<OAuthCredential[]> {
    if (!this.workspaceId) {
      throw new Error('Workspace not set');
    }

    const { data, error } = await supabase
      .from('oauth_credentials')
      .select(`
        id,
        platform,
        platform_display_name,
        is_active,
        verification_status,
        last_verified_at,
        created_at,
        updated_at
      `)
      .eq('workspace_id', this.workspaceId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Failed to fetch OAuth credentials:', error);
      throw new Error('Failed to fetch OAuth credentials');
    }

    return data.map(item => ({
      id: item.id,
      platform: item.platform,
      platformDisplayName: item.platform_display_name,
      isActive: item.is_active,
      verificationStatus: item.verification_status as any,
      lastVerifiedAt: item.last_verified_at,
      createdAt: item.created_at,
      updatedAt: item.updated_at
    }));
  }

  /**
   * Get decrypted credentials for a specific platform
   */
  async getDecryptedCredentials(platform: string): Promise<DecryptedCredential | null> {
    if (!this.workspaceId) {
      throw new Error('Workspace not set');
    }

    const { data, error } = await supabase
      .from('oauth_credentials')
      .select('*')
      .eq('workspace_id', this.workspaceId)
      .eq('platform', platform)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No credentials found
      }
      console.error('Failed to fetch OAuth credential:', error);
      throw new Error('Failed to fetch OAuth credential');
    }

    try {
      // Decrypt the credentials
      const clientId = await encryptionService.decrypt(
        JSON.parse(data.client_id_encrypted) as EncryptedData
      );
      const clientSecret = await encryptionService.decrypt(
        JSON.parse(data.client_secret_encrypted) as EncryptedData
      );

      return {
        id: data.id,
        platform: data.platform,
        platformDisplayName: data.platform_display_name,
        isActive: data.is_active,
        verificationStatus: data.verification_status,
        lastVerifiedAt: data.last_verified_at,
        createdAt: data.created_at,
        updatedAt: data.updated_at,
        clientId,
        clientSecret
      };
    } catch (error) {
      console.error('Failed to decrypt credentials:', error);
      throw new Error('Failed to decrypt credentials');
    }
  }

  /**
   * Save new OAuth credentials (encrypted)
   */
  async saveCredentials(input: OAuthCredentialInput): Promise<OAuthCredential> {
    if (!this.workspaceId) {
      throw new Error('Workspace not set');
    }

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Encrypt the credentials
      const encryptedClientId = await encryptionService.encrypt(input.clientId);
      const encryptedClientSecret = await encryptionService.encrypt(input.clientSecret);

      // Save to database
      const { data, error } = await supabase
        .from('oauth_credentials')
        .upsert({
          user_id: user.id,
          workspace_id: this.workspaceId,
          platform: input.platform,
          platform_display_name: input.platformDisplayName,
          client_id_encrypted: JSON.stringify(encryptedClientId),
          client_secret_encrypted: JSON.stringify(encryptedClientSecret),
          is_active: true,
          verification_status: 'pending',
          created_by: user.id
        }, {
          onConflict: 'workspace_id,platform'
        })
        .select()
        .single();

      if (error) {
        console.error('Failed to save OAuth credentials:', error);
        throw new Error('Failed to save OAuth credentials');
      }

      return {
        id: data.id,
        platform: data.platform,
        platformDisplayName: data.platform_display_name,
        isActive: data.is_active,
        verificationStatus: data.verification_status,
        lastVerifiedAt: data.last_verified_at,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      };
    } catch (error) {
      console.error('Failed to encrypt and save credentials:', error);
      throw new Error('Failed to save credentials');
    }
  }

  /**
   * Update verification status of credentials
   */
  async updateVerificationStatus(
    platform: string, 
    status: 'verified' | 'failed' | 'expired'
  ): Promise<void> {
    if (!this.workspaceId) {
      throw new Error('Workspace not set');
    }

    const { error } = await supabase
      .from('oauth_credentials')
      .update({
        verification_status: status,
        last_verified_at: status === 'verified' ? new Date().toISOString() : null
      })
      .eq('workspace_id', this.workspaceId)
      .eq('platform', platform);

    if (error) {
      console.error('Failed to update verification status:', error);
      throw new Error('Failed to update verification status');
    }
  }

  /**
   * Delete OAuth credentials
   */
  async deleteCredentials(platform: string): Promise<void> {
    if (!this.workspaceId) {
      throw new Error('Workspace not set');
    }

    const { error } = await supabase
      .from('oauth_credentials')
      .update({ is_active: false })
      .eq('workspace_id', this.workspaceId)
      .eq('platform', platform);

    if (error) {
      console.error('Failed to delete OAuth credentials:', error);
      throw new Error('Failed to delete OAuth credentials');
    }
  }

  /**
   * Test OAuth credentials by attempting to verify them
   */
  async testCredentials(platform: string): Promise<boolean> {
    try {
      const credentials = await this.getDecryptedCredentials(platform);
      if (!credentials) {
        throw new Error('No credentials found for platform');
      }

      // Here you would implement platform-specific verification
      // For now, we'll just check if credentials exist and are not empty
      const isValid = !!(credentials.clientId && credentials.clientSecret);
      
      await this.updateVerificationStatus(
        platform, 
        isValid ? 'verified' : 'failed'
      );

      return isValid;
    } catch (error) {
      await this.updateVerificationStatus(platform, 'failed');
      throw error;
    }
  }

  /**
   * Get credentials for OAuth flow (returns only what's needed)
   */
  async getCredentialsForOAuth(platform: string): Promise<{
    clientId: string;
    clientSecret: string;
  } | null> {
    const credentials = await this.getDecryptedCredentials(platform);
    if (!credentials) {
      return null;
    }

    return {
      clientId: credentials.clientId,
      clientSecret: credentials.clientSecret
    };
  }

  /**
   * Check if platform has valid credentials
   */
  async hasValidCredentials(platform: string): Promise<boolean> {
    try {
      const credentials = await this.getDecryptedCredentials(platform);
      return !!(credentials && 
                credentials.clientId && 
                credentials.clientSecret && 
                credentials.verificationStatus === 'verified');
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const oauthCredentialsService = OAuthCredentialsService.getInstance();

/**
 * React hook for OAuth credentials management
 */
export const useOAuthCredentials = () => {
  const { user } = useAuth();

  const initializeEncryption = async () => {
    if (user?.access_token) {
      await encryptionService.initializeKey(user.access_token);
    }
  };

  return {
    initializeEncryption,
    ...oauthCredentialsService
  };
};

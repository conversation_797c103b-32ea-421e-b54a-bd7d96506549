import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { ChevronLeft, ChevronRight, Plus, Clock, Calendar as CalendarIcon, List } from "lucide-react";
import { usePostScheduling } from "@/hooks/usePostScheduling";

interface CalendarViewProps {
  onCreatePost: () => void;
}

const CalendarView = ({ onCreatePost }: CalendarViewProps) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const { posts } = usePostScheduling();

  const getPostsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return posts.filter(post => {
      const postDate = new Date(post.scheduledFor).toISOString().split('T')[0];
      return postDate === dateStr;
    });
  };

  const getPostsForSelectedDate = () => {
    return getPostsForDate(selectedDate);
  };

  const formatTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'posted':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getUpcomingPosts = () => {
    const now = new Date();
    return posts
      .filter(post => new Date(post.scheduledFor) > now && post.status === 'scheduled')
      .sort((a, b) => new Date(a.scheduledFor).getTime() - new Date(b.scheduledFor).getTime())
      .slice(0, 5);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Content Calendar</h2>
        <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2">
          <div className="flex space-x-2">
            <Button
              variant={viewMode === 'calendar' ? 'default' : 'outline'}
              onClick={() => setViewMode('calendar')}
              size="sm"
              className="flex-1 sm:flex-none"
            >
              <CalendarIcon className="h-4 w-4 mr-1 sm:mr-2" />
              Calendar
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              onClick={() => setViewMode('list')}
              size="sm"
              className="flex-1 sm:flex-none"
            >
              <List className="h-4 w-4 mr-1 sm:mr-2" />
              List
            </Button>
          </div>
          <Button
            onClick={onCreatePost}
            className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
            size="sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Post
          </Button>
        </div>
      </div>

      {viewMode === 'calendar' ? (
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 lg:gap-6 min-h-0">
          {/* Calendar */}
          <div className="xl:col-span-2 min-h-0">
            <Card className="h-full">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg sm:text-xl">Schedule Calendar</CardTitle>
              </CardHeader>
              <CardContent className="p-4 sm:p-6">
                <div className="calendar-container w-full overflow-hidden relative">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    className="w-full mx-auto rounded-md border-0"
                    showOutsideDays={false}
                    modifiers={{
                      hasPost: (date) => getPostsForDate(date).length > 0
                    }}
                    modifiersStyles={{
                      hasPost: {
                        backgroundColor: '#dbeafe',
                        color: '#1e40af',
                        fontWeight: 'bold',
                        borderRadius: '6px'
                      }
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Selected Date Posts */}
          <div className="space-y-4 min-h-0">
            <Card className="h-fit relative">
              <CardHeader className="pb-3">
                <CardTitle className="text-base sm:text-lg flex items-center justify-between">
                  <span>
                    {selectedDate.toLocaleDateString('en-US', {
                      weekday: 'long',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    {getPostsForSelectedDate().length} posts
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  {getPostsForSelectedDate().length > 0 ? (
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {getPostsForSelectedDate().map(post => (
                        <div key={post.id} className="p-3 border rounded-lg hover:shadow-sm transition-shadow">
                          <div className="flex items-center justify-between mb-2">
                            <Badge className={getStatusColor(post.status)} variant="secondary">
                              {post.status}
                            </Badge>
                            <span className="text-xs text-gray-500 flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {formatTime(post.scheduledFor)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700 line-clamp-2 mb-2">{post.content}</p>
                          <div className="flex items-center justify-between">
                            <p className="text-xs text-gray-500 font-medium">{post.platform}</p>
                            {post.errorMessage && (
                              <span className="text-xs text-red-500 truncate max-w-32" title={post.errorMessage}>
                                Error: {post.errorMessage}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 px-4">
                      <div className="mb-4">
                        <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">
                          <CalendarIcon className="h-8 w-8 text-blue-400" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-2">No posts scheduled</h3>
                        <p className="text-sm text-gray-500 mb-4">Schedule your first post for this date</p>
                        <Button
                          onClick={onCreatePost}
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Schedule Post
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        /* List View */
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            {posts.map(post => (
              <Card key={post.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge className={getStatusColor(post.status)}>
                          {post.status}
                        </Badge>
                        <span className="text-sm text-gray-600">{post.platform}</span>
                        <span className="text-sm text-gray-500">
                          {new Date(post.scheduledFor).toLocaleDateString()} at {formatTime(post.scheduledFor)}
                        </span>
                      </div>
                      <p className="text-gray-700 line-clamp-2">{post.content}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* Upcoming Posts Sidebar */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Upcoming Posts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {getUpcomingPosts().map(post => (
                    <div key={post.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-600">{post.platform}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(post.scheduledFor).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 line-clamp-2">{post.content}</p>
                    </div>
                  ))}
                  {getUpcomingPosts().length === 0 && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      No upcoming posts
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default CalendarView;

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Sparkles, 
  Wand2, 
  Copy, 
  RefreshCw, 
  Lightbulb,
  Target,
  TrendingUp,
  Users,
  MessageCircle,
  Heart,
  Share
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface AIContentGeneratorProps {
  onContentGenerated: (content: string) => void;
  selectedPlatforms: string[];
}

const AIContentGenerator: React.FC<AIContentGeneratorProps> = ({
  onContentGenerated,
  selectedPlatforms
}) => {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState('');
  const [tone, setTone] = useState('professional');
  const [contentType, setContentType] = useState('promotional');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const toneOptions = [
    { value: 'professional', label: 'Professional', icon: '💼' },
    { value: 'casual', label: 'Casual', icon: '😊' },
    { value: 'friendly', label: 'Friendly', icon: '🤝' },
    { value: 'enthusiastic', label: 'Enthusiastic', icon: '🎉' },
    { value: 'informative', label: 'Informative', icon: '📚' },
    { value: 'humorous', label: 'Humorous', icon: '😄' }
  ];

  const contentTypeOptions = [
    { value: 'promotional', label: 'Promotional', icon: '📢' },
    { value: 'educational', label: 'Educational', icon: '🎓' },
    { value: 'behind-the-scenes', label: 'Behind the Scenes', icon: '🎬' },
    { value: 'user-generated', label: 'User Generated', icon: '👥' },
    { value: 'news-update', label: 'News & Updates', icon: '📰' },
    { value: 'question', label: 'Question/Poll', icon: '❓' },
    { value: 'inspirational', label: 'Inspirational', icon: '✨' },
    { value: 'how-to', label: 'How-to Guide', icon: '🔧' }
  ];

  const samplePrompts = [
    "Launch announcement for our new product feature",
    "Tips for improving social media engagement",
    "Behind-the-scenes look at our team culture",
    "Customer success story and testimonial",
    "Industry trends and insights",
    "How-to guide for using our product",
    "Company milestone celebration",
    "Seasonal promotion or holiday greeting"
  ];

  const generateContent = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Missing Prompt",
        description: "Please enter a topic or prompt for content generation.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      // Simulate AI content generation (in real app, this would call an AI API)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const platformText = selectedPlatforms.length > 0 
        ? ` for ${selectedPlatforms.join(', ')}`
        : '';
      
      const mockContent = generateMockContent(prompt, tone, contentType, platformText);
      setGeneratedContent(mockContent);
      
      // Generate suggestions
      const mockSuggestions = generateMockSuggestions(prompt, contentType);
      setSuggestions(mockSuggestions);
      
      toast({
        title: "Content Generated!",
        description: "AI has created content based on your prompt.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const generateMockContent = (prompt: string, tone: string, type: string, platforms: string) => {
    const toneAdjectives = {
      professional: 'We are excited to',
      casual: 'Hey everyone! We\'re',
      friendly: 'Hi there! We\'re thrilled to',
      enthusiastic: '🎉 We\'re absolutely thrilled to',
      informative: 'Here\'s what you need to know:',
      humorous: 'Plot twist:'
    };

    const starter = toneAdjectives[tone as keyof typeof toneAdjectives] || 'We are excited to';
    
    return `${starter} share insights about ${prompt}${platforms}! 

✨ Key highlights:
• Innovative approach to solving real problems
• Community-focused solutions that make a difference
• Proven results that speak for themselves

What are your thoughts on this? Let us know in the comments below! 

#Innovation #Community #Success #Growth`;
  };

  const generateMockSuggestions = (prompt: string, type: string): string[] => {
    return [
      `Add a call-to-action encouraging engagement`,
      `Include relevant industry hashtags`,
      `Mention specific benefits or features`,
      `Add a question to spark conversation`,
      `Include social proof or testimonials`
    ];
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: "Content copied to clipboard.",
    });
  };

  const useGeneratedContent = () => {
    onContentGenerated(generatedContent);
    toast({
      title: "Content Applied!",
      description: "Generated content has been added to your post.",
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-purple-500" />
          AI Content Generator
        </CardTitle>
        <CardDescription>
          Generate engaging content with AI assistance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Content Generation Form */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="ai-prompt">What would you like to post about? *</Label>
            <Textarea
              id="ai-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="e.g., New product launch, company milestone, industry insights..."
              className="min-h-[80px]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tone">Tone</Label>
              <Select value={tone} onValueChange={setTone}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {toneOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content-type">Content Type</Label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {contentTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center gap-2">
                        <span>{option.icon}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={generateContent} 
            disabled={isGenerating || !prompt.trim()}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Generating Content...
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4 mr-2" />
                Generate Content
              </>
            )}
          </Button>
        </div>

        {/* Sample Prompts */}
        <div className="space-y-2">
          <Label>Quick Prompts</Label>
          <div className="flex flex-wrap gap-2">
            {samplePrompts.slice(0, 4).map((samplePrompt, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setPrompt(samplePrompt)}
                className="text-xs"
              >
                {samplePrompt}
              </Button>
            ))}
          </div>
        </div>

        {/* Generated Content */}
        {generatedContent && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Generated Content</Label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedContent)}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                  <Button
                    size="sm"
                    onClick={useGeneratedContent}
                  >
                    <Target className="w-4 h-4 mr-1" />
                    Use Content
                  </Button>
                </div>
              </div>
              
              <div className="p-4 bg-muted rounded-lg">
                <p className="whitespace-pre-wrap text-sm">{generatedContent}</p>
              </div>

              {/* Suggestions */}
              {suggestions.length > 0 && (
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Lightbulb className="w-4 h-4" />
                    Suggestions to Improve
                  </Label>
                  <ul className="space-y-1">
                    {suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                        <span className="text-blue-500 mt-1">•</span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default AIContentGenerator;

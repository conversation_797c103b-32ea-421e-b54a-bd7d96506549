# 🔒 SSL/HTTPS Setup Guide for PulseBuzz.AI

This guide covers setting up SSL/HTTPS for both development and production environments.

## 🚀 Quick Start (Development)

### Option 1: Automatic Setup (Recommended)
```bash
# Generate SSL certificates and start HTTPS dev server
npm run dev:ssl
```

### Option 2: Manual Setup
```bash
# Generate SSL certificates
npm run ssl:generate

# Start HTTPS development server
npm run dev:https
```

### Option 3: Check SSL Status
```bash
# Check if SSL certificates exist
npm run ssl:check
```

## 🛠️ Development SSL Setup

### Prerequisites
You need OpenSSL installed on your system:

**Windows:**
- Download from: https://slproweb.com/products/Win32OpenSSL.html
- Or install via Chocolatey: `choco install openssl`
- Or use Git Bash (includes OpenSSL)

**macOS:**
```bash
brew install openssl
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-get install openssl

# CentOS/RHEL
sudo yum install openssl
```

### Alternative: mkcert (Trusted Local Certificates)
For trusted local certificates without browser warnings:

1. Install mkcert: https://github.com/FiloSottile/mkcert
2. Setup local CA:
   ```bash
   mkcert -install
   ```
3. Generate certificates:
   ```bash
   mkdir ssl
   mkcert -key-file ssl/key.pem -cert-file ssl/cert.pem localhost 127.0.0.1 ::1
   ```

## 🌐 Production SSL Setup

### Option 1: Let's Encrypt (Free SSL)
For production deployments with a domain:

```bash
# Install Certbot
sudo apt-get install certbot

# Generate SSL certificate
sudo certbot certonly --standalone -d yourdomain.com -d www.yourdomain.com

# Certificates will be in:
# /etc/letsencrypt/live/yourdomain.com/fullchain.pem
# /etc/letsencrypt/live/yourdomain.com/privkey.pem
```

### Option 2: Cloudflare SSL
1. Add your domain to Cloudflare
2. Enable "Full (strict)" SSL/TLS encryption
3. Use Cloudflare's origin certificates for your server

### Option 3: AWS Certificate Manager
For AWS deployments:
1. Request certificate in AWS Certificate Manager
2. Validate domain ownership
3. Use with Application Load Balancer or CloudFront

## 🔧 Environment Configuration

### Development (.env.local)
```env
# Enable HTTPS in development
HTTPS=true
SSL_KEY_PATH=./ssl/key.pem
SSL_CERT_PATH=./ssl/cert.pem

# Security headers
VITE_FORCE_HTTPS=true
VITE_SECURE_COOKIES=true
```

### Production (.env.production)
```env
# Production SSL settings
HTTPS=true
SSL_KEY_PATH=/path/to/production/key.pem
SSL_CERT_PATH=/path/to/production/cert.pem

# Security settings
VITE_FORCE_HTTPS=true
VITE_SECURE_COOKIES=true
VITE_HSTS_MAX_AGE=31536000
```

## 🛡️ Security Headers

The app automatically includes security headers in production:

- **Strict-Transport-Security**: Forces HTTPS connections
- **X-Content-Type-Options**: Prevents MIME type sniffing
- **X-Frame-Options**: Prevents clickjacking
- **X-XSS-Protection**: Enables XSS filtering
- **Referrer-Policy**: Controls referrer information

## 📱 Development URLs

- **HTTP**: http://localhost:8080
- **HTTPS**: https://localhost:8443

## 🚨 Browser Security Warnings

### Self-Signed Certificates (Development)
You'll see security warnings with self-signed certificates:

1. Click "Advanced" or "Show details"
2. Click "Proceed to localhost (unsafe)" or "Continue to site"
3. This is normal for development - the connection is still encrypted

### Trusted Certificates (mkcert)
No warnings with mkcert - certificates are trusted by your system.

## 🔄 SSL Certificate Renewal

### Let's Encrypt Auto-Renewal
```bash
# Test renewal
sudo certbot renew --dry-run

# Setup auto-renewal (crontab)
0 12 * * * /usr/bin/certbot renew --quiet
```

### Development Certificate Renewal
Development certificates expire after 365 days:
```bash
# Remove old certificates
rm -rf ssl/

# Generate new certificates
npm run ssl:generate
```

## 🐳 Docker SSL Setup

### Dockerfile with SSL
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy SSL certificates
COPY ssl/ ./ssl/

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy app files
COPY . .

# Build app
RUN npm run build

# Expose HTTPS port
EXPOSE 8443

# Start with HTTPS
CMD ["npm", "run", "preview:https"]
```

### Docker Compose with SSL
```yaml
version: '3.8'
services:
  pulsebuzz-ai:
    build: .
    ports:
      - "8443:8443"
    volumes:
      - ./ssl:/app/ssl:ro
    environment:
      - HTTPS=true
      - NODE_ENV=production
```

## 🔍 Troubleshooting

### Common Issues

**"OpenSSL not found"**
- Install OpenSSL (see Prerequisites above)
- On Windows, ensure OpenSSL is in your PATH

**"Certificate not trusted"**
- Use mkcert for trusted local certificates
- Or accept the security warning in development

**"Port already in use"**
- Default HTTPS port is 8443
- Change port in vite.config.ts if needed

**"Permission denied"**
- On Linux/macOS, you may need sudo for ports < 1024
- Use ports > 1024 (like 8443) to avoid this

### SSL Certificate Validation
```bash
# Check certificate details
openssl x509 -in ssl/cert.pem -text -noout

# Verify certificate and key match
openssl x509 -noout -modulus -in ssl/cert.pem | openssl md5
openssl rsa -noout -modulus -in ssl/key.pem | openssl md5
```

## 📋 Deployment Checklist

### Development
- [ ] SSL certificates generated
- [ ] HTTPS development server working
- [ ] No console errors related to mixed content

### Production
- [ ] Valid SSL certificate installed
- [ ] HTTPS redirect configured
- [ ] Security headers enabled
- [ ] SSL certificate auto-renewal setup
- [ ] Mixed content issues resolved
- [ ] HSTS header configured

## 🔗 Useful Resources

- [Let's Encrypt](https://letsencrypt.org/)
- [mkcert](https://github.com/FiloSottile/mkcert)
- [SSL Labs Test](https://www.ssllabs.com/ssltest/)
- [Mozilla SSL Configuration Generator](https://ssl-config.mozilla.org/)
- [OWASP Transport Layer Security](https://cheatsheetseries.owasp.org/cheatsheets/Transport_Layer_Security_Cheat_Sheet.html)

## 🆘 Support

If you encounter issues with SSL setup:

1. Check the troubleshooting section above
2. Verify your OpenSSL installation
3. Try the mkcert alternative for trusted certificates
4. Check browser console for mixed content warnings

Remember: SSL/HTTPS is essential for production applications handling user data and authentication!

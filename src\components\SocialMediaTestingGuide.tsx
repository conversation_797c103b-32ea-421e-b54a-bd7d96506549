import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Copy, ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { config, hasValidOAuthCredentials } from '@/config/environment';

interface PlatformSetup {
  name: string;
  key: keyof typeof config.oauth;
  setupUrl: string;
  redirectUri: string;
  scopes: string;
  steps: string[];
  envVars: {
    clientId: string;
    clientSecret: string;
  };
}

const SocialMediaTestingGuide: React.FC = () => {
  const { toast } = useToast();
  const [copiedText, setCopiedText] = useState<string>('');

  // Get origin safely for SSR
  const getOrigin = () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return 'http://localhost:8080';
  };

  const platforms: PlatformSetup[] = [
    {
      name: 'Instagram',
      key: 'instagram',
      setupUrl: 'https://developers.facebook.com/apps/',
      redirectUri: `${getOrigin()}/auth/instagram/callback`,
      scopes: 'user_profile,user_media',
      envVars: {
        clientId: 'VITE_INSTAGRAM_CLIENT_ID',
        clientSecret: 'VITE_INSTAGRAM_CLIENT_SECRET'
      },
      steps: [
        'Go to Facebook Developers Console',
        'Create a new app or select existing app',
        'Add "Instagram Basic Display" product',
        'Go to Instagram Basic Display > Basic Display',
        'Add OAuth Redirect URI (see below)',
        'Copy the Instagram App ID and App Secret',
        'Add to your .env file'
      ]
    },
    {
      name: 'Facebook',
      key: 'facebook',
      setupUrl: 'https://developers.facebook.com/apps/',
      redirectUri: `${getOrigin()}/auth/facebook/callback`,
      scopes: 'pages_manage_posts,pages_read_engagement,pages_show_list',
      envVars: {
        clientId: 'VITE_FACEBOOK_CLIENT_ID',
        clientSecret: 'VITE_FACEBOOK_CLIENT_SECRET'
      },
      steps: [
        'Go to Facebook Developers Console',
        'Create a new app or select existing app',
        'Add "Facebook Login" product',
        'Go to Facebook Login > Settings',
        'Add OAuth Redirect URI (see below)',
        'Copy the App ID and App Secret',
        'Add to your .env file'
      ]
    },
    {
      name: 'LinkedIn',
      key: 'linkedin',
      setupUrl: 'https://www.linkedin.com/developers/apps',
      redirectUri: `${getOrigin()}/auth/linkedin/callback`,
      scopes: 'r_liteprofile,r_emailaddress,w_member_social',
      envVars: {
        clientId: 'VITE_LINKEDIN_CLIENT_ID',
        clientSecret: 'VITE_LINKEDIN_CLIENT_SECRET'
      },
      steps: [
        'Go to LinkedIn Developers',
        'Create a new app',
        'Add "Sign In with LinkedIn" product',
        'Go to Auth tab',
        'Add OAuth 2.0 redirect URL (see below)',
        'Copy the Client ID and Client Secret',
        'Add to your .env file'
      ]
    },
    {
      name: 'Twitter/X',
      key: 'twitter',
      setupUrl: 'https://developer.twitter.com/en/portal/dashboard',
      redirectUri: `${getOrigin()}/auth/twitter/callback`,
      scopes: 'tweet.read,tweet.write,users.read',
      envVars: {
        clientId: 'VITE_TWITTER_CLIENT_ID',
        clientSecret: 'VITE_TWITTER_CLIENT_SECRET'
      },
      steps: [
        'Go to Twitter Developer Portal',
        'Create a new project and app',
        'Go to App Settings > User authentication settings',
        'Enable OAuth 2.0',
        'Add Callback URI (see below)',
        'Copy the Client ID and Client Secret',
        'Add to your .env file'
      ]
    }
  ];

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      setTimeout(() => setCopiedText(''), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const getSetupStatus = (platform: PlatformSetup) => {
    return hasValidOAuthCredentials(platform.key);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-blue-500" />
            Social Media Testing Setup Guide
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              To test real social media connections, you need to set up OAuth apps for each platform. 
              This guide will walk you through the process step by step.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Tabs defaultValue="instagram" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          {platforms.map((platform) => (
            <TabsTrigger key={platform.key} value={platform.key} className="flex items-center gap-2">
              {platform.name}
              {getSetupStatus(platform) ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertCircle className="h-3 w-3 text-orange-500" />
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {platforms.map((platform) => (
          <TabsContent key={platform.key} value={platform.key}>
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {platform.name} Setup
                    {getSetupStatus(platform) ? (
                      <Badge className="bg-green-100 text-green-800">Configured</Badge>
                    ) : (
                      <Badge className="bg-orange-100 text-orange-800">Not Configured</Badge>
                    )}
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(platform.setupUrl, '_blank')}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Developer Console
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Setup Steps:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    {platform.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">Redirect URI:</h4>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                        {platform.redirectUri}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(platform.redirectUri, 'Redirect URI')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Required Scopes:</h4>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                        {platform.scopes}
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(platform.scopes, 'Scopes')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Environment Variables:</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                        {platform.envVars.clientId}=your-{platform.key}-client-id
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(`${platform.envVars.clientId}=your-${platform.key}-client-id`, 'Client ID env var')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                        {platform.envVars.clientSecret}=your-{platform.key}-client-secret
                      </code>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(`${platform.envVars.clientSecret}=your-${platform.key}-client-secret`, 'Client Secret env var')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default SocialMediaTestingGuide;

@echo off
echo Generating SSL certificates for PulseBuzz.AI development...

openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/C=US/ST=Development/L=Local/O=PulseBuzz.AI/OU=Development/CN=localhost"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ SSL certificates generated successfully!
    echo    Key: ssl/key.pem
    echo    Cert: ssl/cert.pem
    echo.
    echo 🚀 You can now run the development server with HTTPS:
    echo    npm run dev:https
    echo.
    echo ⚠️  Note: You may see a security warning in your browser
    echo    This is normal for self-signed certificates in development
    echo    Click "Advanced" and "Proceed to localhost" to continue
) else (
    echo.
    echo ❌ Error generating SSL certificates
    echo Please ensure OpenSSL is installed and available in your PATH
    echo.
    echo Windows installation options:
    echo - Download from: https://slproweb.com/products/Win32OpenSSL.html
    echo - Or install via Chocolatey: choco install openssl
    echo - Or use Git Bash which includes OpenSSL
)

pause

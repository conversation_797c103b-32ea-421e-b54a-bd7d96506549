import React, { useState } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import PageHeader from '@/components/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { PenTool, Users, FileText, CheckCircle, AlertCircle, Sparkles, Wand2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

// Social Media Icon Components (2025 versions)
const InstagramIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" strokeWidth="2"/>
    <path d="m16 11.37-4-4-4 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
    <circle cx="17.5" cy="6.5" r="0.5" fill="currentColor"/>
  </svg>
);

const XIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const FacebookIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

const LinkedInIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

const TikTokIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
);

const YouTubeIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
);

const CreatePostMinimal: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  // Form state
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [content, setContent] = useState('');
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);

  // Available platforms with 2025 icons
  const availablePlatforms = [
    {
      id: 'instagram',
      name: 'Instagram',
      icon: <InstagramIcon />,
      limit: 2200,
      color: 'bg-gradient-to-r from-purple-500 to-pink-500 text-white',
      bgColor: 'bg-pink-50 border-pink-200'
    },
    {
      id: 'twitter',
      name: 'X (Twitter)',
      icon: <XIcon />,
      limit: 280,
      color: 'bg-black text-white',
      bgColor: 'bg-gray-50 border-gray-200'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: <FacebookIcon />,
      limit: 63206,
      color: 'bg-blue-600 text-white',
      bgColor: 'bg-blue-50 border-blue-200'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: <LinkedInIcon />,
      limit: 3000,
      color: 'bg-blue-700 text-white',
      bgColor: 'bg-blue-50 border-blue-200'
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      icon: <TikTokIcon />,
      limit: 2200,
      color: 'bg-black text-white',
      bgColor: 'bg-gray-50 border-gray-200'
    },
    {
      id: 'youtube',
      name: 'YouTube',
      icon: <YouTubeIcon />,
      limit: 5000,
      color: 'bg-red-600 text-white',
      bgColor: 'bg-red-50 border-red-200'
    }
  ];

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const getCharacterCount = () => {
    if (selectedPlatforms.length === 0) return '';
    const minLimit = Math.min(...selectedPlatforms.map(id => 
      availablePlatforms.find(p => p.id === id)?.limit || 280
    ));
    return `${content.length}/${minLimit}`;
  };

  const isContentValid = () => {
    if (selectedPlatforms.length === 0) return false;
    const minLimit = Math.min(...selectedPlatforms.map(id => 
      availablePlatforms.find(p => p.id === id)?.limit || 280
    ));
    return content.length <= minLimit;
  };

  const isFormValid = () => {
    return content.trim().length > 0 && selectedPlatforms.length > 0 && isContentValid();
  };

  // AI Content Generation
  const generateAIContent = async (prompt: string) => {
    setIsGeneratingContent(true);
    try {
      // Simulate AI content generation
      await new Promise(resolve => setTimeout(resolve, 2000));

      const aiSuggestions = [
        `🚀 Exciting news! ${prompt} - What are your thoughts on this trend? #Innovation #TechTrends`,
        `💡 Just discovered something amazing about ${prompt}. Here's what I learned and why it matters to you...`,
        `🌟 ${prompt} is changing the game! Here are 3 key insights that will transform how you think about this topic:`,
        `🔥 Hot take: ${prompt} is more important than you think. Here's why this matters for your business...`,
        `✨ ${prompt} - breaking it down into simple terms. Save this post for later! 📌`
      ];

      const randomSuggestion = aiSuggestions[Math.floor(Math.random() * aiSuggestions.length)];
      setContent(randomSuggestion);

      toast({
        title: "AI Content Generated!",
        description: "Your content has been generated. Feel free to edit it as needed.",
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Failed to generate content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingContent(false);
    }
  };

  const handleQuickGenerate = (type: string) => {
    const prompts = {
      motivational: "daily motivation and success mindset",
      business: "business growth and entrepreneurship",
      tech: "latest technology trends and innovations",
      lifestyle: "lifestyle tips and personal development",
      marketing: "social media marketing strategies"
    };

    generateAIContent(prompts[type as keyof typeof prompts] || "general content");
  };

  const handleSubmit = async () => {
    if (!isFormValid()) {
      toast({
        title: "Form Validation Error",
        description: "Please fill in all required fields and ensure content meets platform limits.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Simulate post creation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Success!",
        description: `Post created successfully for ${selectedPlatforms.length} platform(s).`,
      });

      // Reset form
      setContent('');
      setSelectedPlatforms([]);
      
      navigate('/');
    } catch (error) {
      console.error('Error creating post:', error);
      toast({
        title: "Error",
        description: "Failed to create post. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Create New Post"
              description="Create and schedule content across multiple social media platforms"
              icon={<PenTool className="w-8 h-8" />}
            />
            <div className="px-6 pb-6">
              <div className="max-w-4xl mx-auto space-y-6">
                
                {/* Platform Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Select Platforms
                    </CardTitle>
                    <CardDescription>
                      Choose which social media platforms to post to
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {availablePlatforms.map((platform) => (
                        <div
                          key={platform.id}
                          className={`p-4 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                            selectedPlatforms.includes(platform.id)
                              ? `${platform.bgColor} border-current shadow-lg transform scale-105`
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                          onClick={() => handlePlatformToggle(platform.id)}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${platform.color}`}>
                                {platform.icon}
                              </div>
                              <span className="font-semibold text-sm">{platform.name}</span>
                            </div>
                            {selectedPlatforms.includes(platform.id) && (
                              <CheckCircle className="w-5 h-5 text-green-500" />
                            )}
                          </div>
                          <div className="flex justify-between items-center">
                            <Badge variant="outline" className="text-xs font-medium">
                              {platform.limit.toLocaleString()} chars
                            </Badge>
                            {selectedPlatforms.includes(platform.id) && (
                              <Badge className="text-xs bg-green-100 text-green-800">
                                Selected
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    {selectedPlatforms.length === 0 && (
                      <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <div className="flex items-center gap-2 text-amber-800">
                          <AlertCircle className="w-4 h-4" />
                          <span className="text-sm">Please select at least one platform</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* AI Content Generation */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-purple-500" />
                      AI Content Generator
                    </CardTitle>
                    <CardDescription>
                      Generate engaging content with AI assistance
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickGenerate('motivational')}
                        disabled={isGeneratingContent}
                        className="text-xs"
                      >
                        💪 Motivational
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickGenerate('business')}
                        disabled={isGeneratingContent}
                        className="text-xs"
                      >
                        💼 Business
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickGenerate('tech')}
                        disabled={isGeneratingContent}
                        className="text-xs"
                      >
                        🚀 Tech
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickGenerate('lifestyle')}
                        disabled={isGeneratingContent}
                        className="text-xs"
                      >
                        ✨ Lifestyle
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleQuickGenerate('marketing')}
                        disabled={isGeneratingContent}
                        className="text-xs"
                      >
                        📈 Marketing
                      </Button>
                    </div>

                    {isGeneratingContent && (
                      <div className="flex items-center justify-center py-4">
                        <div className="flex items-center gap-2 text-purple-600">
                          <RefreshCw className="w-4 h-4 animate-spin" />
                          <span className="text-sm">Generating amazing content...</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Content Creation */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Content
                    </CardTitle>
                    <CardDescription>
                      Write your post content or use AI generation above
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="content">Post Content *</Label>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setContent('')}
                          className="text-xs text-muted-foreground hover:text-foreground"
                        >
                          Clear
                        </Button>
                      </div>
                      <Textarea
                        id="content"
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="What's on your mind? Share your thoughts, updates, or insights... Or use the AI generator above!"
                        className={`min-h-[140px] resize-none ${
                          !isContentValid() && content.length > 0 ? 'border-red-500' : ''
                        }`}
                      />
                      <div className="flex justify-between items-center">
                        <span className={`text-sm ${
                          !isContentValid() && content.length > 0 ? 'text-red-500' : 'text-muted-foreground'
                        }`}>
                          {getCharacterCount()}
                        </span>
                        {!isContentValid() && content.length > 0 && (
                          <span className="text-sm text-red-500">Content exceeds platform limits</span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Submit Button */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-end gap-3">
                      <Button variant="outline" onClick={() => navigate('/')}>
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleSubmit}
                        disabled={!isFormValid()}
                        className="min-w-[120px]"
                      >
                        Create Post
                      </Button>
                    </div>
                    
                    {!isFormValid() && (
                      <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                        <div className="flex items-start gap-2 text-amber-800">
                          <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          <div className="text-sm">
                            <p className="font-medium mb-1">Please complete the following:</p>
                            <ul className="list-disc list-inside space-y-1">
                              {!content.trim() && <li>Add post content</li>}
                              {selectedPlatforms.length === 0 && <li>Select at least one platform</li>}
                              {!isContentValid() && <li>Ensure content meets platform character limits</li>}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default CreatePostMinimal;

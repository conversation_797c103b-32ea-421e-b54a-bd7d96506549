#!/usr/bin/env node

/**
 * Generate self-signed SSL certificates for local development
 * This script creates SSL certificates for HTTPS development server
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const certDir = path.join(__dirname, '..', 'ssl');
const keyPath = path.join(certDir, 'key.pem');
const certPath = path.join(certDir, 'cert.pem');

// Create ssl directory if it doesn't exist
if (!fs.existsSync(certDir)) {
  fs.mkdirSync(certDir, { recursive: true });
}

// Check if certificates already exist
if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {
  console.log('✅ SSL certificates already exist');
  console.log(`   Key: ${keyPath}`);
  console.log(`   Cert: ${certPath}`);
  process.exit(0);
}

console.log('🔐 Generating self-signed SSL certificates for development...');

try {
  // Generate private key and certificate
  const opensslCmd = `openssl req -x509 -newkey rsa:4096 -keyout "${keyPath}" -out "${certPath}" -days 365 -nodes -subj "/C=US/ST=Development/L=Local/O=PulseBuzz.AI/OU=Development/CN=localhost"`;
  
  execSync(opensslCmd, { stdio: 'inherit' });
  
  console.log('✅ SSL certificates generated successfully!');
  console.log(`   Key: ${keyPath}`);
  console.log(`   Cert: ${certPath}`);
  console.log('');
  console.log('🚀 You can now run the development server with HTTPS:');
  console.log('   npm run dev:https');
  console.log('');
  console.log('⚠️  Note: You may see a security warning in your browser');
  console.log('   This is normal for self-signed certificates in development');
  console.log('   Click "Advanced" and "Proceed to localhost" to continue');
  
} catch (error) {
  console.error('❌ Error generating SSL certificates:');
  
  if (error.message.includes('openssl')) {
    console.error('');
    console.error('OpenSSL is required but not found. Please install it:');
    console.error('');
    console.error('Windows:');
    console.error('  - Download from: https://slproweb.com/products/Win32OpenSSL.html');
    console.error('  - Or install via Chocolatey: choco install openssl');
    console.error('  - Or install via Git Bash (includes OpenSSL)');
    console.error('');
    console.error('macOS:');
    console.error('  - brew install openssl');
    console.error('');
    console.error('Linux:');
    console.error('  - sudo apt-get install openssl (Ubuntu/Debian)');
    console.error('  - sudo yum install openssl (CentOS/RHEL)');
    console.error('');
    console.error('Alternative: Use mkcert for trusted local certificates:');
    console.error('  - Install mkcert: https://github.com/FiloSottile/mkcert');
    console.error('  - Run: mkcert -install && mkcert localhost 127.0.0.1');
  } else {
    console.error(error.message);
  }
  
  process.exit(1);
}

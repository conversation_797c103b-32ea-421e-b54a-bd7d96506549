
export interface EnhancedSocialPost {
  id: string;
  content: string;
  platform: string;
  scheduledFor: string;
  status: 'scheduled' | 'draft' | 'published' | 'failed' | 'pending_approval';
  mediaUrls: string[];
  hashtags: string[];
  createdAt: string;
  updatedAt: string;
  categoryId?: string;
  teamNotes?: PostNote[];
  analytics?: PostAnalytics;
}

export interface PostNote {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  createdAt: string;
}

export interface PostAnalytics {
  impressions: number;
  engagements: number;
  clicks: number;
  shares: number;
  comments: number;
  likes: number;
}

export interface BulkAction {
  type: 'schedule' | 'reschedule' | 'pause' | 'delete' | 'update_category';
  targetDate?: string;
  targetCategory?: string;
  selectedPostIds: string[];
}

export interface PlatformMetrics {
  platform: string;
  followers: number;
  engagement: number;
  posts: number;
  growth: number;
}

export interface CategoryMetrics {
  category: string;
  posts: number;
  engagement: number;
  reach: number;
}

export interface WorkspaceMember {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  avatarUrl?: string;
  lastActive: string;
}

export interface Workspace {
  id: string;
  name: string;
  description: string;
  members: WorkspaceMember[];
  createdAt: string;
  updatedAt: string;
}

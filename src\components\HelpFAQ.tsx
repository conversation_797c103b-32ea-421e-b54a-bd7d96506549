
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Mail, MessageSquare, HelpCircle } from "lucide-react";

const HelpFAQ = () => {
  const faqs = [
    {
      question: "How do I connect my social media accounts?",
      answer: "Click on any social platform card in the dashboard and follow the OAuth authentication process. You can connect one account per platform (Instagram, X, Facebook, LinkedIn)."
    },
    {
      question: "Can I schedule posts for multiple platforms at once?",
      answer: "Yes! When creating a post, you can select multiple connected platforms to post to simultaneously at your scheduled time."
    },
    {
      question: "How does the AI content generation work?",
      answer: "Our AI uses ChatGPT to analyze your topic and generate engaging captions, suggest viral topics, and recommend relevant hashtags tailored to each platform's best practices."
    },
    {
      question: "What analytics are available?",
      answer: "You can view likes, comments, shares, reach, and engagement rates for each post, along with 7-day performance charts and platform comparisons."
    },
    {
      question: "Is there a limit to how many posts I can schedule?",
      answer: "No, you can schedule unlimited posts. However, each user can only manage one set of social accounts (no multi-brand features)."
    },
    {
      question: "Can I edit or delete scheduled posts?",
      answer: "Yes, you can edit, delete, or post immediately any scheduled post from your dashboard before it goes live."
    },
    {
      question: "What happens if a post fails to publish?",
      answer: "If a post fails, you'll see a 'failed' status with an error message. You can retry the post or edit it to fix any issues."
    },
    {
      question: "Is the app mobile-friendly?",
      answer: "Yes! The app is fully responsive and works great on mobile devices, tablets, and desktops."
    }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <HelpCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Help & Support</h1>
        <p className="text-gray-600">Find answers to common questions or get in touch with our support team</p>
      </div>

      {/* Contact Support */}
      <Card>
        <CardHeader>
          <CardTitle>Need More Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button className="h-auto p-4 flex flex-col items-center space-y-2">
              <Mail className="h-6 w-6" />
              <span className="font-medium">Email Support</span>
              <span className="text-sm text-gray-600"><EMAIL></span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <MessageSquare className="h-6 w-6" />
              <span className="font-medium">Live Chat</span>
              <span className="text-sm text-gray-600">Available 9AM-5PM EST</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="space-y-2">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="border rounded-lg px-4">
                <AccordionTrigger className="text-left font-medium">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 pb-4">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      {/* Quick Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Tips for Success</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">📈 Boost Engagement</h4>
              <p className="text-sm text-gray-600">Post when your audience is most active, use trending hashtags, and ask questions to encourage comments.</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">⏰ Optimal Timing</h4>
              <p className="text-sm text-gray-600">Generally, post between 9-11 AM and 1-3 PM on weekdays for maximum reach across platforms.</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">🎯 Content Mix</h4>
              <p className="text-sm text-gray-600">Use the 80/20 rule: 80% valuable content, 20% promotional. Mix photos, videos, and text posts.</p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">📱 Platform Best Practices</h4>
              <p className="text-sm text-gray-600">Tailor content to each platform: visual for Instagram, professional for LinkedIn, concise for X.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HelpFAQ;

import React from 'react';
import { useDataInitialization, useWorkspaceCheck, useAuthState } from '@/hooks/useDataInitialization';
import { LoadingScreen } from '@/components/LoadingScreen';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertTriangle, Plus, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface DataInitializerProps {
  children: React.ReactNode;
}

export const DataInitializer: React.FC<DataInitializerProps> = ({ children }) => {
  const { user, isLoading: authLoading } = useAuthState();
  const { isInitialized, initError, retry } = useDataInitialization();
  const { needsWorkspace, createWorkspace, isLoading: workspaceLoading } = useWorkspaceCheck();
  
  const [workspaceName, setWorkspaceName] = useState('');
  const [workspaceDescription, setWorkspaceDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Show loading while checking auth
  if (authLoading) {
    return <LoadingScreen message="Checking authentication..." />;
  }

  // If no user, let the auth system handle it
  if (!user) {
    return <>{children}</>;
  }

  // Show loading while initializing data
  if (!isInitialized && !initError) {
    return <LoadingScreen message="Loading your workspace..." />;
  }

  // Show error if initialization failed
  if (initError) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
              Failed to Load Data
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600 dark:text-gray-400">
              We couldn't load your workspace data. This might be due to a network issue or server problem.
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Error: {initError}
            </p>
            <Button 
              onClick={retry}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show workspace creation form if needed
  if (needsWorkspace) {
    const handleCreateWorkspace = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!workspaceName.trim()) return;
      
      setIsCreating(true);
      try {
        await createWorkspace(workspaceName.trim(), workspaceDescription.trim() || undefined);
      } catch (error) {
        console.error('Failed to create workspace:', error);
      } finally {
        setIsCreating(false);
      }
    };

    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
              <Plus className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Welcome to PulseBuzz.AI
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Let's create your first workspace to get started
            </p>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateWorkspace} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="workspace-name">Workspace Name *</Label>
                <Input
                  id="workspace-name"
                  type="text"
                  placeholder="My Company"
                  value={workspaceName}
                  onChange={(e) => setWorkspaceName(e.target.value)}
                  required
                  disabled={isCreating || workspaceLoading}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="workspace-description">Description (Optional)</Label>
                <Textarea
                  id="workspace-description"
                  placeholder="Describe your workspace..."
                  value={workspaceDescription}
                  onChange={(e) => setWorkspaceDescription(e.target.value)}
                  disabled={isCreating || workspaceLoading}
                  rows={3}
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full"
                disabled={!workspaceName.trim() || isCreating || workspaceLoading}
              >
                {isCreating || workspaceLoading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Creating Workspace...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Workspace
                  </>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // All good, render the app
  return <>{children}</>;
};

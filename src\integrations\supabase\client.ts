// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://btpqrfmlkivpfkjmlcfn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ0cHFyZm1sa2l2cGZram1sY2ZuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMTI4ODksImV4cCI6MjA2NTY4ODg4OX0.5izI00kg44UB3BclrEza0_Cy3d_y045A7frLpTCb11o";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
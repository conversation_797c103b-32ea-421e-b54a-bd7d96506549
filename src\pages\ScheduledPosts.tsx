
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import PageHeader from '@/components/PageHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TopHeader } from '@/components/TopHeader';
import ScheduledPostsList from '@/components/ScheduledPostsList';
import { useAppStore } from '@/store/appStore';
import { useToast } from '@/hooks/use-toast';
import {
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  Edit,
  Trash2,
  Play,
  Pause
} from 'lucide-react';

interface ScheduledPost {
  id: string;
  content: string;
  platform: string;
  scheduledFor: string;
  status: string;
  mediaUrls: string[];
  hashtags: string[];
  createdAt: string;
  updatedAt: string;
}

const ScheduledPosts: React.FC = () => {
  const { scheduledPosts, deletePost, updatePost } = useAppStore();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = scheduledPosts.filter(post => {
    const matchesSearch = post.content.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending_approval':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlatformIcon = (platform: string) => {
    return platform.charAt(0).toUpperCase() + platform.slice(1);
  };

  const handleEditPost = (postId: string) => {
    // Navigate to edit page or open edit modal
    window.location.href = `/create?edit=${postId}`;
  };

  const handleDeletePost = async (postId: string) => {
    try {
      await deletePost(postId);
      toast({
        title: "Post Deleted",
        description: "The scheduled post has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: "Failed to delete the post. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handlePausePost = async (postId: string) => {
    try {
      await updatePost(postId, { status: 'draft' });
      toast({
        title: "Post Paused",
        description: "The post has been moved to drafts.",
      });
    } catch (error) {
      toast({
        title: "Pause Failed",
        description: "Failed to pause the post. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCreatePost = () => {
    window.location.href = '/create';
  };

  const handleFilter = () => {
    console.log('Opening filter options...');
  };

  const handleCalendarView = () => {
    console.log('Switching to calendar view...');
  };

  const handleSchedulePost = () => {
    console.log('Opening schedule post dialog...');
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Scheduled Posts"
              description="Manage and monitor your scheduled social media posts"
              icon={<Clock className="w-8 h-8" />}
              actions={
                <div className="flex items-center space-x-2">
                  <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20" onClick={handleFilter}>
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20" onClick={handleCalendarView}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Calendar View
                  </Button>
                  <Button className="bg-white text-blue-600 hover:bg-white/90" onClick={handleSchedulePost}>
                    <Plus className="h-4 w-4 mr-2" />
                    Schedule Post
                  </Button>
                </div>
              }
            />

            <div className="px-6 pb-6 space-y-6">
              {/* Search Controls */}
              <div className="flex items-center space-x-4">
                <div className="flex-1 max-w-sm">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search posts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

          {/* Stats Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Scheduled</p>
                    <p className="text-2xl font-bold">{scheduledPosts.filter(p => p.status === 'scheduled').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Edit className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Drafts</p>
                    <p className="text-2xl font-bold">{scheduledPosts.filter(p => p.status === 'draft').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Play className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Published</p>
                    <p className="text-2xl font-bold">{scheduledPosts.filter(p => p.status === 'published').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Pause className="h-4 w-4 text-muted-foreground" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Failed</p>
                    <p className="text-2xl font-bold">{scheduledPosts.filter(p => p.status === 'failed').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Posts List */}
          <div className="space-y-4">
            {filteredPosts.map((post) => (
              <Card key={post.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant="outline">
                          {getPlatformIcon(post.platform)}
                        </Badge>
                        <Badge className={getStatusColor(post.status)}>
                          {post.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-900 mb-2">{post.content}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>📅 {post.scheduled_for ? new Date(post.scheduled_for).toLocaleDateString() : 'Not scheduled'}</span>
                        <span>🕒 {post.scheduled_for ? new Date(post.scheduled_for).toLocaleTimeString() : ''}</span>
                        {post.hashtags && post.hashtags.length > 0 && (
                          <span>#{post.hashtags.join(' #')}</span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEditPost(post.id)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      {post.status === 'scheduled' && (
                        <Button variant="ghost" size="sm" onClick={() => handlePausePost(post.id)}>
                          <Pause className="h-4 w-4" />
                        </Button>
                      )}
                      <Button variant="ghost" size="sm" onClick={() => handleDeletePost(post.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

              <ScheduledPostsList onCreatePost={handleCreatePost} />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default ScheduledPosts;

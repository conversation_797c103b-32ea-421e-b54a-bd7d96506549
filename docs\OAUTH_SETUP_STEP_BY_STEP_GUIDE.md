# 🚀 OAuth Setup - Complete Step-by-Step Guide

## 📋 **Prerequisites Checklist**

Before starting, ensure you have:
- ✅ PulseBuzz.AI application running locally
- ✅ User account created and signed in
- ✅ Workspace set up in your application
- ✅ Access to social media developer consoles

## 🎯 **Step 1: Access OAuth Setup Page**

1. **Open your browser** and navigate to: `http://localhost:8081/oauth-setup`
2. **Sign in** if prompted
3. **Verify page loads** - you should see the OAuth credentials setup interface

**Expected Result**: You should see a page with platform cards for Instagram, Facebook, LinkedIn, and X (Twitter).

## 📱 **Step 2: Choose Your First Platform (Instagram)**

Let's start with Instagram as it's commonly used:

### **2.1 Open Instagram Developer Console**
1. **Click "Open Console"** button on the Instagram card
2. **Navigate to**: https://developers.facebook.com/apps/
3. **Sign in** with your Facebook/Meta account

### **2.2 Create a New App (if needed)**
1. **Click "Create App"**
2. **Select "Business"** as app type
3. **Enter app details**:
   - App Name: `PulseBuzz.AI Integration`
   - App Contact Email: Your email
   - Business Account: Your business account

### **2.3 Configure Instagram Basic Display**
1. **Go to "Add Products"** in left sidebar
2. **Find "Instagram Basic Display"** and click "Set Up"
3. **Click "Create New App"** in Instagram Basic Display section

### **2.4 Get Your Credentials**
1. **Go to Instagram Basic Display > Basic Display**
2. **Copy "Instagram App ID"** (this is your Client ID)
3. **Copy "Instagram App Secret"** (this is your Client Secret)

### **2.5 Configure Redirect URI**
1. **In "Instagram Basic Display" settings**
2. **Add OAuth Redirect URI**: `http://localhost:8081/auth/instagram/callback`
3. **Save changes**

## 🔐 **Step 3: Enter Credentials in PulseBuzz.AI**

### **3.1 Return to OAuth Setup Page**
1. **Go back to**: `http://localhost:8081/oauth-setup`
2. **Find the Instagram card**

### **3.2 Enter Credentials**
1. **Client ID**: Paste the Instagram App ID
2. **Client Secret**: Paste the Instagram App Secret
3. **Click "Save Credentials"**

**Expected Result**: You should see a success message and the Instagram card should show "Configured" status.

## 🔄 **Step 4: Repeat for Other Platforms**

### **Facebook Pages**
1. **Developer Console**: https://developers.facebook.com/apps/
2. **Product**: Facebook Login + Pages API
3. **Redirect URI**: `http://localhost:8081/auth/facebook/callback`

### **LinkedIn**
1. **Developer Console**: https://www.linkedin.com/developers/apps
2. **Create App** with Company Page access
3. **Redirect URI**: `http://localhost:8081/auth/linkedin/callback`

### **X (Twitter)**
1. **Developer Console**: https://developer.twitter.com/en/portal/dashboard
2. **Create Project** and App
3. **Redirect URI**: `http://localhost:8081/auth/twitter/callback`

## ✅ **Step 5: Verify Configuration**

### **5.1 Check Status**
- All configured platforms should show "Configured" badges
- Status should show "X of 4 platforms configured"

### **5.2 Test Credentials (Optional)**
1. **Look for "Test Credentials" section**
2. **Click test buttons** for each platform
3. **Verify successful connections**

## 🔧 **Step 6: Test OAuth Flow**

### **6.1 Create a Test Post**
1. **Navigate to**: `http://localhost:8081/create-post`
2. **Select a configured platform**
3. **Try to connect/authorize**

### **6.2 Verify Connection**
- OAuth flow should redirect to platform
- User should be able to authorize
- Should redirect back to your app successfully

## 🛡️ **Security Verification**

### **6.1 Check Database Storage**
Your credentials are now:
- ✅ **Encrypted** before storage
- ✅ **Isolated** by your workspace
- ✅ **Secure** from other users
- ✅ **Persistent** across sessions

### **6.2 Verify in Supabase Dashboard**
1. **Open Supabase dashboard**
2. **Go to Table Editor**
3. **Check `oauth_credentials` table**
4. **Verify encrypted entries exist**

## 🚨 **Troubleshooting Common Issues**

### **Issue: "Save Failed" Error**
**Solutions**:
1. Check if you're signed in
2. Verify workspace is set up
3. Check browser console for errors
4. Try refreshing the page

### **Issue: "Invalid Redirect URI"**
**Solutions**:
1. Verify exact redirect URI in platform console
2. Ensure no trailing slashes
3. Check HTTP vs HTTPS
4. Verify port number (8081)

### **Issue: "Credentials Not Working"**
**Solutions**:
1. Double-check Client ID and Secret
2. Verify app is in correct mode (development/production)
3. Check platform-specific permissions
4. Ensure app is approved/reviewed if required

### **Issue: OAuth Flow Fails**
**Solutions**:
1. Check redirect URI configuration
2. Verify credentials are saved correctly
3. Check platform app status
4. Review platform-specific requirements

## 📊 **Platform-Specific Notes**

### **Instagram**
- Requires Facebook Developer account
- App must be in "Live" mode for production
- Requires business verification for some features

### **Facebook**
- Same developer console as Instagram
- Requires Pages permissions for business posting
- App review required for public use

### **LinkedIn**
- Requires company page association
- Limited to company page posting
- Verification required for marketing APIs

### **X (Twitter)**
- Requires Twitter Developer account approval
- Essential access is free but limited
- Elevated access requires application

## 🎯 **Next Steps After Setup**

### **Development**
1. **Test OAuth flows** with each platform
2. **Implement posting functionality**
3. **Add error handling** for failed connections
4. **Test with different user accounts**

### **Production Preparation**
1. **Update redirect URIs** to production domains
2. **Submit apps for review** (if required)
3. **Configure production credentials**
4. **Set up monitoring** for OAuth failures

## 📞 **Getting Help**

### **Platform Support**
- **Instagram/Facebook**: Meta Developer Support
- **LinkedIn**: LinkedIn Developer Support  
- **X (Twitter)**: Twitter Developer Support

### **Application Support**
- Check browser console for errors
- Review Supabase logs
- Verify network requests
- Test with different browsers

---

## 🎉 **Success Criteria**

You've successfully configured OAuth when:
- ✅ All desired platforms show "Configured" status
- ✅ Credentials are saved in Supabase database
- ✅ OAuth flows redirect correctly
- ✅ Users can authorize your application
- ✅ Social media posting works end-to-end

**Congratulations! Your social media OAuth integration is now ready for production use!** 🚀

# 🧪 Social Accounts Page - Complete Testing Guide

## 📋 Pre-Testing Setup

### **1. Environment Check**
```bash
# Ensure your development server is running
npm run dev
# or
yarn dev

# Open browser to: http://localhost:8080 (or your dev URL)
```

### **2. Browser Setup**
- **Open Developer Tools** (F12 or Ctrl+Shift+I)
- **Go to Console tab** - Keep this open to monitor for errors
- **Clear console** (Ctrl+L) to start fresh
- **Disable popup blocker** for localhost (for OAuth testing)

---

## 🎯 Phase 1: Navigation & Initial Load Testing

### **Step 1: Navigate to Social Accounts**
1. **Click "Social Accounts"** in the sidebar navigation
2. **Expected Result**: Page loads within 2-3 seconds
3. **Check Console**: Should be clean (no red errors)
4. **Verify Elements**:
   - ✅ Blue/purple gradient header visible
   - ✅ "Social Accounts" title displayed
   - ✅ "Show Setup Guide" button present
   - ✅ OAuth Setup Progress section visible

### **Step 2: Verify Progress Indicator**
1. **Look at the header section**
2. **Expected Result**: 
   - Progress bar showing "0/4 platforms configured" (initially)
   - Progress bar at 0% (gray/empty)
   - "0 Ready" and "4 Pending" indicators
3. **Check**: No console errors during render

### **Step 3: Check Available Platforms Section**
1. **Scroll down to "Available Platforms"**
2. **Expected Result**:
   - 4 platform cards visible (Instagram, X/Twitter, LinkedIn, Facebook)
   - Each shows platform icon
   - "Demo Mode" badges on all platforms (initially)
   - Setup time estimates visible (~10 min, ~15 min, etc.)
   - "Connect" buttons on all platforms

---

## 🎯 Phase 2: Demo Mode Testing

### **Step 4: Test Demo Connection**
1. **Click "Connect" on Instagram**
2. **Expected Behavior**:
   - Button changes to "Connecting..." with disabled state
   - 2-3 second loading animation
   - Success toast notification appears
   - Demo account appears in "Connected Accounts" section

### **Step 5: Verify Demo Account Display**
1. **Check "Connected Accounts" section**
2. **Expected Result**:
   - Counter shows "(1)" connected account
   - Demo account card displays:
     - Profile picture (placeholder image)
     - Username (demo_instagram)
     - Platform name (Instagram)
     - "Connected" green badge
     - Follower count (random number)
     - "Last sync: Never"
     - Three buttons: Refresh, Settings, Disconnect

### **Step 6: Test Demo Account Actions**
1. **Click "Refresh" button**
   - Expected: Toast notification "Account Refreshed"
2. **Click "Settings" button**
   - Expected: Toast notification "Opening settings... (Feature coming soon!)"
3. **Click "Disconnect" button**
   - Expected: Account disappears, toast notification "Account Disconnected"
   - Counter returns to "(0)"

### **Step 7: Test Multiple Demo Connections**
1. **Connect 2-3 more platforms** (Facebook, LinkedIn)
2. **Expected Result**:
   - Each connection works independently
   - Connected accounts section shows all accounts
   - Available platforms section hides connected ones
   - Progress bar remains at 0% (demo mode doesn't count)

---

## 🎯 Phase 3: Setup Guide Testing

### **Step 8: Open Setup Guide**
1. **Click "Show Setup Guide" button**
2. **Expected Behavior**:
   - Button text changes to "Hide Setup Guide"
   - Setup guide section expands smoothly
   - "OAuth Setup Guide" card appears
   - 4 collapsible platform sections visible

### **Step 9: Test Platform Sections**
1. **Click on "Instagram" section**
2. **Expected Result**:
   - Section expands to show:
     - Setup time estimate (~10 min)
     - Difficulty level (Easy)
     - Step-by-step instructions
     - "Open Console" button
     - Environment variable copy button
     - Redirect URI copy button
     - Required scopes copy button

### **Step 10: Test Copy Functionality**
1. **Click "VITE_INSTAGRAM_CLIENT_ID" button**
2. **Expected Result**:
   - Toast notification "Environment variable copied to clipboard"
   - Text is actually copied (test by pasting somewhere)
3. **Test other copy buttons**:
   - Redirect URI copy
   - Scopes copy
   - .env template copy (at bottom)

### **Step 11: Test External Links**
1. **Click "Open Console" button**
2. **Expected Result**:
   - New tab opens to Facebook Developers Console
   - Original tab remains on your app
3. **Close the new tab and return**

---

## 🎯 Phase 4: OAuth Configuration Testing (Optional)

### **Step 12: Configure Real OAuth (Instagram)**
**Note**: This requires creating a Facebook Developer account

1. **Follow the setup guide instructions**:
   - Go to Facebook Developers Console
   - Create a new app
   - Add Instagram Basic Display product
   - Configure OAuth redirect URI
   - Copy Client ID and Secret

2. **Create .env file** in project root:
```env
VITE_INSTAGRAM_CLIENT_ID=your-actual-client-id
VITE_INSTAGRAM_CLIENT_SECRET=your-actual-client-secret
```

3. **Restart development server**:
```bash
# Stop server (Ctrl+C)
npm run dev
# or yarn dev
```

### **Step 13: Test OAuth Ready Status**
1. **Refresh Social Accounts page**
2. **Expected Changes**:
   - Instagram shows "OAuth Ready" green badge (not "Demo Mode")
   - Progress bar updates to "1/4 platforms configured" (25%)
   - "1 Ready" and "3 Pending" in progress indicators
   - "Test Connection" button appears for Instagram

### **Step 14: Test Connection Button**
1. **Click "Test Connection" for Instagram**
2. **Expected Behavior**:
   - Button shows loading spinner and "Testing..."
   - 2-second test simulation
   - Success toast: "Connection Test Successful"
   - No actual account created (just validation)

### **Step 15: Test Real OAuth Connection**
1. **Click "Connect" on Instagram**
2. **Expected Behavior**:
   - OAuth popup window opens
   - Instagram login page loads
   - After authorization, real account data appears
   - Real username, follower count, profile picture

---

## 🎯 Phase 5: Error Handling Testing

### **Step 16: Test Error Scenarios**
1. **Test with invalid OAuth** (if configured):
   - Modify .env with invalid credentials
   - Restart server
   - Try connecting - should show error toast

2. **Test popup blocking**:
   - Enable popup blocker
   - Try OAuth connection
   - Should handle gracefully

3. **Test network issues**:
   - Disconnect internet briefly
   - Try demo connection
   - Should show appropriate error

---

## 🎯 Phase 6: Responsive Design Testing

### **Step 17: Test Mobile View**
1. **Open Developer Tools**
2. **Click device toggle** (mobile icon)
3. **Select iPhone/Android preset**
4. **Expected Result**:
   - Sidebar collapses or adapts
   - Platform cards stack vertically
   - Progress bar scales appropriately
   - All buttons remain accessible

### **Step 18: Test Tablet View**
1. **Select iPad preset**
2. **Expected Result**:
   - 2-column grid for platforms
   - Setup guide remains readable
   - Progress bar fits well

---

## 🎯 Phase 7: Performance Testing

### **Step 19: Check Load Times**
1. **Open Network tab** in Developer Tools
2. **Refresh page**
3. **Expected Performance**:
   - Initial page load: < 3 seconds
   - Setup guide toggle: < 0.5 seconds
   - Demo connections: < 3 seconds
   - No memory leaks or excessive requests

### **Step 20: Check Console Health**
1. **Review Console tab**
2. **Expected State**:
   - No red errors
   - No yellow warnings (or minimal)
   - Clean, professional output

---

## ✅ Success Criteria Checklist

### **Navigation & UI**
- [ ] Page loads without errors
- [ ] All sections render correctly
- [ ] Progress indicator displays properly
- [ ] Setup guide expands/collapses smoothly

### **Demo Mode Functionality**
- [ ] All platforms connect in demo mode
- [ ] Demo accounts display correctly
- [ ] Account actions work (refresh, settings, disconnect)
- [ ] Multiple connections work independently

### **Setup Guide Features**
- [ ] All copy buttons work
- [ ] External links open correctly
- [ ] Platform sections expand properly
- [ ] Time estimates and difficulty show

### **OAuth Integration** (if configured)
- [ ] OAuth status detection works
- [ ] Progress bar updates correctly
- [ ] Test connection validates credentials
- [ ] Real OAuth flow completes successfully

### **Error Handling**
- [ ] Invalid scenarios handled gracefully
- [ ] Appropriate error messages shown
- [ ] No console errors during normal use
- [ ] Popup blocking handled properly

### **Responsive Design**
- [ ] Mobile view works correctly
- [ ] Tablet view adapts properly
- [ ] Desktop view is optimal
- [ ] All features accessible on all sizes

---

## 🚨 Common Issues & Solutions

### **If Progress Bar Shows NaN:**
- Check console for calculation errors
- Verify OAuth status function works
- Refresh page to reset state

### **If Demo Connections Fail:**
- Check network connectivity
- Verify toast notifications are enabled
- Clear browser cache and retry

### **If Setup Guide Doesn't Expand:**
- Check for JavaScript errors
- Verify component imports
- Test with different browser

### **If OAuth Test Fails:**
- Verify .env file configuration
- Check redirect URI matches exactly
- Ensure development server restarted

---

## 📊 Expected Test Results

**Total Test Time**: 15-30 minutes
**Success Rate**: 95%+ for demo mode, 80%+ for OAuth (depends on setup)
**Performance**: All interactions < 3 seconds
**Compatibility**: Works in Chrome, Firefox, Safari, Edge

This comprehensive testing ensures your Social Accounts page is production-ready and provides an excellent user experience for agency clients and team members.

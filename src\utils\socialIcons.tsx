import React from 'react';

export type IconSize = 'sm' | 'md' | 'lg' | 'xl';

const getSizeValue = (size: IconSize): number => {
  switch (size) {
    case 'sm':
      return 16;
    case 'md':
      return 20;
    case 'lg':
      return 24;
    case 'xl':
      return 32;
    default:
      return 20;
  }
};

// 2025 Social Media Icons - Latest designs
const InstagramIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" className="text-pink-600">
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" strokeWidth="2"/>
    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
    <circle cx="17.5" cy="6.5" r="1.5" fill="currentColor"/>
  </svg>
);

const XTwitterIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className="text-gray-900">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const FacebookIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className="text-blue-600">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
);

const LinkedInIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className="text-blue-700">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

const TikTokIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className="text-gray-900">
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
);

const YouTubeIcon = ({ size }: { size: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className="text-red-600">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
);

export const getPlatformIcon = (platform: string, size: IconSize = 'md'): React.ReactNode => {
  const iconSize = getSizeValue(size);

  switch (platform.toLowerCase()) {
    case 'instagram':
      return <InstagramIcon size={iconSize} />;
    case 'x (twitter)':
    case 'twitter':
    case 'x':
      return <XTwitterIcon size={iconSize} />;
    case 'facebook':
      return <FacebookIcon size={iconSize} />;
    case 'linkedin':
      return <LinkedInIcon size={iconSize} />;
    case 'tiktok':
      return <TikTokIcon size={iconSize} />;
    case 'youtube':
      return <YouTubeIcon size={iconSize} />;
    default:
      return <div className={`bg-gray-400 rounded`} style={{ width: iconSize, height: iconSize }}></div>;
  }
};

export const getPlatformColor = (platform: string): string => {
  switch (platform.toLowerCase()) {
    case 'instagram':
      return 'text-pink-600';
    case 'x (twitter)':
    case 'twitter':
    case 'x':
      return 'text-gray-900';
    case 'facebook':
      return 'text-blue-600';
    case 'linkedin':
      return 'text-blue-700';
    case 'tiktok':
      return 'text-gray-900';
    case 'youtube':
      return 'text-red-600';
    default:
      return 'text-gray-500';
  }
};

export const getPlatformBgColor = (platform: string): string => {
  switch (platform.toLowerCase()) {
    case 'instagram':
      return 'bg-pink-100';
    case 'x (twitter)':
    case 'twitter':
    case 'x':
      return 'bg-gray-100';
    case 'facebook':
      return 'bg-blue-100';
    case 'linkedin':
      return 'bg-blue-100';
    case 'tiktok':
      return 'bg-gray-100';
    case 'youtube':
      return 'bg-red-100';
    default:
      return 'bg-gray-100';
  }
};

export const getPlatformGradient = (platform: string): string => {
  switch (platform.toLowerCase()) {
    case 'instagram':
      return 'bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500';
    case 'x (twitter)':
    case 'twitter':
    case 'x':
      return 'bg-black';
    case 'facebook':
      return 'bg-blue-600';
    case 'linkedin':
      return 'bg-blue-700';
    case 'tiktok':
      return 'bg-gradient-to-r from-pink-500 to-cyan-500';
    case 'youtube':
      return 'bg-red-600';
    default:
      return 'bg-gray-500';
  }
};

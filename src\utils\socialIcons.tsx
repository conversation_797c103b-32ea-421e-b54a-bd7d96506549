import React from 'react';
import { FaInstagram, FaFacebookF, FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';

export type IconSize = 'sm' | 'md' | 'lg' | 'xl';

interface IconProps {
  size: number;
  style: React.CSSProperties;
}

const getSizeValue = (size: IconSize): number => {
  switch (size) {
    case 'sm':
      return 16;
    case 'md':
      return 20;
    case 'lg':
      return 24;
    case 'xl':
      return 32;
    default:
      return 20;
  }
};

export const getPlatformIcon = (platform: string, size: IconSize = 'md'): React.ReactNode => {
  const iconSize = getSizeValue(size);
  const iconProps: IconProps = { 
    size: iconSize, 
    style: { display: 'inline-block' } 
  };

  switch (platform) {
    case 'Instagram':
      return <FaInstagram {...iconProps} className="text-pink-600" />;
    case 'X (Twitter)':
      return <FaXTwitter {...iconProps} className="text-gray-900" />;
    case 'Facebook':
      return <FaFacebookF {...iconProps} className="text-blue-600" />;
    case 'LinkedIn':
      return <FaLinkedinIn {...iconProps} className="text-blue-700" />;
    default:
      return <div className={`bg-gray-400 rounded`} style={{ width: iconSize, height: iconSize }}></div>;
  }
};

export const getPlatformColor = (platform: string): string => {
  switch (platform) {
    case 'Instagram':
      return 'text-pink-600';
    case 'X (Twitter)':
      return 'text-gray-900';
    case 'Facebook':
      return 'text-blue-600';
    case 'LinkedIn':
      return 'text-blue-700';
    default:
      return 'text-gray-500';
  }
};

export const getPlatformBgColor = (platform: string): string => {
  switch (platform) {
    case 'Instagram':
      return 'bg-pink-100';
    case 'X (Twitter)':
      return 'bg-gray-100';
    case 'Facebook':
      return 'bg-blue-100';
    case 'LinkedIn':
      return 'bg-blue-100';
    default:
      return 'bg-gray-100';
  }
};

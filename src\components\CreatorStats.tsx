
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Users, Heart, MessageCircle, Share } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  trend: 'up' | 'down';
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const StatCard = ({ title, value, change, trend, icon: Icon, color }: StatCardProps) => (
  <Card className="bg-card shadow-sm">
    <CardContent className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground">{value}</p>
        </div>
        <div className={`h-12 w-12 ${color} rounded-lg flex items-center justify-center`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      <div className="mt-2 flex items-center text-sm">
        {trend === 'up' ? (
          <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
        ) : (
          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
        )}
        <span className={trend === 'up' ? 'text-green-600' : 'text-red-600'}>{change}</span>
        <span className="text-muted-foreground ml-1">from last week</span>
      </div>
    </CardContent>
  </Card>
);

export function CreatorStats() {
  const stats = [
    {
      title: "Total Followers",
      value: "12.4K",
      change: "+5.2%",
      trend: 'up' as const,
      icon: Users,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400",
    },
    {
      title: "Engagement Rate",
      value: "4.8%",
      change: "+0.9%",
      trend: 'up' as const,
      icon: Heart,
      color: "bg-pink-100 text-pink-600 dark:bg-pink-900/20 dark:text-pink-400",
    },
    {
      title: "Comments",
      value: "892",
      change: "+12.3%",
      trend: 'up' as const,
      icon: MessageCircle,
      color: "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400",
    },
    {
      title: "Shares",
      value: "234",
      change: "-2.1%",
      trend: 'down' as const,
      icon: Share,
      color: "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
}

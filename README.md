# AutoMarketer Command Center 🚀

A production-ready social media management platform with real OAuth integration, AI-powered content generation, and comprehensive analytics.

## 🌟 Features

### **Production-Ready Social Media Management**
- **Multi-Platform Support**: Connect Instagram, Twitter/X, Facebook, and LinkedIn
- **Real OAuth Integration**: Production OAuth flows with proper security
- **Content Scheduling**: Schedule posts across multiple platforms
- **AI Content Generation**: OpenAI-powered content creation
- **Advanced Analytics**: Real-time performance metrics and insights
- **Responsive Design**: Works on desktop, tablet, and mobile

### **Developer Features**
- **Production APIs**: Full API integration with demo mode fallback
- **Environment Configuration**: Comprehensive .env setup
- **Feature Flags**: Enable/disable features per deployment
- **Security First**: Proper OAuth with security best practices
- **Scalable Architecture**: Modular services for production

## 🚀 Quick Start

### Development Setup
```bash
# Clone the repository
git clone <YOUR_GIT_URL>
cd automarketer-command-center

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your OAuth credentials

# Start development server
npm run dev
```

### Production Deployment
```bash
# Build for production
npm run build

# Preview production build
npm run preview

# Deploy (example with Vercel)
npm install -g vercel
vercel --prod
```

## 🔧 Production Configuration

### OAuth Setup Required

1. **Instagram** - [Facebook Developers Console](https://developers.facebook.com/apps/)
2. **Facebook** - [Facebook Developers Console](https://developers.facebook.com/apps/)
3. **LinkedIn** - [LinkedIn Developers](https://www.linkedin.com/developers/apps)
4. **Twitter/X** - [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)

See [PRODUCTION_SETUP.md](./PRODUCTION_SETUP.md) for detailed instructions.

### Environment Variables
```env
# OAuth Credentials
VITE_INSTAGRAM_CLIENT_ID=your-instagram-client-id
VITE_FACEBOOK_CLIENT_ID=your-facebook-client-id
VITE_LINKEDIN_CLIENT_ID=your-linkedin-client-id
VITE_TWITTER_CLIENT_ID=your-twitter-client-id

# Feature Flags
VITE_ENABLE_REAL_TIME_POSTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_AI_CONTENT=true

# API Configuration
VITE_API_BASE_URL=https://your-api-domain.com/api
```

## 📊 Dashboard Features

### **Main Dashboard**
- Real-time stats overview
- Connected accounts status
- Recent posts feed
- Quick content generation

### **Content Planner**
- Calendar view for scheduled posts
- Drag-and-drop scheduling
- Platform-specific post optimization
- Bulk scheduling tools

### **Analytics**
- Engagement metrics
- Platform performance comparison
- Growth tracking
- Best posting times analysis

### **Social Accounts**
- OAuth connection management
- Account health monitoring
- Platform-specific settings
- Token refresh handling

## 🔐 Security & Production

### **OAuth Security**
- Secure token storage
- Automatic token refresh
- State parameter validation
- HTTPS enforcement

### **API Security**
- JWT authentication
- Rate limiting
- Input validation
- CORS configuration

### **Data Protection**
- Encrypted sensitive data
- Secure session management
- Privacy compliance ready
- Audit logging

## 🤖 AI Integration

### **Content Generation**
- OpenAI GPT integration
- Platform-specific optimization
- Hashtag generation
- Engagement prediction

### **Smart Scheduling**
- Optimal posting time suggestions
- Audience analysis
- Content performance prediction
- A/B testing support

## 📈 Analytics & Insights

### **Real-time Metrics**
- Post performance tracking
- Engagement rate monitoring
- Reach and impression analytics
- Click-through rate analysis

### **Growth Analytics**
- Follower growth tracking
- Engagement trend analysis
- Platform comparison
- ROI measurement

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI**: Tailwind CSS, shadcn/ui, Lucide Icons
- **State Management**: React Hooks, Context API
- **Routing**: React Router v6
- **HTTP Client**: Fetch API with custom wrapper
- **Authentication**: OAuth 2.0 flows
- **Build Tool**: Vite with TypeScript
- **Deployment**: Vercel, Netlify, or custom hosting

## 📚 Documentation

- [Production Setup Guide](./PRODUCTION_SETUP.md) - Complete deployment guide
- [API Documentation](./docs/API.md) - Backend API specifications
- [OAuth Integration](./docs/OAUTH.md) - Social media OAuth setup
- [Feature Flags](./docs/FEATURES.md) - Configuration options

## 🚀 Deployment Options

### **Vercel (Recommended)**
```bash
npm install -g vercel
vercel --prod
```

### **Netlify**
```bash
npm run build
# Upload dist/ folder
```

### **Docker**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 8080
CMD ["npm", "run", "preview"]
```

## 🔄 Development Workflow

### **Local Development**
```bash
npm run dev          # Start dev server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
```

### **Testing**
```bash
npm run test         # Run tests
npm run test:watch   # Watch mode
npm run test:coverage # Coverage report
```

## 📞 Support & Contributing

### **Getting Help**
- Check [Issues](https://github.com/your-repo/issues) for common problems
- Review [Production Setup Guide](./PRODUCTION_SETUP.md)
- Test with demo mode first

### **Contributing**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Project URL**: https://lovable.dev/projects/f5cfede3-0844-449e-9e0f-9be2ae937c12

## 💻 Development with Lovable

### **Using Lovable Platform**
Simply visit the [Lovable Project](https://lovable.dev/projects/f5cfede3-0844-449e-9e0f-9be2ae937c12) and start prompting. Changes made via Lovable will be committed automatically to this repo.

### **Local Development**
If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

Requirements: Node.js & npm - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

### **GitHub Integration**
- **Direct editing**: Navigate to files and click the "Edit" button (pencil icon)
- **GitHub Codespaces**: Click "Code" → "Codespaces" → "New codespace"

### **Lovable Deployment**
For quick deployment: Open [Lovable](https://lovable.dev/projects/f5cfede3-0844-449e-9e0f-9be2ae937c12) → Share → Publish

### **Custom Domain**
Connect a custom domain: Project → Settings → Domains → Connect Domain
[Learn more](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

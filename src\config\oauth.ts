// OAuth configuration
export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string;
  authUrl: string;
  tokenUrl: string;
}

export interface OAuthPlatformConfig {
  instagram: OAuthConfig;
  facebook: OAuthConfig;
  linkedin: OAuthConfig;
  twitter: OAuthConfig;
}

const getOrigin = (): string => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return 'http://localhost:8080'; // fallback for SSR
};

// Helper function to get credentials (from env or sessionStorage for testing)
const getCredential = (envKey: string): string => {
  // First check environment variables
  const envValue = import.meta.env[envKey];
  if (envValue && !envValue.startsWith('your-')) {
    return envValue;
  }

  // Then check sessionStorage for testing credentials
  if (typeof window !== 'undefined') {
    const testValue = sessionStorage.getItem(envKey);
    if (testValue && !testValue.startsWith('your-')) {
      return testValue;
    }
  }

  return '';
};

export const config = {
  oauth: {
    instagram: {
      clientId: getCredential('VITE_INSTAGRAM_CLIENT_ID'),
      clientSecret: getCredential('VITE_INSTAGRAM_CLIENT_SECRET'),
      redirectUri: `${getOrigin()}/auth/instagram/callback`,
      scope: 'user_profile,user_media',
      authUrl: 'https://api.instagram.com/oauth/authorize',
      tokenUrl: 'https://api.instagram.com/oauth/access_token',
    },
    facebook: {
      clientId: getCredential('VITE_FACEBOOK_CLIENT_ID'),
      clientSecret: getCredential('VITE_FACEBOOK_CLIENT_SECRET'),
      redirectUri: `${getOrigin()}/auth/facebook/callback`,
      scope: 'pages_manage_posts,pages_read_engagement,pages_show_list',
      authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
    },
    linkedin: {
      clientId: getCredential('VITE_LINKEDIN_CLIENT_ID'),
      clientSecret: getCredential('VITE_LINKEDIN_CLIENT_SECRET'),
      redirectUri: `${getOrigin()}/auth/linkedin/callback`,
      scope: 'r_liteprofile,r_emailaddress,w_member_social',
      authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
    },
    twitter: {
      clientId: getCredential('VITE_TWITTER_CLIENT_ID'),
      clientSecret: getCredential('VITE_TWITTER_CLIENT_SECRET'),
      redirectUri: `${getOrigin()}/auth/twitter/callback`,
      scope: 'tweet.read,tweet.write,users.read',
      authUrl: 'https://twitter.com/i/oauth2/authorize',
      tokenUrl: 'https://api.twitter.com/2/oauth2/token',
    },
  },
};

// Helper function to check if OAuth credentials are valid
export const hasValidOAuthCredentials = (platform: keyof typeof config.oauth): boolean => {
  const platformConfig = config.oauth[platform];
  return !!(
    platformConfig.clientId &&
    platformConfig.clientSecret &&
    !platformConfig.clientId.startsWith('your-') &&
    !platformConfig.clientSecret.startsWith('your-')
  );
};

// Generate OAuth authorization URL
export const generateOAuthUrl = (platform: keyof typeof config.oauth, state?: string): string => {
  const platformConfig = config.oauth[platform];
  if (!platformConfig) {
    throw new Error(`OAuth configuration not found for platform: ${platform}`);
  }

  const stateParam = state || Math.random().toString(36).substring(2);

  const params = new URLSearchParams({
    client_id: platformConfig.clientId,
    redirect_uri: platformConfig.redirectUri,
    scope: platformConfig.scope,
    response_type: 'code',
    state: stateParam
  });

  // Platform-specific parameters
  if (platform === 'twitter') {
    params.append('code_challenge', 'challenge');
    params.append('code_challenge_method', 'plain');
  }

  return `${platformConfig.authUrl}?${params.toString()}`;
};

import { getPlatformIcon } from '@/utils/socialIcons';

// Get platform display information
export const getPlatformInfo = (platform: keyof typeof config.oauth) => {
  const platformDetails = {
    instagram: {
      displayName: 'Instagram',
      icon: getPlatformIcon('instagram', 'md'),
      color: 'bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500',
      description: 'Connect your Instagram account to share photos and stories'
    },
    facebook: {
      displayName: 'Facebook',
      icon: getPlatformIcon('facebook', 'md'),
      color: 'bg-blue-600',
      description: 'Connect your Facebook page to share posts and engage with followers'
    },
    linkedin: {
      displayName: 'LinkedIn',
      icon: getPlatformIcon('linkedin', 'md'),
      color: 'bg-blue-700',
      description: 'Connect your LinkedIn profile to share professional content'
    },
    twitter: {
      displayName: 'X (Twitter)',
      icon: getPlatformIcon('x (twitter)', 'md'),
      color: 'bg-black',
      description: 'Connect your X (Twitter) account to share posts and engage with your audience'
    }
  };

  return platformDetails[platform] || {
    displayName: platform,
    icon: '🔗',
    color: 'bg-gray-500',
    description: `Connect your ${platform} account`
  };
};

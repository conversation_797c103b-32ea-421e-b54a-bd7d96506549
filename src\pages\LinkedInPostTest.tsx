import React from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import LinkedInPostTest from '@/components/LinkedInPostTest';

const LinkedInPostTestPage: React.FC = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          <main className="flex-1 p-6">
            <div className="max-w-2xl mx-auto">
              <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">LinkedIn Posting Test</h1>
                <p className="text-gray-600">
                  Test your LinkedIn OAuth integration by posting a message.
                </p>
              </div>
              
              <LinkedInPostTest />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default LinkedInPostTestPage;


import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import PageHeader from '@/components/PageHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TopHeader } from '@/components/TopHeader';
import SocialInbox from '@/components/SocialInbox';
import {
  MessageSquare,
  Send,
  Search,
  MoreHorizontal,
  Star,
  Archive,
  Inbox
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  platform: string;
  type: 'comment' | 'mention' | 'message';
  read: boolean;
  starred: boolean;
}

const SocialInboxPage: React.FC = () => {
  const [messages] = useState<Message[]>([
    {
      id: '1',
      content: 'Love your latest post about sustainable business practices! Very insightful.',
      author: '<PERSON>',
      createdAt: '2024-01-15T10:30:00Z',
      platform: 'LinkedIn',
      type: 'comment',
      read: false,
      starred: false
    },
    {
      id: '2',
      content: 'Hi! I have a question about your services. Could you DM me?',
      author: 'Mike Chen',
      createdAt: '2024-01-15T09:15:00Z',
      platform: 'Twitter',
      type: 'mention',
      read: false,
      starred: true
    },
    {
      id: '3',
      content: 'Thank you for the quick response! Really appreciate the customer service.',
      author: 'Emma Wilson',
      createdAt: '2024-01-15T08:45:00Z',
      platform: 'Facebook',
      type: 'message',
      read: true,
      starred: false
    }
  ]);

  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [replyText, setReplyText] = useState('');

  const unreadCount = messages.filter(m => !m.read).length;

  const handleSelectMessage = (message: Message) => {
    setSelectedMessage(message);
  };

  const handleSendReply = () => {
    if (replyText.trim() && selectedMessage) {
      console.log('Sending reply:', replyText, 'to:', selectedMessage.author);
      setReplyText('');
    }
  };

  const handleStarMessage = (messageId: string) => {
    console.log('Star message:', messageId);
  };

  const handleArchiveMessage = (messageId: string) => {
    console.log('Archive message:', messageId);
  };

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'comment':
        return 'bg-blue-100 text-blue-800';
      case 'mention':
        return 'bg-green-100 text-green-800';
      case 'message':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'Twitter':
        return 'bg-sky-100 text-sky-800';
      case 'Facebook':
        return 'bg-blue-100 text-blue-800';
      case 'LinkedIn':
        return 'bg-indigo-100 text-indigo-800';
      case 'Instagram':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Social Inbox"
              description="Manage messages and interactions from all your social media platforms"
              icon={<Inbox className="w-8 h-8" />}
              actions={
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/20">
                    {unreadCount} unread
                  </Badge>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search messages..."
                      className="pl-10 w-64 bg-white/10 border-white/20 text-white placeholder:text-white/70"
                    />
                  </div>
                </div>
              }
            />

            <div className="px-6 pb-6">

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Messages List */}
            <div className="lg:col-span-1 space-y-4">
              <Card>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {messages.map((message) => (
                      <div 
                        key={message.id} 
                        className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                          selectedMessage?.id === message.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        } ${!message.read ? 'bg-blue-25' : ''}`}
                        onClick={() => handleSelectMessage(message)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium">
                              {message.author.charAt(0)}
                            </div>
                            <div>
                              <p className="text-sm font-medium">{message.author}</p>
                              <div className="flex items-center gap-1 mt-1">
                                <Badge className={getPlatformColor(message.platform)} variant="secondary">
                                  {message.platform}
                                </Badge>
                                <Badge className={getMessageTypeColor(message.type)} variant="secondary">
                                  {message.type}
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            {message.starred && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                            {!message.read && <div className="w-2 h-2 bg-blue-500 rounded-full" />}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-2 mb-2">{message.content}</p>
                        <p className="text-xs text-gray-400">
                          {new Date(message.createdAt).toLocaleDateString()} at {new Date(message.createdAt).toLocaleTimeString()}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Message Detail */}
            <div className="lg:col-span-2">
              {selectedMessage ? (
                <Card className="h-full">
                  <CardContent className="p-0 h-full flex flex-col">
                    {/* Message Header */}
                    <div className="p-4 border-b bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium">
                            {selectedMessage.author.charAt(0)}
                          </div>
                          <div>
                            <p className="font-medium">{selectedMessage.author}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge className={getPlatformColor(selectedMessage.platform)} variant="secondary">
                                {selectedMessage.platform}
                              </Badge>
                              <Badge className={getMessageTypeColor(selectedMessage.type)} variant="secondary">
                                {selectedMessage.type}
                              </Badge>
                              <span className="text-xs text-gray-500">
                                {new Date(selectedMessage.createdAt).toLocaleDateString()} at {new Date(selectedMessage.createdAt).toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleStarMessage(selectedMessage.id)}
                          >
                            <Star className={`h-4 w-4 ${selectedMessage.starred ? 'text-yellow-500 fill-current' : ''}`} />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleArchiveMessage(selectedMessage.id)}
                          >
                            <Archive className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Message Content */}
                    <div className="flex-1 p-4">
                      <div className="bg-white border rounded-lg p-4">
                        <p className="text-gray-900">{selectedMessage.content}</p>
                      </div>
                    </div>

                    {/* Reply Section */}
                    <div className="p-4 border-t bg-gray-50">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Type your reply..."
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSendReply()}
                          className="flex-1"
                        />
                        <Button onClick={handleSendReply} disabled={!replyText.trim()}>
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="h-full">
                  <CardContent className="p-8 h-full flex items-center justify-center">
                    <div className="text-center">
                      <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Select a message to view details</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

              <SocialInbox />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default SocialInboxPage;

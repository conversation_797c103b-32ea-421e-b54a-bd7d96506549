# ⚡ Quick Test Checklist - Social Accounts Page

## 🚀 5-Minute Quick Test

### **Setup (30 seconds)**
1. Open browser to your dev server (localhost:8080)
2. Open Developer Tools (F12) → Console tab
3. Clear console (Ctrl+L)

### **Basic Functionality (2 minutes)**
1. **Navigate**: Click "Social Accounts" in sidebar
   - ✅ Page loads without console errors
   - ✅ Progress bar shows "0/4 platforms configured"

2. **Demo Connection**: Click "Connect" on Instagram
   - ✅ <PERSON><PERSON> shows "Connecting..." for 2-3 seconds
   - ✅ Success toast appears
   - ✅ Demo account appears in Connected Accounts section

3. **Account Actions**: Test buttons on connected account
   - ✅ "Refresh" → Shows toast notification
   - ✅ "Settings" → Shows toast notification  
   - ✅ "Disconnect" → Account disappears

### **Setup Guide (2 minutes)**
4. **Open Guide**: Click "Show Setup Guide"
   - ✅ Guide expands smoothly
   - ✅ 4 platform sections visible

5. **Test Copy**: Click "VITE_INSTAGRAM_CLIENT_ID" button
   - ✅ Toast shows "copied to clipboard"
   - ✅ Text actually copied (test by pasting)

6. **External Link**: Click "Open Console" for Instagram
   - ✅ New tab opens to Facebook Developers
   - ✅ Original tab stays on your app

### **Final Check (30 seconds)**
7. **Console Health**: Check Developer Tools console
   - ✅ No red errors
   - ✅ No critical warnings

---

## 🔧 15-Minute Comprehensive Test

### **All Demo Platforms (5 minutes)**
1. Connect all 4 platforms one by one
2. Verify each creates unique demo account
3. Test all account action buttons
4. Disconnect all accounts

### **Setup Guide Deep Dive (5 minutes)**
1. Expand all 4 platform sections
2. Test all copy buttons in each section
3. Verify time estimates show correctly
4. Test .env template copy at bottom

### **Responsive Design (3 minutes)**
1. Toggle device view in Developer Tools
2. Test mobile view (iPhone preset)
3. Test tablet view (iPad preset)
4. Verify all features work on all sizes

### **Error Scenarios (2 minutes)**
1. Try connecting while already connected
2. Test with popup blocker enabled
3. Verify graceful error handling

---

## 🎯 30-Minute Full OAuth Test

### **Prerequisites**
- Facebook Developer account
- 20-30 minutes for OAuth setup
- Willingness to create test app

### **OAuth Setup Steps**
1. **Create Facebook App** (10 minutes)
   - Go to developers.facebook.com
   - Create new app → Consumer type
   - Add Instagram Basic Display product

2. **Configure OAuth** (5 minutes)
   - Add redirect URI: `http://localhost:8080/auth/instagram/callback`
   - Add test users (your Instagram account)
   - Copy Client ID and Secret

3. **Update Environment** (2 minutes)
   ```env
   VITE_INSTAGRAM_CLIENT_ID=your-actual-client-id
   VITE_INSTAGRAM_CLIENT_SECRET=your-actual-client-secret
   ```
   - Restart dev server

4. **Test Real OAuth** (10 minutes)
   - Verify "OAuth Ready" badge appears
   - Progress bar updates to 25%
   - Test "Test Connection" button
   - Try real OAuth connection
   - Verify real account data appears

### **OAuth Success Indicators**
- ✅ Instagram shows "OAuth Ready" (not "Demo Mode")
- ✅ Progress bar: "1/4 platforms configured"
- ✅ "Test Connection" button works
- ✅ Real OAuth popup opens Instagram login
- ✅ Real account data populates after authorization

---

## 🚨 Red Flags to Watch For

### **Critical Issues**
- ❌ Console shows red errors
- ❌ Page doesn't load or crashes
- ❌ Progress bar shows "NaN" or undefined
- ❌ Demo connections fail completely
- ❌ Setup guide doesn't expand

### **Warning Signs**
- ⚠️ Slow loading (>5 seconds)
- ⚠️ Multiple yellow console warnings
- ⚠️ Buttons don't respond to clicks
- ⚠️ Toast notifications don't appear
- ⚠️ Copy functionality doesn't work

### **Minor Issues**
- 🔸 Styling inconsistencies
- 🔸 Text alignment problems
- 🔸 Icon sizing issues
- 🔸 Mobile layout quirks

---

## 📱 Device-Specific Testing

### **Desktop (Chrome/Firefox/Safari)**
- Full functionality expected
- All features should work perfectly
- Optimal layout and performance

### **Mobile (iPhone/Android)**
- Sidebar navigation adapts
- Platform cards stack vertically
- Touch targets are appropriate size
- OAuth popups work in mobile browsers

### **Tablet (iPad)**
- 2-column grid for platforms
- Setup guide remains readable
- Progress bar scales appropriately

---

## 🎯 Success Benchmarks

### **Performance Targets**
- Page load: < 3 seconds
- Demo connection: < 3 seconds
- Setup guide toggle: < 0.5 seconds
- Copy operations: Instant

### **Functionality Targets**
- Demo mode: 100% success rate
- Setup guide: All features work
- Copy operations: 100% success rate
- OAuth (if configured): 90%+ success rate

### **User Experience Targets**
- No console errors during normal use
- Intuitive navigation flow
- Clear feedback for all actions
- Professional appearance

---

## 🔄 Quick Troubleshooting

### **If Demo Connections Fail:**
1. Check network connectivity
2. Clear browser cache
3. Restart development server
4. Try different browser

### **If Progress Bar Shows Errors:**
1. Check console for calculation errors
2. Refresh page to reset state
3. Verify OAuth status function works

### **If Setup Guide Issues:**
1. Check for JavaScript errors in console
2. Verify component imports
3. Test with browser refresh

### **If OAuth Test Fails:**
1. Verify .env file exists and has correct values
2. Ensure development server was restarted
3. Check redirect URI matches exactly
4. Confirm app is in development mode

---

## ✅ Test Completion Checklist

- [ ] **Basic Navigation**: Page loads and navigates correctly
- [ ] **Demo Mode**: At least one platform connects successfully
- [ ] **Account Management**: Refresh, Settings, Disconnect work
- [ ] **Setup Guide**: Expands and copy functions work
- [ ] **Progress Tracking**: Shows correct counts and percentages
- [ ] **Console Clean**: No red errors during testing
- [ ] **Responsive**: Works on mobile/tablet views
- [ ] **Performance**: All interactions feel responsive

**Minimum for "Pass"**: 6/8 items checked
**Target for "Excellent"**: 8/8 items checked

This testing approach ensures your Social Accounts page is robust, user-friendly, and ready for real-world agency use!

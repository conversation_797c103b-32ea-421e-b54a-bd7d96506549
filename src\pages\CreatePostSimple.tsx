import React from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import PageHeader from '@/components/PageHeader';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PenTool } from 'lucide-react';

const CreatePostSimple: React.FC = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Create New Post"
              description="Create and schedule content across multiple social media platforms"
              icon={<PenTool className="w-8 h-8" />}
            />
            <div className="px-6 pb-6">
              <div className="max-w-4xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle>Simple Create Post Page</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>This is a simplified version of the Create Post page for debugging.</p>
                    <p>If you can see this, the basic page structure is working.</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default CreatePostSimple;

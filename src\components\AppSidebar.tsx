
import { useState } from "react";
import { useLocation, <PERSON>, useNavigate } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  Home,
  Calendar,
  BarChart3,
  Users,
  Settings,
  HelpCircle,
  PenTool,
  Image,
  Clock,
  Zap,
  ChevronDown,
  ChevronRight,
  Folder,
  CheckSquare,
  TrendingUp,
  UserCheck,
  Inbox,
  LogOut,
  User,
  Key,
  type LucideIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/SupabaseAuthContext";

interface MenuItem {
  title: string;
  url: string;
  icon: LucideIcon;
  badge?: string;
}

const mainMenuItems: MenuItem[] = [
  {
    title: "Dashboard",
    url: "/",
    icon: Home,
  },
  {
    title: "Content Planner",
    url: "/content-planner",
    icon: Calendar,
  },
  {
    title: "Create Post",
    url: "/create",
    icon: PenTool,
  },
  {
    title: "Media Library",
    url: "/media",
    icon: Image,
  },
  {
    title: "Scheduled Posts",
    url: "/scheduled",
    icon: Clock,
  },
];

const contentItems: MenuItem[] = [
  {
    title: "Content Categories",
    url: "/categories",
    icon: Folder,
  },
  {
    title: "Bulk Manager",
    url: "/bulk-manager",
    icon: CheckSquare,
  },
];

const analyticsItems: MenuItem[] = [
  {
    title: "Analytics",
    url: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Advanced Analytics",
    url: "/advanced-analytics",
    icon: TrendingUp,
  },
  {
    title: "Performance",
    url: "/performance",
    icon: Zap,
  },
];

const engagementItems: MenuItem[] = [
  {
    title: "Social Inbox",
    url: "/inbox",
    icon: Inbox,
  },
  {
    title: "Team Collaboration",
    url: "/collaboration",
    icon: UserCheck,
  },
];

const accountItems: MenuItem[] = [
  {
    title: "Social Accounts",
    url: "/accounts",
    icon: Users,
  },
  {
    title: "OAuth Setup",
    url: "/oauth-setup",
    icon: Key,
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
  },
  {
    title: "Help & Support",
    url: "/help",
    icon: HelpCircle,
  },
];

export function AppSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const [expandedGroups, setExpandedGroups] = useState({
    main: true,
    content: true,
    analytics: true,
    engagement: true,
    account: true,
  });

  const toggleGroup = (group: keyof typeof expandedGroups) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };

  const isActive = (url: string) => location.pathname === url;

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      console.error('Sign out failed:', error);
    }
  };

  const getUserInitials = (name: string | undefined) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserDisplayName = () => {
    if (!user) return '';
    return user.user_metadata?.full_name ||
           user.user_metadata?.name ||
           user.email?.split('@')[0] ||
           'User';
  };

  const getUserAvatar = () => {
    if (!user) return null;
    return user.user_metadata?.avatar_url ||
           user.user_metadata?.picture ||
           null;
  };

  const renderMenuItems = (items: MenuItem[]) => (
    <SidebarMenu className="space-y-1 px-3">
      {items.map((item) => (
        <SidebarMenuItem key={item.title}>
          <SidebarMenuButton
            asChild
            className={`w-full px-3 py-2.5 rounded-lg transition-all duration-200 hover:bg-accent hover:text-accent-foreground ${
              isActive(item.url)
                ? 'bg-primary/10 text-primary border-r-2 border-primary'
                : 'text-foreground'
            }`}
          >
            <Link to={item.url} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <item.icon className={`h-5 w-5 ${isActive(item.url) ? 'text-primary' : 'text-muted-foreground'}`} />
                <span className="font-medium text-sm">{item.title}</span>
              </div>
              {item.badge && (
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 hover:bg-green-100">
                  {item.badge}
                </Badge>
              )}
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      ))}
    </SidebarMenu>
  );

  return (
    <Sidebar className="border-r border-gray-200 bg-white">
      <SidebarHeader className="border-b border-gray-100 p-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-xl overflow-hidden flex items-center justify-center bg-white border border-gray-200">
            <img
              src="/logo.jpg"
              alt="PulseBuzz.AI Logo"
              className="w-8 h-8 object-contain"
              onError={(e) => {
                // Fallback to gradient background with initials if logo fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.parentElement!.innerHTML = '<span class="text-blue-600 font-bold text-lg">PB</span>';
                target.parentElement!.className = 'w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center';
              }}
            />
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">PulseBuzz.AI</h1>
            <p className="text-xs text-muted-foreground leading-tight">The Pulse of Your Social Media</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="py-4">
        <SidebarGroup>
          <SidebarGroupLabel className="px-6 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider flex items-center justify-between">
            <span>Main</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => toggleGroup('main')}
            >
              {expandedGroups.main ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          </SidebarGroupLabel>
          {expandedGroups.main && (
            <SidebarGroupContent>
              {renderMenuItems(mainMenuItems)}
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-6 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider flex items-center justify-between">
            <span>Content Management</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => toggleGroup('content')}
            >
              {expandedGroups.content ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          </SidebarGroupLabel>
          {expandedGroups.content && (
            <SidebarGroupContent>
              {renderMenuItems(contentItems)}
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-6 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider flex items-center justify-between">
            <span>Analytics</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => toggleGroup('analytics')}
            >
              {expandedGroups.analytics ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          </SidebarGroupLabel>
          {expandedGroups.analytics && (
            <SidebarGroupContent>
              {renderMenuItems(analyticsItems)}
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-6 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider flex items-center justify-between">
            <span>Engagement</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => toggleGroup('engagement')}
            >
              {expandedGroups.engagement ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          </SidebarGroupLabel>
          {expandedGroups.engagement && (
            <SidebarGroupContent>
              {renderMenuItems(engagementItems)}
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-6 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider flex items-center justify-between">
            <span>Account</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-5 w-5 p-0"
              onClick={() => toggleGroup('account')}
            >
              {expandedGroups.account ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
            </Button>
          </SidebarGroupLabel>
          {expandedGroups.account && (
            <SidebarGroupContent>
              {renderMenuItems(accountItems)}
            </SidebarGroupContent>
          )}
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-100 p-4">
        {user ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-muted rounded-lg">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                {getUserAvatar() ? (
                  <img
                    src={getUserAvatar()!}
                    alt={getUserDisplayName()}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-white font-semibold text-sm">
                    {getUserInitials(getUserDisplayName())}
                  </span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">{getUserDisplayName()}</p>
                <p className="text-xs text-muted-foreground">
                  {user.app_metadata?.provider === 'google' ? 'Google Account' : 'Email Account'}
                </p>
              </div>
            </div>
            <Button
              onClick={handleSignOut}
              variant="outline"
              size="sm"
              className="w-full justify-start text-muted-foreground hover:text-foreground"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            <Button
              asChild
              variant="default"
              size="sm"
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              <Link to="/login">
                <User className="h-4 w-4 mr-2" />
                Sign In
              </Link>
            </Button>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}

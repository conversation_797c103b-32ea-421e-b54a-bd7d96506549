
import React from 'react';
import { Menu } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useSidebar } from "@/components/ui/sidebar"
import { UserMenu } from './UserMenu';

export function TopHeader() {
  const { toggleSidebar } = useSidebar()

  return (
    <header className="h-16 border-b bg-background flex items-center justify-between px-6">
      {/* Left side with SidebarTrigger */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={toggleSidebar}>
          <Menu className="w-5 h-5" />
        </Button>
      </div>

      {/* Right side with user menu */}
      <div className="flex items-center gap-4">
        <UserMenu />
      </div>
    </header>
  );
}

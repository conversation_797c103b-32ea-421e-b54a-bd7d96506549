-- Safe Fix for Workspace Creation Issues
-- This version handles existing policies gracefully

-- Step 1: Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT,
  first_name TEXT,
  last_name TEX<PERSON>,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Enable RLS on profiles (safe)
DO $$ 
BEGIN
  ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
EXCEPTION 
  WHEN OTHERS THEN 
    NULL; -- Table might already have RLS enabled
END $$;

-- Step 3: Safely drop and recreate workspace policies
DO $$ 
BEGIN
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Users can view workspaces they own or are members of" ON public.workspaces;
  DROP POLICY IF EXISTS "Users can create their own workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Workspace owners can update their workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Workspace owners can delete their workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Users can view their own workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Users can create workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Users can update their own workspaces" ON public.workspaces;
  DROP POLICY IF EXISTS "Users can delete their own workspaces" ON public.workspaces;
EXCEPTION 
  WHEN OTHERS THEN 
    NULL; -- Policies might not exist
END $$;

-- Create simple workspace policies
CREATE POLICY "Users can view their own workspaces"
  ON public.workspaces
  FOR SELECT
  USING (owner_id = auth.uid());

CREATE POLICY "Users can create workspaces"
  ON public.workspaces
  FOR INSERT
  WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Users can update their own workspaces"
  ON public.workspaces
  FOR UPDATE
  USING (owner_id = auth.uid());

CREATE POLICY "Users can delete their own workspaces"
  ON public.workspaces
  FOR DELETE
  USING (owner_id = auth.uid());

-- Step 4: Fix workspace_members policies
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can view workspace members for workspaces they belong to" ON public.workspace_members;
  DROP POLICY IF EXISTS "Workspace owners and admins can manage members" ON public.workspace_members;
  DROP POLICY IF EXISTS "Users can view workspace members" ON public.workspace_members;
  DROP POLICY IF EXISTS "Users can manage workspace members" ON public.workspace_members;
EXCEPTION 
  WHEN OTHERS THEN 
    NULL;
END $$;

CREATE POLICY "Users can view workspace members"
  ON public.workspace_members
  FOR SELECT
  USING (
    user_id = auth.uid() OR
    workspace_id IN (SELECT id FROM public.workspaces WHERE owner_id = auth.uid())
  );

CREATE POLICY "Users can manage workspace members"
  ON public.workspace_members
  FOR ALL
  USING (
    workspace_id IN (SELECT id FROM public.workspaces WHERE owner_id = auth.uid())
  );

-- Step 5: Create profiles policies
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
  DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
  DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
EXCEPTION 
  WHEN OTHERS THEN 
    NULL;
END $$;

CREATE POLICY "Users can view all profiles"
  ON public.profiles
  FOR SELECT
  USING (true);

CREATE POLICY "Users can update their own profile"
  ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON public.profiles
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Step 6: Create function to handle new user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, first_name, last_name)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data ->> 'first_name',
    NEW.raw_user_meta_data ->> 'last_name'
  );
  RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Step 7: Create trigger for profile creation
DROP TRIGGER IF EXISTS on_auth_user_created_profile ON auth.users;
CREATE TRIGGER on_auth_user_created_profile
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 8: Create updated_at trigger for profiles
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Step 9: Grant permissions
GRANT ALL ON public.profiles TO authenticated;

-- Step 10: Create profile for existing users (if any)
INSERT INTO public.profiles (id, email, first_name, last_name)
SELECT 
  id, 
  email,
  raw_user_meta_data ->> 'first_name',
  raw_user_meta_data ->> 'last_name'
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- Success message
SELECT 'Workspace creation fix applied successfully!' as result;

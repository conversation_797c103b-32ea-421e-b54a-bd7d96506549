-- Check Database Schema and Diagnose Issues
-- Run this in Supabase SQL Editor to see current state

-- 1. Check if profiles table exists
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'profiles'
ORDER BY ordinal_position;

-- 2. Check if workspaces table exists and its structure
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name = 'workspaces'
ORDER BY ordinal_position;

-- 3. Check current RLS policies on workspaces
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'workspaces';

-- 4. Check current RLS policies on profiles (if table exists)
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename = 'profiles';

-- 5. Check if there are any existing users
SELECT 
  id,
  email,
  created_at,
  raw_user_meta_data
FROM auth.users
LIMIT 5;

-- 6. Check if there are any existing workspaces
SELECT 
  id,
  name,
  owner_id,
  created_at
FROM public.workspaces
LIMIT 5;

-- 7. Check if there are any existing profiles
SELECT 
  id,
  email,
  first_name,
  last_name,
  created_at
FROM public.profiles
LIMIT 5;

-- 8. List all public tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;

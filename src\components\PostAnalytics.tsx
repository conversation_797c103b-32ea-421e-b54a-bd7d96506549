
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Heart, MessageCircle, Share, Eye, TrendingUp } from "lucide-react";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

interface PostAnalyticsProps {
  postId: string;
  platform: string;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    reach: number;
    engagement_rate: number;
  };
  chartData?: Array<{
    date: string;
    likes: number;
    comments: number;
    shares: number;
    reach: number;
  }>;
}

const PostAnalytics = ({ platform, metrics, chartData }: PostAnalyticsProps) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getEngagementColor = (rate: number) => {
    if (rate >= 5) return "text-green-600 bg-green-100";
    if (rate >= 2) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const currentData = chartData || [
    { date: '7d ago', likes: Math.floor(metrics.likes * 0.1), comments: Math.floor(metrics.comments * 0.1), shares: Math.floor(metrics.shares * 0.1), reach: Math.floor(metrics.reach * 0.1) },
    { date: '6d ago', likes: Math.floor(metrics.likes * 0.2), comments: Math.floor(metrics.comments * 0.2), shares: Math.floor(metrics.shares * 0.2), reach: Math.floor(metrics.reach * 0.2) },
    { date: '5d ago', likes: Math.floor(metrics.likes * 0.4), comments: Math.floor(metrics.comments * 0.4), shares: Math.floor(metrics.shares * 0.4), reach: Math.floor(metrics.reach * 0.4) },
    { date: '4d ago', likes: Math.floor(metrics.likes * 0.6), comments: Math.floor(metrics.comments * 0.6), shares: Math.floor(metrics.shares * 0.6), reach: Math.floor(metrics.reach * 0.6) },
    { date: '3d ago', likes: Math.floor(metrics.likes * 0.8), comments: Math.floor(metrics.comments * 0.8), shares: Math.floor(metrics.shares * 0.8), reach: Math.floor(metrics.reach * 0.8) },
    { date: '2d ago', likes: Math.floor(metrics.likes * 0.9), comments: Math.floor(metrics.comments * 0.9), shares: Math.floor(metrics.shares * 0.9), reach: Math.floor(metrics.reach * 0.9) },
    { date: 'Today', likes: metrics.likes, comments: metrics.comments, shares: metrics.shares, reach: metrics.reach },
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Post Analytics</CardTitle>
          <Badge className={getEngagementColor(metrics.engagement_rate)}>
            <TrendingUp className="h-3 w-3 mr-1" />
            {metrics.engagement_rate.toFixed(1)}% engagement
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <Heart className="h-6 w-6 text-red-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-800">{formatNumber(metrics.likes)}</p>
            <p className="text-sm text-gray-600">Likes</p>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <MessageCircle className="h-6 w-6 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-800">{formatNumber(metrics.comments)}</p>
            <p className="text-sm text-gray-600">Comments</p>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <Share className="h-6 w-6 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-800">{formatNumber(metrics.shares)}</p>
            <p className="text-sm text-gray-600">Shares</p>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <Eye className="h-6 w-6 text-purple-500 mx-auto mb-2" />
            <p className="text-2xl font-bold text-gray-800">{formatNumber(metrics.reach)}</p>
            <p className="text-sm text-gray-600">Reach</p>
          </div>
        </div>

        {/* Engagement Chart */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">7-Day Performance</h4>
          <ResponsiveContainer width="100%" height={200}>
            <LineChart data={currentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="likes" stroke="#ef4444" strokeWidth={2} />
              <Line type="monotone" dataKey="comments" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="shares" stroke="#10b981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Platform Performance */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Platform Comparison</h4>
          <ResponsiveContainer width="100%" height={150}>
            <BarChart data={[{ platform, ...metrics }]}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="platform" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="likes" fill="#ef4444" />
              <Bar dataKey="comments" fill="#3b82f6" />
              <Bar dataKey="shares" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default PostAnalytics;

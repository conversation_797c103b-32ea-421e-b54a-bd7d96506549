
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import fs from "fs";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // SSL certificate paths
  const sslKeyPath = path.resolve(__dirname, 'ssl/key.pem');
  const sslCertPath = path.resolve(__dirname, 'ssl/cert.pem');

  // Check if SSL certificates exist
  const hasSSL = fs.existsSync(sslKeyPath) && fs.existsSync(sslCertPath);

  // Enable HTTPS if certificates exist or if HTTPS is explicitly requested
  const useHTTPS = hasSSL && (process.env.HTTPS === 'true' || process.env.npm_lifecycle_event === 'dev:https');

  return {
    server: {
      host: "::",
      port: useHTTPS ? 8443 : 8080,
      https: useHTTPS ? {
        key: fs.readFileSync(sslKeyPath),
        cert: fs.readFileSync(sslCertPath),
      } : false,
      // Force HTTPS redirect in production
      ...(mode === 'production' && {
        headers: {
          'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
          'X-XSS-Protection': '1; mode=block',
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
      })
    },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Fix for TypeScript project reference error
  build: {
    target: "esnext",
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select', '@radix-ui/react-tabs'],
          'chart-vendor': ['recharts'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'utils-vendor': ['clsx', 'tailwind-merge', 'date-fns', 'lucide-react'],

          // App chunks
          'pages': [
            'src/pages/Index.tsx',
            'src/pages/CreatePost.tsx',
            'src/pages/ScheduledPosts.tsx',
            'src/pages/Analytics.tsx',
            'src/pages/SocialAccounts.tsx',
            'src/pages/MediaLibrary.tsx',
            'src/pages/Settings.tsx'
          ],
          'components': [
            'src/components/AppSidebar.tsx',
            'src/components/PostScheduler.tsx',
            'src/components/PostCard.tsx',
            'src/components/PageHeader.tsx'
          ]
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  esbuild: {
    target: "esnext",
  },
  };
});

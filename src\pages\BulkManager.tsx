
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import PageHeader from '@/components/PageHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TopHeader } from '@/components/TopHeader';
import BulkContentManager from '@/components/BulkContentManager';
import { EnhancedSocialPost, BulkAction } from '@/types/advanced';
import { CheckSquare } from 'lucide-react';

const BulkManager: React.FC = () => {
  const handleImportPosts = () => {
    console.log('Opening import posts dialog...');
  };
  const [posts] = useState<EnhancedSocialPost[]>([
    {
      id: '1',
      content: 'Sample post content for bulk management',
      platform: 'twitter',
      scheduledFor: '2024-01-15T10:00:00Z',
      status: 'scheduled',
      mediaUrls: [],
      hashtags: ['sample', 'bulk'],
      createdAt: '2024-01-10T10:00:00Z',
      updatedAt: '2024-01-10T10:00:00Z'
    }
  ]);

  const handleBulkAction = (action: BulkAction) => {
    console.log('Processing bulk action:', action);
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Bulk Manager"
              description="Manage multiple posts at once"
              icon={<CheckSquare className="w-8 h-8" />}
              actions={
                <Button className="bg-white text-blue-600 hover:bg-white/90" onClick={handleImportPosts}>
                  Import Posts
                </Button>
              }
            />

            <div className="px-6 pb-6">
              <Card>
                <CardHeader>
                  <CardTitle>Bulk Content Operations</CardTitle>
                </CardHeader>
                <CardContent>
                  <BulkContentManager
                    posts={posts}
                    onBulkAction={handleBulkAction}
                  />
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default BulkManager;

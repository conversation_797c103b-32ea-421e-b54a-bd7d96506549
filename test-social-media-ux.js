/**
 * PulseBuzz.AI Social Media UX Testing Script
 * 
 * This script provides automated testing for the social media setup flow
 * Run in browser console or as part of automated testing suite
 */

class SocialMediaUXTester {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  // Test navigation and initial page load
  async testNavigation() {
    console.log('🧪 Testing Navigation Flow...');
    
    const tests = [
      {
        name: 'Sidebar Navigation Exists',
        test: () => document.querySelector('[data-testid="sidebar"]') !== null
      },
      {
        name: 'Social Accounts Link Visible',
        test: () => document.querySelector('a[href*="social-accounts"]') !== null
      },
      {
        name: 'Page Header Loads',
        test: () => document.querySelector('h1')?.textContent.includes('Social Accounts')
      },
      {
        name: 'Setup Guide Button Present',
        test: () => document.querySelector('button')?.textContent.includes('Setup Guide')
      }
    ];

    return this.runTests('Navigation', tests);
  }

  // Test demo mode functionality
  async testDemoMode() {
    console.log('🧪 Testing Demo Mode...');
    
    const tests = [
      {
        name: 'Available Platforms Visible',
        test: () => document.querySelectorAll('[data-platform]').length >= 4
      },
      {
        name: 'Demo Mode Badges Present',
        test: () => document.querySelector('.badge')?.textContent.includes('Demo Mode')
      },
      {
        name: 'Connect Buttons Functional',
        test: () => {
          const connectBtn = document.querySelector('button[data-action="connect"]');
          return connectBtn && !connectBtn.disabled;
        }
      }
    ];

    return this.runTests('Demo Mode', tests);
  }

  // Test setup guide functionality
  async testSetupGuide() {
    console.log('🧪 Testing Setup Guide...');
    
    // Click setup guide button
    const setupBtn = document.querySelector('button[data-action="show-setup-guide"]');
    if (setupBtn) {
      setupBtn.click();
      await this.wait(500); // Wait for animation
    }

    const tests = [
      {
        name: 'Setup Guide Expands',
        test: () => document.querySelector('[data-component="oauth-setup-guide"]') !== null
      },
      {
        name: 'Platform Sections Present',
        test: () => document.querySelectorAll('[data-platform-setup]').length >= 4
      },
      {
        name: 'Copy Buttons Functional',
        test: () => document.querySelectorAll('button[data-action="copy"]').length > 0
      },
      {
        name: 'External Links Present',
        test: () => document.querySelectorAll('a[target="_blank"]').length > 0
      }
    ];

    return this.runTests('Setup Guide', tests);
  }

  // Test OAuth status detection
  async testOAuthStatus() {
    console.log('🧪 Testing OAuth Status Detection...');
    
    const tests = [
      {
        name: 'OAuth Status Badges Visible',
        test: () => {
          const badges = document.querySelectorAll('.badge');
          return Array.from(badges).some(badge => 
            badge.textContent.includes('OAuth Ready') || 
            badge.textContent.includes('Demo Mode')
          );
        }
      },
      {
        name: 'Platform Icons Rendered',
        test: () => document.querySelectorAll('[data-platform-icon]').length >= 4
      },
      {
        name: 'Connection Status Clear',
        test: () => {
          const statusElements = document.querySelectorAll('[data-connection-status]');
          return statusElements.length > 0;
        }
      }
    ];

    return this.runTests('OAuth Status', tests);
  }

  // Test responsive design
  async testResponsiveDesign() {
    console.log('🧪 Testing Responsive Design...');
    
    const originalWidth = window.innerWidth;
    const tests = [];

    // Test mobile view
    window.resizeTo(375, 667);
    await this.wait(100);
    
    tests.push({
      name: 'Mobile Layout Adapts',
      test: () => {
        const grid = document.querySelector('[data-grid]');
        return !grid || window.getComputedStyle(grid).gridTemplateColumns.includes('1fr');
      }
    });

    // Test tablet view
    window.resizeTo(768, 1024);
    await this.wait(100);
    
    tests.push({
      name: 'Tablet Layout Adapts',
      test: () => {
        const sidebar = document.querySelector('[data-sidebar]');
        return sidebar && window.getComputedStyle(sidebar).display !== 'none';
      }
    });

    // Restore original width
    window.resizeTo(originalWidth, window.innerHeight);
    
    return this.runTests('Responsive Design', tests);
  }

  // Test accessibility features
  async testAccessibility() {
    console.log('🧪 Testing Accessibility...');
    
    const tests = [
      {
        name: 'Buttons Have Accessible Names',
        test: () => {
          const buttons = document.querySelectorAll('button');
          return Array.from(buttons).every(btn => 
            btn.textContent.trim() || 
            btn.getAttribute('aria-label') || 
            btn.getAttribute('title')
          );
        }
      },
      {
        name: 'Images Have Alt Text',
        test: () => {
          const images = document.querySelectorAll('img');
          return Array.from(images).every(img => img.getAttribute('alt'));
        }
      },
      {
        name: 'Focus Indicators Present',
        test: () => {
          const focusableElements = document.querySelectorAll('button, a, input');
          return focusableElements.length > 0; // Basic check
        }
      },
      {
        name: 'Headings Properly Structured',
        test: () => {
          const h1 = document.querySelector('h1');
          return h1 && h1.textContent.trim().length > 0;
        }
      }
    ];

    return this.runTests('Accessibility', tests);
  }

  // Test performance metrics
  async testPerformance() {
    console.log('🧪 Testing Performance...');
    
    const tests = [
      {
        name: 'Page Load Time < 3s',
        test: () => {
          const loadTime = Date.now() - this.startTime;
          return loadTime < 3000;
        }
      },
      {
        name: 'No Console Errors',
        test: () => {
          // This would need to be implemented with error tracking
          return true; // Placeholder
        }
      },
      {
        name: 'Images Load Properly',
        test: () => {
          const images = document.querySelectorAll('img');
          return Array.from(images).every(img => img.complete && img.naturalHeight > 0);
        }
      }
    ];

    return this.runTests('Performance', tests);
  }

  // Simulate user interactions
  async simulateUserFlow() {
    console.log('🧪 Simulating Complete User Flow...');
    
    const steps = [
      {
        name: 'Navigate to Social Accounts',
        action: () => {
          const link = document.querySelector('a[href*="social-accounts"]');
          if (link) link.click();
        }
      },
      {
        name: 'Open Setup Guide',
        action: () => {
          const btn = document.querySelector('button[data-action="show-setup-guide"]');
          if (btn) btn.click();
        }
      },
      {
        name: 'Expand Platform Section',
        action: () => {
          const section = document.querySelector('[data-platform-setup="instagram"]');
          if (section) section.click();
        }
      },
      {
        name: 'Copy Environment Variable',
        action: () => {
          const copyBtn = document.querySelector('button[data-action="copy"]');
          if (copyBtn) copyBtn.click();
        }
      },
      {
        name: 'Test Demo Connection',
        action: () => {
          const connectBtn = document.querySelector('button[data-action="connect"]');
          if (connectBtn) connectBtn.click();
        }
      }
    ];

    for (const step of steps) {
      console.log(`  ▶️ ${step.name}`);
      try {
        step.action();
        await this.wait(500); // Wait between actions
        console.log(`  ✅ ${step.name} - Success`);
      } catch (error) {
        console.log(`  ❌ ${step.name} - Failed:`, error.message);
      }
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting PulseBuzz.AI Social Media UX Tests...\n');
    
    const testSuites = [
      () => this.testNavigation(),
      () => this.testDemoMode(),
      () => this.testSetupGuide(),
      () => this.testOAuthStatus(),
      () => this.testResponsiveDesign(),
      () => this.testAccessibility(),
      () => this.testPerformance()
    ];

    for (const testSuite of testSuites) {
      await testSuite();
      await this.wait(100);
    }

    // Run user flow simulation
    await this.simulateUserFlow();

    // Generate report
    this.generateReport();
  }

  // Helper methods
  async runTests(suiteName, tests) {
    const results = [];
    
    for (const test of tests) {
      try {
        const passed = test.test();
        results.push({ name: test.name, passed, suite: suiteName });
        console.log(`  ${passed ? '✅' : '❌'} ${test.name}`);
      } catch (error) {
        results.push({ name: test.name, passed: false, suite: suiteName, error: error.message });
        console.log(`  ❌ ${test.name} - Error: ${error.message}`);
      }
    }
    
    this.testResults.push(...results);
    return results;
  }

  wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(t => t.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);

    console.log('\n📊 Test Results Summary:');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Success Rate: ${successRate}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(t => !t.passed)
        .forEach(t => console.log(`  - ${t.suite}: ${t.name}`));
    }

    // UX Score calculation
    const uxScore = this.calculateUXScore();
    console.log(`\n🎯 Overall UX Score: ${uxScore}/10`);
    
    return {
      totalTests,
      passedTests,
      failedTests,
      successRate: parseFloat(successRate),
      uxScore,
      details: this.testResults
    };
  }

  calculateUXScore() {
    const weights = {
      'Navigation': 2,
      'Demo Mode': 2,
      'Setup Guide': 2,
      'OAuth Status': 1.5,
      'Responsive Design': 1,
      'Accessibility': 1,
      'Performance': 0.5
    };

    let weightedScore = 0;
    let totalWeight = 0;

    Object.keys(weights).forEach(suite => {
      const suiteTests = this.testResults.filter(t => t.suite === suite);
      if (suiteTests.length > 0) {
        const suiteScore = suiteTests.filter(t => t.passed).length / suiteTests.length;
        weightedScore += suiteScore * weights[suite];
        totalWeight += weights[suite];
      }
    });

    return totalWeight > 0 ? (weightedScore / totalWeight * 10).toFixed(1) : 0;
  }
}

// Usage instructions
console.log(`
🧪 PulseBuzz.AI Social Media UX Tester

To run tests, execute:
const tester = new SocialMediaUXTester();
tester.runAllTests();

Or run individual test suites:
tester.testNavigation();
tester.testDemoMode();
tester.testSetupGuide();
`);

// Auto-run if in testing environment
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Uncomment to auto-run tests
  // const tester = new SocialMediaUXTester();
  // tester.runAllTests();
}

import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  Heart,
  MessageSquare,
  Share,
  Download,
  Calendar,
  Target
} from 'lucide-react';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import PageHeader from '@/components/PageHeader';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';

const Analytics: React.FC = () => {
  const handleDateFilter = () => {
    console.log('Opening date filter...');
  };

  const handleExportReport = () => {
    console.log('Exporting analytics report...');
  };
  // Mock analytics data
  const metrics = {
    totalFollowers: 12847,
    followerGrowth: 8.5,
    totalReach: 89234,
    reachGrowth: 12.3,
    engagement: 4567,
    engagementGrowth: -2.1,
    engagementRate: 4.8,
    rateGrowth: 0.3
  };

  // Chart data
  const engagementData = [
    { date: 'Mon', engagement: 2400, reach: 4000 },
    { date: 'Tue', engagement: 1398, reach: 3000 },
    { date: 'Wed', engagement: 9800, reach: 2000 },
    { date: 'Thu', engagement: 3908, reach: 2780 },
    { date: 'Fri', engagement: 4800, reach: 1890 },
    { date: 'Sat', engagement: 3800, reach: 2390 },
    { date: 'Sun', engagement: 4300, reach: 3490 },
  ];

  const followerGrowthData = [
    { date: 'Week 1', followers: 11200 },
    { date: 'Week 2', followers: 11450 },
    { date: 'Week 3', followers: 11800 },
    { date: 'Week 4', followers: 12847 },
  ];

  const topPosts = [
    {
      id: '1',
      platform: 'Instagram',
      content: '🚀 Exciting news! Our new AI-powered content generator is now live...',
      engagement: 342,
      reach: 5420,
      likes: 289,
      comments: 67,
      shares: 23
    },
    {
      id: '2',
      platform: 'X (Twitter)',
      content: 'Just launched our advanced analytics dashboard! 📊 Get deep insights...',
      engagement: 198,
      reach: 3240,
      likes: 156,
      comments: 42,
      shares: 18
    }
  ];

  const platformStats = [
    {
      platform: 'Instagram',
      followers: 5420,
      growth: 12.5,
      posts: 18,
      engagement: 4.8
    },
    {
      platform: 'X (Twitter)',
      followers: 3240,
      growth: 8.2,
      posts: 24,
      engagement: 3.2
    },
    {
      platform: 'LinkedIn',
      followers: 2890,
      growth: 15.7,
      posts: 12,
      engagement: 6.1
    },
    {
      platform: 'Facebook',
      followers: 1297,
      growth: -2.3,
      posts: 8,
      engagement: 2.9
    }
  ];

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (growth < 0) return <TrendingDown className="w-4 h-4 text-red-500" />;
    return <div className="w-4 h-4" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Analytics Dashboard"
              description="Track your social media performance and engagement"
              icon={<BarChart3 className="w-8 h-8" />}
              actions={
                <div className="flex items-center space-x-2">
                  <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20" onClick={handleDateFilter}>
                    <Calendar className="w-4 h-4 mr-2" />
                    Last 30 days
                  </Button>
                  <Button className="bg-white text-blue-600 hover:bg-white/90" onClick={handleExportReport}>
                    <Download className="w-4 h-4 mr-2" />
                    Export Report
                  </Button>
                </div>
              }
            />

            <div className="px-6 pb-6 space-y-6">

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Followers</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(metrics.totalFollowers)}</p>
                  <div className="flex items-center space-x-1 mt-1">
                    {getGrowthIcon(metrics.followerGrowth)}
                    <span className={`text-sm ${getGrowthColor(metrics.followerGrowth)}`}>
                      {metrics.followerGrowth > 0 ? '+' : ''}{metrics.followerGrowth}%
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Reach</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(metrics.totalReach)}</p>
                  <div className="flex items-center space-x-1 mt-1">
                    {getGrowthIcon(metrics.reachGrowth)}
                    <span className={`text-sm ${getGrowthColor(metrics.reachGrowth)}`}>
                      {metrics.reachGrowth > 0 ? '+' : ''}{metrics.reachGrowth}%
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Engagement</p>
                  <p className="text-2xl font-bold text-gray-900">{formatNumber(metrics.engagement)}</p>
                  <div className="flex items-center space-x-1 mt-1">
                    {getGrowthIcon(metrics.engagementGrowth)}
                    <span className={`text-sm ${getGrowthColor(metrics.engagementGrowth)}`}>
                      {metrics.engagementGrowth > 0 ? '+' : ''}{metrics.engagementGrowth}%
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.engagementRate}%</p>
                  <div className="flex items-center space-x-1 mt-1">
                    {getGrowthIcon(metrics.rateGrowth)}
                    <span className={`text-sm ${getGrowthColor(metrics.rateGrowth)}`}>
                      {metrics.rateGrowth > 0 ? '+' : ''}{metrics.rateGrowth}%
                    </span>
                  </div>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Target className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="platforms">Platforms</TabsTrigger>
            <TabsTrigger value="posts">Top Posts</TabsTrigger>
            <TabsTrigger value="audience">Audience</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Over Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={engagementData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="engagement" stroke="#3b82f6" strokeWidth={2} />
                        <Line type="monotone" dataKey="reach" stroke="#10b981" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Follower Growth</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={followerGrowthData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="followers" fill="#8b5cf6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="platforms" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Platform Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {platformStats.map((platform) => (
                    <div key={platform.platform} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                          <span className="text-sm font-medium">{platform.platform.slice(0, 2)}</span>
                        </div>
                        <div>
                          <h3 className="font-medium">{platform.platform}</h3>
                          <p className="text-sm text-gray-600">{formatNumber(platform.followers)} followers</p>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="flex items-center space-x-2">
                          {getGrowthIcon(platform.growth)}
                          <span className={`text-sm font-medium ${getGrowthColor(platform.growth)}`}>
                            {platform.growth > 0 ? '+' : ''}{platform.growth}%
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">
                          {platform.posts} posts • {platform.engagement}% engagement
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="posts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Posts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPosts.map((post, index) => (
                    <div key={post.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        <Badge variant="secondary">#{index + 1}</Badge>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge variant="outline">{post.platform}</Badge>
                        </div>
                        <p className="text-sm text-gray-900 mb-2">{post.content}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Eye className="w-4 h-4" />
                            <span>{formatNumber(post.reach)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Heart className="w-4 h-4" />
                            <span>{formatNumber(post.likes)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MessageSquare className="w-4 h-4" />
                            <span>{formatNumber(post.comments)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Share className="w-4 h-4" />
                            <span>{formatNumber(post.shares)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audience" className="space-y-4">
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Audience Insights</h3>
              <p className="text-gray-600">
                Detailed audience demographics and behavior analysis coming soon
              </p>
            </div>
          </TabsContent>
        </Tabs>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Analytics;

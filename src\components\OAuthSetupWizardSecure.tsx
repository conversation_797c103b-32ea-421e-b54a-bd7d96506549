import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  ExternalLink, 
  Copy, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Save,
  Eye,
  EyeOff,
  Database,
  Zap,
  Shield,
  Loader2
} from 'lucide-react';
import { config, getPlatformInfo } from '@/config/oauth';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useOAuthCredentials, type OAuthCredentialInput } from '@/services/oauthCredentialsService';
import { useAppStore } from '@/store/appStore';

interface OAuthSetupWizardSecureProps {
  onClose?: () => void;
}

interface CredentialInputs {
  [platform: string]: {
    clientId: string;
    clientSecret: string;
    showSecret: boolean;
  };
}

const OAuthSetupWizardSecure: React.FC<OAuthSetupWizardSecureProps> = ({ onClose }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { workspace } = useAppStore();
  const [copiedText, setCopiedText] = useState<string>('');
  const [storedCredentials, setStoredCredentials] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);
  const [credentialInputs, setCredentialInputs] = useState<CredentialInputs>({});
  const [savingPlatform, setSavingPlatform] = useState<string | null>(null);

  const platforms = Object.keys(config.oauth) as Array<keyof typeof config.oauth>;
  const oauthService = useOAuthCredentials();

  // Initialize OAuth service with workspace
  useEffect(() => {
    if (workspace?.id && oauthService) {
      oauthService.setWorkspace(workspace.id);
      loadStoredCredentials();
    }
  }, [workspace?.id, oauthService]);

  // Load stored credentials from Supabase
  const loadStoredCredentials = async () => {
    if (!oauthService) return;
    
    try {
      setLoading(true);
      const stored: Record<string, any> = {};
      
      for (const platform of platforms) {
        try {
          const credentials = await oauthService.getCredentials(platform);
          if (credentials.length > 0) {
            stored[platform] = credentials[0]; // Get the active credential
          }
        } catch (error) {
          // Platform not configured, continue
        }
      }
      
      setStoredCredentials(stored);
    } catch (error) {
      console.error('Failed to load stored credentials:', error);
      toast({
        title: "Loading Error",
        description: "Failed to load stored credentials",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Check if a platform has stored credentials
  const hasStoredCredentials = (platform: string): boolean => {
    return !!storedCredentials[platform];
  };

  // Handle credential input changes
  const handleCredentialChange = (platform: string, field: 'clientId' | 'clientSecret', value: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        clientId: field === 'clientId' ? value : prev[platform]?.clientId || '',
        clientSecret: field === 'clientSecret' ? value : prev[platform]?.clientSecret || '',
        showSecret: prev[platform]?.showSecret || false
      }
    }));
  };

  // Toggle password visibility
  const toggleSecretVisibility = (platform: string) => {
    setCredentialInputs(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        clientId: prev[platform]?.clientId || '',
        clientSecret: prev[platform]?.clientSecret || '',
        showSecret: !prev[platform]?.showSecret
      }
    }));
  };

  // Save credentials to Supabase
  const saveCredentials = async (platform: string) => {
    if (!oauthService || !user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to save credentials",
        variant: "destructive",
      });
      return;
    }

    const input = credentialInputs[platform];
    if (!input?.clientId || !input?.clientSecret) {
      toast({
        title: "Missing Information",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    try {
      setSavingPlatform(platform);
      const platformInfo = getPlatformInfo(platform);
      
      const credentialInput: OAuthCredentialInput = {
        platform,
        platformDisplayName: platformInfo.displayName,
        clientId: input.clientId,
        clientSecret: input.clientSecret
      };

      await oauthService.saveCredentials(credentialInput);

      // Update stored credentials state
      setStoredCredentials(prev => ({
        ...prev,
        [platform]: {
          platform,
          platformDisplayName: platformInfo.displayName,
          isActive: true,
          verificationStatus: 'pending'
        }
      }));

      // Clear input fields
      setCredentialInputs(prev => ({
        ...prev,
        [platform]: { clientId: '', clientSecret: '', showSecret: false }
      }));

      toast({
        title: "Credentials Saved",
        description: `${platformInfo.displayName} credentials saved securely to database`,
      });
    } catch (error) {
      console.error('Failed to save credentials:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save credentials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSavingPlatform(null);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
      setTimeout(() => setCopiedText(''), 2000);
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const getSetupStatus = () => {
    const configured = platforms.filter(platform => hasStoredCredentials(platform));
    return {
      total: platforms.length,
      configured: configured.length,
      platforms: configured
    };
  };

  const status = getSetupStatus();

  const PlatformSetupCard = ({ platform }: { platform: keyof typeof config.oauth }) => {
    const platformConfig = config.oauth[platform];
    const platformInfo = getPlatformInfo(platform);
    const isConfigured = hasStoredCredentials(platform);
    const currentInput = credentialInputs[platform] || { clientId: '', clientSecret: '', showSecret: false };
    const isSaving = savingPlatform === platform;

    const getRedirectUri = () => {
      const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8080';
      return `${origin}/auth/${platform}/callback`;
    };

    const getDeveloperUrl = () => {
      const urls = {
        instagram: 'https://developers.facebook.com/apps/',
        facebook: 'https://developers.facebook.com/apps/',
        linkedin: 'https://www.linkedin.com/developers/apps',
        twitter: 'https://developer.twitter.com/en/portal/dashboard'
      };
      return urls[platform] || '#';
    };

    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{platformInfo.icon}</span>
              <div>
                <CardTitle className="text-lg">{platformInfo.displayName}</CardTitle>
                <CardDescription>{platformInfo.description}</CardDescription>
              </div>
            </div>
            <Badge variant={isConfigured ? "default" : "secondary"}>
              {isConfigured ? (
                <><CheckCircle className="w-3 h-3 mr-1" /> Configured</>
              ) : (
                <><AlertCircle className="w-3 h-3 mr-1" /> Not Configured</>
              )}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Developer Console</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(getDeveloperUrl(), '_blank')}
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Open Console
                </Button>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Redirect URI</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input
                  value={getRedirectUri()}
                  readOnly
                  className="text-xs"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(getRedirectUri(), `${platformInfo.displayName} Redirect URI`)}
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>

          {isConfigured ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Credentials Configured</span>
              </div>
              <div className="space-y-2">
                <div>
                  <Label className="text-xs text-green-700">Client ID</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard('Stored securely', `${platformInfo.displayName} Client ID`)}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-green-700">Client Secret</Label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Input
                      value="••••••••••••••••"
                      readOnly
                      className="text-xs"
                      type="password"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard('Stored securely', `${platformInfo.displayName} Client Secret`)}
                    >
                      <Copy className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert>
                <Database className="h-4 w-4" />
                <AlertDescription>
                  Enter your <strong>{platformInfo.displayName}</strong> OAuth credentials below. They will be encrypted and stored securely in your database.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div>
                  <Label htmlFor={`${platform}-client-id`} className="text-sm font-medium">
                    Client ID
                  </Label>
                  <Input
                    id={`${platform}-client-id`}
                    value={currentInput.clientId}
                    onChange={(e) => handleCredentialChange(platform, 'clientId', e.target.value)}
                    placeholder="Enter your client ID"
                    className="text-sm"
                  />
                </div>

                <div>
                  <Label htmlFor={`${platform}-client-secret`} className="text-sm font-medium">
                    Client Secret
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id={`${platform}-client-secret`}
                      type={currentInput.showSecret ? "text" : "password"}
                      value={currentInput.clientSecret}
                      onChange={(e) => handleCredentialChange(platform, 'clientSecret', e.target.value)}
                      placeholder="Enter your client secret"
                      className="text-sm"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSecretVisibility(platform)}
                    >
                      {currentInput.showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>
                  </div>
                </div>

                <Button
                  onClick={() => saveCredentials(platform)}
                  disabled={!currentInput.clientId || !currentInput.clientSecret || isSaving}
                  className="w-full"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Credentials
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin" />
        <span className="ml-2">Loading OAuth credentials...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">OAuth Credentials Setup</h1>
        <p className="text-gray-600">
          Configure your social media platform credentials for secure OAuth integration.
        </p>
        <div className="flex items-center space-x-4 mt-4">
          <Badge variant="outline">
            {status.configured} of {status.total} platforms configured
          </Badge>
          <Badge variant={status.configured === status.total ? "default" : "secondary"}>
            {status.configured === status.total ? "Complete" : "In Progress"}
          </Badge>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Secure Credential Storage
            </CardTitle>
            <CardDescription>
              Your OAuth credentials are encrypted and stored securely in your Supabase database with workspace isolation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <Shield className="h-4 w-4" />
              <AlertDescription>
                <strong>Production Security:</strong> All credentials are encrypted before storage and isolated per workspace. 
                Only your application can decrypt and use these credentials.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {platforms.map(platform => (
            <PlatformSetupCard key={platform} platform={platform} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default OAuthSetupWizardSecure;

interface LinkedInTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
}

interface LinkedInProfile {
  id: string;
  firstName: {
    localized: {
      [key: string]: string;
    };
  };
  lastName: {
    localized: {
      [key: string]: string;
    };
  };
  profilePicture?: {
    displayImage: string;
  };
}

interface LinkedInPostRequest {
  author: string;
  lifecycleState: string;
  specificContent: {
    'com.linkedin.ugc.ShareContent': {
      shareCommentary: {
        text: string;
      };
      shareMediaCategory: string;
    };
  };
  visibility: {
    'com.linkedin.ugc.MemberNetworkVisibility': string;
  };
}

class LinkedInService {
  private baseUrl = 'https://api.linkedin.com/v2';
  private corsProxy = 'https://cors-anywhere.herokuapp.com/';
  
  async exchangeCodeForToken(code: string, redirectUri: string): Promise<LinkedInTokenResponse> {
    const clientId = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_ID');
    const clientSecret = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_SECRET');

    if (!clientId || !clientSecret) {
      throw new Error('LinkedIn credentials not found in session storage');
    }

    console.log('Token exchange attempt:', {
      clientId: clientId.substring(0, 8) + '...',
      redirectUri,
      codeLength: code.length
    });

    const tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: redirectUri,
      client_id: clientId,
      client_secret: clientSecret,
    });

    console.log('Making token request to:', tokenUrl);

    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: params.toString(),
      });

      console.log('Token response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Token exchange error response:', errorData);
        throw new Error(`Token exchange failed: ${response.status} - ${errorData}`);
      }

      const tokenData = await response.json();
      console.log('Token exchange successful');
      return tokenData;
    } catch (error) {
      console.error('Token exchange network error:', error);
      throw error;
    }
  }

  async getUserProfile(accessToken: string): Promise<LinkedInProfile> {
    console.log('Fetching LinkedIn profile with token:', accessToken.substring(0, 10) + '...');

    // Try multiple endpoints in order of preference
    const endpoints = [
      {
        name: 'OpenID Connect userinfo',
        url: `${this.baseUrl}/userinfo`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        }
      },
      {
        name: 'Legacy people endpoint (basic)',
        url: `${this.baseUrl}/people/~:(id,firstName,lastName)`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        }
      },
      {
        name: 'Legacy people endpoint (detailed)',
        url: `${this.baseUrl}/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        }
      },
      {
        name: 'Simple people endpoint',
        url: `${this.baseUrl}/people/~`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        }
      }
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying ${endpoint.name}:`, endpoint.url);

        const response = await fetch(endpoint.url, {
          headers: endpoint.headers,
        });

        console.log(`${endpoint.name} response status:`, response.status);

        if (response.ok) {
          const profileData = await response.json();
          console.log(`${endpoint.name} data:`, profileData);

          // Transform response based on endpoint type
          if (endpoint.name.includes('OpenID Connect')) {
            // OpenID Connect format
            return {
              id: profileData.sub || profileData.id || 'unknown',
              firstName: {
                localized: {
                  'en_US': profileData.given_name || 'Unknown'
                }
              },
              lastName: {
                localized: {
                  'en_US': profileData.family_name || 'User'
                }
              },
              profilePicture: profileData.picture ? {
                displayImage: {
                  elements: [{
                    identifiers: [{
                      identifier: profileData.picture
                    }]
                  }]
                }
              } : undefined
            };
          } else {
            // Legacy LinkedIn format - return as-is or transform if needed
            if (profileData.id && profileData.firstName && profileData.lastName) {
              return profileData;
            } else {
              // Handle simple response format
              return {
                id: profileData.id || 'unknown',
                firstName: {
                  localized: {
                    'en_US': profileData.firstName?.localized?.en_US || profileData.localizedFirstName || 'Unknown'
                  }
                },
                lastName: {
                  localized: {
                    'en_US': profileData.lastName?.localized?.en_US || profileData.localizedLastName || 'User'
                  }
                }
              };
            }
          }
        } else {
          const errorData = await response.text();
          console.error(`${endpoint.name} error:`, response.status, errorData);
        }
      } catch (error) {
        console.error(`${endpoint.name} failed:`, error);
      }
    }

    throw new Error('All LinkedIn profile endpoints failed. Please check your access token and ensure it has the correct scopes (openid, profile, email, w_member_social).');
  }

  async createPost(accessToken: string, text: string, authorId: string): Promise<any> {
    const postData: LinkedInPostRequest = {
      author: `urn:li:person:${authorId}`,
      lifecycleState: 'PUBLISHED',
      specificContent: {
        'com.linkedin.ugc.ShareContent': {
          shareCommentary: {
            text: text,
          },
          shareMediaCategory: 'NONE',
        },
      },
      visibility: {
        'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
      },
    };

    const response = await fetch(`${this.baseUrl}/ugcPosts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0',
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create post: ${response.status} - ${errorData}`);
    }

    return response.json();
  }

  async validateToken(accessToken: string, useCorsProxy: boolean = false): Promise<{ valid: boolean; info?: any; error?: string }> {
    try {
      console.log('Validating LinkedIn token...', useCorsProxy ? 'with CORS proxy' : 'direct');

      const baseUrl = useCorsProxy ? `${this.corsProxy}${this.baseUrl}` : this.baseUrl;

      // Try a simple endpoint first to check token validity
      const response = await fetch(`${baseUrl}/people/~`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
          ...(useCorsProxy && { 'X-Requested-With': 'XMLHttpRequest' })
        },
      });

      console.log('Token validation response status:', response.status);

      if (response.status === 401) {
        return { valid: false, error: 'Token is invalid or expired' };
      } else if (response.status === 403) {
        return { valid: false, error: 'Token lacks required permissions. Required scopes: openid, profile, email, w_member_social' };
      } else if (response.ok) {
        const data = await response.json();
        console.log('Token validation successful:', data);
        return { valid: true, info: data };
      } else {
        const errorText = await response.text();
        console.error('Token validation error:', response.status, errorText);
        return { valid: false, error: `HTTP ${response.status}: ${errorText}` };
      }
    } catch (error) {
      console.error('Token validation network error:', error);

      // Check if it's a CORS error and we haven't tried the proxy yet
      if (error instanceof TypeError && error.message.includes('Failed to fetch') && !useCorsProxy) {
        console.log('CORS error detected, trying with proxy...');
        return this.validateToken(accessToken, true);
      }

      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  async testConnection(accessToken: string): Promise<{ success: boolean; profile?: LinkedInProfile; error?: string }> {
    try {
      console.log('Testing LinkedIn connection...');

      // First validate the token
      const tokenValidation = await this.validateToken(accessToken);
      console.log('Token validation result:', tokenValidation);

      if (!tokenValidation.valid) {
        return {
          success: false,
          error: `Token validation failed: ${tokenValidation.error}`
        };
      }

      const profile = await this.getUserProfile(accessToken);
      console.log('LinkedIn connection test successful:', profile);
      return { success: true, profile };
    } catch (error) {
      console.error('LinkedIn connection test failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}

export const linkedinService = new LinkedInService();
export type { LinkedInTokenResponse, LinkedInProfile, LinkedInPostRequest };

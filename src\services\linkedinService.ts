interface LinkedInTokenResponse {
  access_token: string;
  expires_in: number;
  refresh_token?: string;
  scope: string;
}

interface LinkedInProfile {
  id: string;
  firstName: {
    localized: {
      [key: string]: string;
    };
  };
  lastName: {
    localized: {
      [key: string]: string;
    };
  };
  profilePicture?: {
    displayImage: string;
  };
}

interface LinkedInPostRequest {
  author: string;
  lifecycleState: string;
  specificContent: {
    'com.linkedin.ugc.ShareContent': {
      shareCommentary: {
        text: string;
      };
      shareMediaCategory: string;
    };
  };
  visibility: {
    'com.linkedin.ugc.MemberNetworkVisibility': string;
  };
}

class LinkedInService {
  private baseUrl = 'https://api.linkedin.com/v2';
  
  async exchangeCodeForToken(code: string, redirectUri: string): Promise<LinkedInTokenResponse> {
    const clientId = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_ID');
    const clientSecret = sessionStorage.getItem('VITE_LINKEDIN_CLIENT_SECRET');
    
    if (!clientId || !clientSecret) {
      throw new Error('LinkedIn credentials not found');
    }

    const tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    
    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code: code,
      redirect_uri: redirectUri,
      client_id: clientId,
      client_secret: clientSecret,
    });

    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Token exchange failed: ${response.status} - ${errorData}`);
    }

    return response.json();
  }

  async getUserProfile(accessToken: string): Promise<LinkedInProfile> {
    const response = await fetch(`${this.baseUrl}/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get user profile: ${response.status} - ${errorData}`);
    }

    return response.json();
  }

  async createPost(accessToken: string, text: string, authorId: string): Promise<any> {
    const postData: LinkedInPostRequest = {
      author: `urn:li:person:${authorId}`,
      lifecycleState: 'PUBLISHED',
      specificContent: {
        'com.linkedin.ugc.ShareContent': {
          shareCommentary: {
            text: text,
          },
          shareMediaCategory: 'NONE',
        },
      },
      visibility: {
        'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
      },
    };

    const response = await fetch(`${this.baseUrl}/ugcPosts`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0',
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to create post: ${response.status} - ${errorData}`);
    }

    return response.json();
  }

  async testConnection(accessToken: string): Promise<{ success: boolean; profile?: LinkedInProfile; error?: string }> {
    try {
      const profile = await this.getUserProfile(accessToken);
      return { success: true, profile };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}

export const linkedinService = new LinkedInService();
export type { LinkedInTokenResponse, LinkedInProfile, LinkedInPostRequest };

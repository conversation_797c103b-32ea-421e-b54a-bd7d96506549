
import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clock, Hash, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAppStore } from "@/store/appStore";
import ErrorBoundary from "@/components/ErrorBoundary";

interface PostSchedulerProps {
  initialPlatform?: string;
  initialContent?: string;
  onClose?: () => void;
}

const PostScheduler = ({ initialPlatform = "", initialContent = "", onClose }: PostSchedulerProps) => {
  const [selectedPlatform, setSelectedPlatform] = useState(initialPlatform);
  const [content, setContent] = useState(initialContent);
  const [scheduledDate, setScheduledDate] = useState("");
  const [scheduledTime, setScheduledTime] = useState("");
  const [postType, setPostType] = useState<'text' | 'image' | 'video' | 'carousel'>('text');
  const [hashtags, setHashtags] = useState("");
  const [mediaFiles, setMediaFiles] = useState<FileList | null>(null);

  const { createPost, categories, workspace } = useAppStore();
  const { toast } = useToast();

  // Safely get connected accounts with error handling and demo fallback
  const connectedAccounts = useAppStore((state) => {
    try {
      const accounts = Array.isArray(state.connectedAccounts) ? state.connectedAccounts : [];

      // If no accounts are connected, provide demo accounts for testing
      if (accounts.length === 0) {
        return [
          { id: 'demo-instagram', platform: 'Instagram', username: 'demo_account' },
          { id: 'demo-twitter', platform: 'X (Twitter)', username: 'demo_account' },
          { id: 'demo-facebook', platform: 'Facebook', username: 'demo_account' },
          { id: 'demo-linkedin', platform: 'LinkedIn', username: 'demo_account' }
        ];
      }

      return accounts;
    } catch (error) {
      console.warn('Error accessing connectedAccounts:', error);
      // Return demo accounts as fallback
      return [
        { id: 'demo-instagram', platform: 'Instagram', username: 'demo_account' },
        { id: 'demo-twitter', platform: 'X (Twitter)', username: 'demo_account' },
        { id: 'demo-facebook', platform: 'Facebook', username: 'demo_account' },
        { id: 'demo-linkedin', platform: 'LinkedIn', username: 'demo_account' }
      ];
    }
  });

  const getPlatformLimits = (platform: string) => {
    const limits = {
      'Instagram': { textLimit: 2200, hashtagLimit: 30, mediaLimit: 10, postTypes: ['text', 'image', 'video', 'carousel'] },
      'X (Twitter)': { textLimit: 280, hashtagLimit: 10, mediaLimit: 4, postTypes: ['text', 'image', 'video'] },
      'Facebook': { textLimit: 63206, hashtagLimit: 30, mediaLimit: 10, postTypes: ['text', 'image', 'video'] },
      'LinkedIn': { textLimit: 3000, hashtagLimit: 5, mediaLimit: 9, postTypes: ['text', 'image', 'video'] }
    };
    return limits[platform as keyof typeof limits] || null;
  };

  const platformLimits = selectedPlatform ? getPlatformLimits(selectedPlatform) : null;

  const handleSchedule = useCallback(async () => {
    // Validate required fields
    if (!selectedPlatform || !content.trim() || !scheduledDate || !scheduledTime) {
      toast({
        title: "Missing Required Fields",
        description: "Please fill in all required fields (Platform, Content, Date, and Time).",
        variant: "destructive",
      });
      return;
    }

    // Validate content length
    if (!isContentValid()) {
      toast({
        title: "Content Too Long",
        description: `Content exceeds the ${platformLimits?.textLimit} character limit for ${selectedPlatform}.`,
        variant: "destructive",
      });
      return;
    }

    const scheduledFor = `${scheduledDate}T${scheduledTime}:00`;
    const hashtagArray = hashtags.split(',').map(tag => tag.trim()).filter(Boolean);

    try {
      await createPost({
        platform: selectedPlatform,
        content: content.trim(),
        scheduled_for: scheduledFor,
        status: 'scheduled',
        category_id: categories.length > 0 ? categories[0].id : null,
        media_urls: [], // In real app, upload media first
        hashtags: hashtagArray,
        mentions: [],
        is_evergreen: false,
        recycle_count: 0,
        max_recycles: 3,
        approval_status: 'approved',
        external_post_id: null,
        published_at: null
      });

      // Reset form
      setContent("");
      setScheduledDate("");
      setScheduledTime("");
      setHashtags("");
      setMediaFiles(null);

      toast({
        title: "Post Scheduled",
        description: `Your post has been scheduled for ${selectedPlatform} on ${scheduledDate} at ${scheduledTime}.`,
      });

      if (onClose) onClose();
    } catch (error) {
      console.error('Error scheduling post:', error);
      toast({
        title: "Scheduling Failed",
        description: "There was an error scheduling your post. Please try again.",
        variant: "destructive",
      });
    }
  }, [selectedPlatform, content, scheduledDate, scheduledTime, platformLimits, hashtags, createPost, categories, toast, onClose, isContentValid]);



  const isContentValid = useCallback(() => {
    if (!platformLimits) return true;
    return content.length <= platformLimits.textLimit;
  }, [content, platformLimits]);

  const getCharacterCount = () => {
    if (!platformLimits) return "";
    return `${content.length}/${platformLimits.textLimit}`;
  };

  const isFormValid = useCallback(() => {
    return selectedPlatform &&
           content.trim() &&
           scheduledDate &&
           scheduledTime &&
           isContentValid();
  }, [selectedPlatform, content, scheduledDate, scheduledTime, isContentValid]);

  // Set default date to today when component mounts
  useEffect(() => {
    if (!scheduledDate) {
      const today = new Date().toISOString().split('T')[0];
      setScheduledDate(today);
    }
  }, [scheduledDate]);

  // Clear hashtags when switching away from Twitter
  useEffect(() => {
    if (selectedPlatform !== "X (Twitter)" && hashtags) {
      setHashtags("");
    }
  }, [selectedPlatform, hashtags]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && onClose) {
        onClose();
      }
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter' && isFormValid()) {
        handleSchedule();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isFormValid, onClose, handleSchedule]);

  return (
    <ErrorBoundary>
      <Card className="w-full max-w-2xl mx-auto border-0 shadow-none">
      <CardHeader className="relative pb-4">
        <CardTitle className="flex items-center justify-between" id="post-scheduler-title">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            <span>Schedule New Post</span>
          </div>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-muted"
              aria-label="Close scheduler"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 px-6 pb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="platform" className="text-sm font-medium">Platform *</Label>
            <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={Array.isArray(connectedAccounts) && connectedAccounts.length > 0 ? "Select platform" : "No connected accounts"} />
              </SelectTrigger>
              <SelectContent className="z-[10000]">
                {Array.isArray(connectedAccounts) && connectedAccounts.length > 0 ? (
                  connectedAccounts.map((account) => (
                    <SelectItem key={account.id} value={account.platform}>
                      {account.platform} ({account.username}){account.id.startsWith('demo-') ? ' - Demo' : ''}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="" disabled>
                    No connected accounts. Please connect accounts first.
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            {connectedAccounts.some(account => account.id.startsWith('demo-')) && (
              <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                ℹ️ Using demo accounts for testing. Go to <strong>Social Accounts</strong> to connect real platforms.
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="postType" className="text-sm font-medium">Post Type</Label>
            <Select value={postType} onValueChange={(value: 'text' | 'image' | 'video' | 'carousel') => setPostType(value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Text Only" />
              </SelectTrigger>
              <SelectContent className="z-[10000]">
                <SelectItem value="text">Text Only</SelectItem>
                <SelectItem value="image">Image</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="carousel">Carousel</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="content" className="text-sm font-medium">Content *</Label>
          <Textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Write your post content..."
            className={`min-h-[120px] resize-none ${!isContentValid() ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'}`}
          />
          <div className="flex justify-between items-center">
            <span className={`text-xs font-medium ${!isContentValid() ? 'text-red-500' : 'text-gray-500'}`}>
              {getCharacterCount()}
            </span>
            {!isContentValid() && (
              <span className="text-xs text-red-500 font-medium">Content exceeds platform limit</span>
            )}
          </div>
        </div>

        {postType !== 'text' && (
          <div className="space-y-2">
            <Label htmlFor="media" className="text-sm font-medium">Media Files</Label>
            <Input
              id="media"
              type="file"
              multiple={postType === 'carousel'}
              accept={postType === 'video' ? 'video/*' : 'image/*'}
              onChange={(e) => setMediaFiles(e.target.files)}
              className="cursor-pointer"
            />
            <p className="text-xs text-gray-500">
              {postType === 'carousel' && 'Select multiple images for carousel'}
              {postType === 'image' && 'Select an image file'}
              {postType === 'video' && 'Select a video file'}
            </p>
          </div>
        )}

        {selectedPlatform === "X (Twitter)" && (
          <div className="space-y-2">
            <Label htmlFor="hashtags" className="text-sm font-medium">Hashtags</Label>
            <div className="relative">
              <Hash className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="hashtags"
                value={hashtags}
                onChange={(e) => setHashtags(e.target.value)}
                placeholder="productivity, success, tips (comma separated)"
                className="pl-10"
              />
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="date" className="text-sm font-medium">Scheduled Date *</Label>
            <Input
              id="date"
              type="date"
              value={scheduledDate}
              onChange={(e) => setScheduledDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="time" className="text-sm font-medium">Scheduled Time *</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="time"
                type="time"
                value={scheduledTime}
                onChange={(e) => setScheduledTime(e.target.value)}
                className="pl-10 w-full"
              />
            </div>
          </div>
        </div>

        {selectedPlatform && platformLimits && (
          <div className="bg-blue-50 p-3 rounded-md text-sm">
            <p className="font-medium text-blue-900 mb-1">{selectedPlatform} Limits:</p>
            <ul className="text-blue-700 space-y-1">
              <li>• Character limit: {platformLimits.textLimit}</li>
              {platformLimits.hashtagLimit && (
                <li>• Hashtag limit: {platformLimits.hashtagLimit}</li>
              )}
              <li>• Media limit: {platformLimits.mediaLimit} files</li>
              <li>• Supported types: {platformLimits.postTypes.join(', ')}</li>
            </ul>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          <Button
            onClick={handleSchedule}
            disabled={!isFormValid()}
            className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            size="default"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Post
          </Button>
          {onClose && (
            <Button
              variant="outline"
              onClick={onClose}
              className="sm:w-auto"
              size="default"
            >
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
    </ErrorBoundary>
  );
};

export default PostScheduler;

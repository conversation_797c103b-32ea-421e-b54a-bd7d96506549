import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  EyeOff, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Save,
  Trash2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { connectSocialAccount } from '@/services/socialMediaService';

interface OAuthCredentials {
  clientId: string;
  clientSecret: string;
}

interface PlatformCredentials {
  instagram: OAuthCredentials;
  facebook: OAuthCredentials;
  linkedin: OAuthCredentials;
  twitter: OAuthCredentials;
}

const OAuthCredentialsTest: React.FC = () => {
  const { toast } = useToast();
  const [credentials, setCredentials] = useState<PlatformCredentials>({
    instagram: { clientId: '', clientSecret: '' },
    facebook: { clientId: '', clientSecret: '' },
    linkedin: { clientId: '', clientSecret: '' },
    twitter: { clientId: '', clientSecret: '' }
  });
  
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, 'idle' | 'testing' | 'success' | 'error'>>({});
  const [testErrors, setTestErrors] = useState<Record<string, string>>({});

  const platforms = [
    { 
      key: 'linkedin' as keyof PlatformCredentials, 
      name: 'LinkedIn', 
      icon: '💼',
      description: 'Connect your LinkedIn profile or company page'
    },
    { 
      key: 'instagram' as keyof PlatformCredentials, 
      name: 'Instagram', 
      icon: '📷',
      description: 'Connect your Instagram account'
    },
    { 
      key: 'facebook' as keyof PlatformCredentials, 
      name: 'Facebook', 
      icon: '📘',
      description: 'Connect your Facebook page'
    },
    { 
      key: 'twitter' as keyof PlatformCredentials, 
      name: 'Twitter/X', 
      icon: '🐦',
      description: 'Connect your Twitter account'
    }
  ];

  const updateCredential = (platform: keyof PlatformCredentials, field: keyof OAuthCredentials, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
    
    // Clear test results when credentials change
    setTestResults(prev => ({ ...prev, [platform]: 'idle' }));
    setTestErrors(prev => ({ ...prev, [platform]: '' }));
  };

  const toggleSecretVisibility = (platform: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [platform]: !prev[platform]
    }));
  };

  const saveCredentialsToEnv = (platform: keyof PlatformCredentials) => {
    const creds = credentials[platform];
    if (!creds.clientId || !creds.clientSecret) {
      toast({
        title: "Missing Credentials",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    // Store in sessionStorage for testing (not recommended for production)
    sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`, creds.clientId);
    sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`, creds.clientSecret);
    
    // Update the environment variables temporarily
    (window as any).testOAuthCredentials = {
      ...(window as any).testOAuthCredentials,
      [`VITE_${platform.toUpperCase()}_CLIENT_ID`]: creds.clientId,
      [`VITE_${platform.toUpperCase()}_CLIENT_SECRET`]: creds.clientSecret
    };

    toast({
      title: "Credentials Saved",
      description: `${platforms.find(p => p.key === platform)?.name} credentials saved for testing`,
    });
  };

  const clearCredentials = (platform: keyof PlatformCredentials) => {
    setCredentials(prev => ({
      ...prev,
      [platform]: { clientId: '', clientSecret: '' }
    }));
    
    // Clear from sessionStorage
    sessionStorage.removeItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`);
    sessionStorage.removeItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`);
    
    // Clear test results
    setTestResults(prev => ({ ...prev, [platform]: 'idle' }));
    setTestErrors(prev => ({ ...prev, [platform]: '' }));

    toast({
      title: "Credentials Cleared",
      description: `${platforms.find(p => p.key === platform)?.name} credentials cleared`,
    });
  };

  const testOAuthConnection = async (platform: keyof PlatformCredentials) => {
    const creds = credentials[platform];
    if (!creds.clientId || !creds.clientSecret) {
      toast({
        title: "Missing Credentials",
        description: "Please enter both Client ID and Client Secret before testing",
        variant: "destructive",
      });
      return;
    }

    setTestResults(prev => ({ ...prev, [platform]: 'testing' }));
    setTestErrors(prev => ({ ...prev, [platform]: '' }));

    try {
      // Save credentials temporarily for testing
      saveCredentialsToEnv(platform);
      
      // Map platform key to display name
      const platformDisplayName = platforms.find(p => p.key === platform)?.name || platform;
      
      // Test the OAuth connection
      const result = await connectSocialAccount(platformDisplayName as any);
      
      if (result) {
        setTestResults(prev => ({ ...prev, [platform]: 'success' }));
        toast({
          title: "OAuth Test Successful!",
          description: `Successfully connected to ${platformDisplayName}`,
        });
      } else {
        throw new Error('Connection failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setTestResults(prev => ({ ...prev, [platform]: 'error' }));
      setTestErrors(prev => ({ ...prev, [platform]: errorMessage }));
      
      toast({
        title: "OAuth Test Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const getTestResultBadge = (platform: string) => {
    const result = testResults[platform];
    switch (result) {
      case 'testing':
        return <Badge variant="secondary">Testing...</Badge>;
      case 'success':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Success</Badge>;
      case 'error':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      default:
        return <Badge variant="outline">Not Tested</Badge>;
    }
  };

  const PlatformCredentialCard = ({ platform }: { platform: typeof platforms[0] }) => {
    const creds = credentials[platform.key];
    const isComplete = creds.clientId && creds.clientSecret;
    
    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{platform.icon}</span>
              <div>
                <CardTitle className="text-lg">{platform.name}</CardTitle>
                <CardDescription>{platform.description}</CardDescription>
              </div>
            </div>
            {getTestResultBadge(platform.key)}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor={`${platform.key}-client-id`}>Client ID</Label>
              <Input
                id={`${platform.key}-client-id`}
                type="text"
                placeholder="Enter your client ID"
                value={creds.clientId}
                onChange={(e) => updateCredential(platform.key, 'clientId', e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor={`${platform.key}-client-secret`}>Client Secret</Label>
              <div className="relative mt-1">
                <Input
                  id={`${platform.key}-client-secret`}
                  type={showSecrets[platform.key] ? "text" : "password"}
                  placeholder="Enter your client secret"
                  value={creds.clientSecret}
                  onChange={(e) => updateCredential(platform.key, 'clientSecret', e.target.value)}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => toggleSecretVisibility(platform.key)}
                >
                  {showSecrets[platform.key] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>

          {testErrors[platform.key] && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{testErrors[platform.key]}</AlertDescription>
            </Alert>
          )}

          <div className="flex space-x-2">
            <Button
              onClick={() => saveCredentialsToEnv(platform.key)}
              disabled={!isComplete}
              variant="outline"
              size="sm"
            >
              <Save className="w-4 h-4 mr-2" />
              Save for Testing
            </Button>
            
            <Button
              onClick={() => testOAuthConnection(platform.key)}
              disabled={!isComplete || testResults[platform.key] === 'testing'}
              size="sm"
            >
              <TestTube className="w-4 h-4 mr-2" />
              {testResults[platform.key] === 'testing' ? 'Testing...' : 'Test OAuth'}
            </Button>
            
            <Button
              onClick={() => clearCredentials(platform.key)}
              variant="outline"
              size="sm"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">OAuth Credentials Testing</h1>
        <p className="text-muted-foreground">
          Enter your OAuth credentials to test the integration. Start with LinkedIn since you have a company page set up.
        </p>
      </div>

      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Testing Mode:</strong> Credentials entered here are stored temporarily in your browser session for testing only. 
          For production, add them to your .env file as shown in the OAuth Setup guide.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {platforms.map(platform => (
          <PlatformCredentialCard key={platform.key} platform={platform} />
        ))}
      </div>

      <Alert className="mt-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Next Steps:</strong> After successful testing, add your credentials to the .env file:
          <br />
          <code className="text-xs bg-gray-100 p-1 rounded mt-2 block">
            VITE_LINKEDIN_CLIENT_ID=your_client_id<br />
            VITE_LINKEDIN_CLIENT_SECRET=your_client_secret
          </code>
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default OAuthCredentialsTest;

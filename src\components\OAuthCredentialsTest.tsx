import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Eye,
  EyeOff,
  TestTube,
  CheckCircle,
  XCircle,
  AlertCircle,
  Save,
  Trash2,
  Shield,
  Lock,
  Database
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useOAuthCredentials, type OAuthCredentialInput } from '@/services/oauthCredentialsService';
import { isEncryptionSupported } from '@/services/encryptionService';
import { useAppStore } from '@/store/appStore';

interface OAuthCredentials {
  clientId: string;
  clientSecret: string;
}

interface PlatformCredentials {
  instagram: OAuthCredentials;
  facebook: OAuthCredentials;
  linkedin: OAuthCredentials;
  twitter: OAuthCredentials;
}

const OAuthCredentialsTest: React.FC = () => {
  const { toast } = useToast();
  const { workspace } = useAppStore();
  const oauthService = useOAuthCredentials();

  const [credentials, setCredentials] = useState<PlatformCredentials>({
    instagram: { clientId: '', clientSecret: '' },
    facebook: { clientId: '', clientSecret: '' },
    linkedin: { clientId: '', clientSecret: '' },
    twitter: { clientId: '', clientSecret: '' }
  });

  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [testResults, setTestResults] = useState<Record<string, 'idle' | 'testing' | 'success' | 'error'>>({});
  const [testErrors, setTestErrors] = useState<Record<string, string>>({});
  const [savedCredentials, setSavedCredentials] = useState<Record<string, boolean>>({});
  const [isEncryptionReady, setIsEncryptionReady] = useState(false);

  const platforms = [
    {
      key: 'linkedin' as keyof PlatformCredentials,
      name: 'LinkedIn',
      icon: '💼',
      description: 'Connect your LinkedIn profile or company page'
    },
    {
      key: 'instagram' as keyof PlatformCredentials,
      name: 'Instagram',
      icon: '📷',
      description: 'Connect your Instagram account'
    },
    {
      key: 'facebook' as keyof PlatformCredentials,
      name: 'Facebook',
      icon: '📘',
      description: 'Connect your Facebook page'
    },
    {
      key: 'twitter' as keyof PlatformCredentials,
      name: 'Twitter/X',
      icon: '🐦',
      description: 'Connect your Twitter account'
    }
  ];

  // Initialize encryption and load saved credentials
  useEffect(() => {
    const initializeService = async () => {
      if (!workspace?.id) return;

      try {
        // Initialize encryption
        await oauthService.initializeEncryption();
        setIsEncryptionReady(true);

        // Set workspace context
        oauthService.setWorkspace(workspace.id);

        // Load existing credentials
        const existingCredentials = await oauthService.getCredentials();
        const savedStatus: Record<string, boolean> = {};

        for (const cred of existingCredentials) {
          savedStatus[cred.platform] = true;
          setTestResults(prev => ({
            ...prev,
            [cred.platform]: cred.verificationStatus === 'verified' ? 'success' : 'idle'
          }));
        }

        setSavedCredentials(savedStatus);
      } catch (error) {
        console.error('Failed to initialize OAuth service:', error);
        toast({
          title: "Initialization Failed",
          description: "Failed to initialize secure credential storage",
          variant: "destructive",
        });
      }
    };

    initializeService();
  }, [workspace?.id, oauthService, toast]);

  const updateCredential = (platform: keyof PlatformCredentials, field: keyof OAuthCredentials, value: string) => {
    setCredentials(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
    
    // Clear test results when credentials change
    setTestResults(prev => ({ ...prev, [platform]: 'idle' }));
    setTestErrors(prev => ({ ...prev, [platform]: '' }));
  };

  const toggleSecretVisibility = (platform: string) => {
    setShowSecrets(prev => ({
      ...prev,
      [platform]: !prev[platform]
    }));
  };

  const saveCredentialsSecurely = async (platform: keyof PlatformCredentials) => {
    const creds = credentials[platform];
    if (!creds.clientId || !creds.clientSecret) {
      toast({
        title: "Missing Credentials",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    if (!isEncryptionReady) {
      toast({
        title: "Encryption Not Ready",
        description: "Please wait for encryption to initialize",
        variant: "destructive",
      });
      return;
    }

    try {
      const platformInfo = platforms.find(p => p.key === platform);
      const input: OAuthCredentialInput = {
        platform: platform,
        platformDisplayName: platformInfo?.name || platform,
        clientId: creds.clientId,
        clientSecret: creds.clientSecret
      };

      await oauthService.saveCredentials(input);

      setSavedCredentials(prev => ({ ...prev, [platform]: true }));

      toast({
        title: "Credentials Saved Securely",
        description: `${platformInfo?.name} credentials encrypted and saved to database`,
      });
    } catch (error) {
      console.error('Failed to save credentials:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save credentials securely",
        variant: "destructive",
      });
    }
  };

  const clearCredentials = async (platform: keyof PlatformCredentials) => {
    try {
      // Clear from database
      await oauthService.deleteCredentials(platform);

      // Clear from local state
      setCredentials(prev => ({
        ...prev,
        [platform]: { clientId: '', clientSecret: '' }
      }));

      setSavedCredentials(prev => ({ ...prev, [platform]: false }));
      setTestResults(prev => ({ ...prev, [platform]: 'idle' }));
      setTestErrors(prev => ({ ...prev, [platform]: '' }));

      toast({
        title: "Credentials Deleted",
        description: `${platforms.find(p => p.key === platform)?.name} credentials securely deleted`,
      });
    } catch (error) {
      console.error('Failed to delete credentials:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete credentials",
        variant: "destructive",
      });
    }
  };

  const testOAuthConnection = async (platform: keyof PlatformCredentials) => {
    if (!savedCredentials[platform]) {
      toast({
        title: "Save Credentials First",
        description: "Please save your credentials securely before testing",
        variant: "destructive",
      });
      return;
    }

    setTestResults(prev => ({ ...prev, [platform]: 'testing' }));
    setTestErrors(prev => ({ ...prev, [platform]: '' }));

    try {
      // Test using saved credentials
      const success = await oauthService.testCredentials(platform);

      if (success) {
        setTestResults(prev => ({ ...prev, [platform]: 'success' }));
        toast({
          title: "OAuth Test Successful!",
          description: `Successfully verified ${platforms.find(p => p.key === platform)?.name} credentials`,
        });
      } else {
        throw new Error('Credential verification failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setTestResults(prev => ({ ...prev, [platform]: 'error' }));
      setTestErrors(prev => ({ ...prev, [platform]: errorMessage }));

      toast({
        title: "OAuth Test Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const getTestResultBadge = (platform: string) => {
    const result = testResults[platform];
    switch (result) {
      case 'testing':
        return <Badge variant="secondary">Testing...</Badge>;
      case 'success':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Success</Badge>;
      case 'error':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>;
      default:
        return <Badge variant="outline">Not Tested</Badge>;
    }
  };

  const PlatformCredentialCard = ({ platform }: { platform: typeof platforms[0] }) => {
    const creds = credentials[platform.key];
    const isComplete = creds.clientId && creds.clientSecret;
    const isSaved = savedCredentials[platform.key];

    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-2xl">{platform.icon}</span>
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  {platform.name}
                  {isSaved && <Shield className="w-4 h-4 text-green-600" />}
                </CardTitle>
                <CardDescription>{platform.description}</CardDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isSaved && <Badge variant="outline"><Database className="w-3 h-3 mr-1" />Saved</Badge>}
              {getTestResultBadge(platform.key)}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor={`${platform.key}-client-id`}>Client ID</Label>
              <Input
                id={`${platform.key}-client-id`}
                type="text"
                placeholder="Enter your client ID"
                value={creds.clientId}
                onChange={(e) => updateCredential(platform.key, 'clientId', e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor={`${platform.key}-client-secret`}>Client Secret</Label>
              <div className="relative mt-1">
                <Input
                  id={`${platform.key}-client-secret`}
                  type={showSecrets[platform.key] ? "text" : "password"}
                  placeholder="Enter your client secret"
                  value={creds.clientSecret}
                  onChange={(e) => updateCredential(platform.key, 'clientSecret', e.target.value)}
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => toggleSecretVisibility(platform.key)}
                >
                  {showSecrets[platform.key] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>

          {testErrors[platform.key] && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{testErrors[platform.key]}</AlertDescription>
            </Alert>
          )}

          <div className="flex space-x-2">
            <Button
              onClick={() => saveCredentialsSecurely(platform.key)}
              disabled={!isComplete || !isEncryptionReady}
              variant="outline"
              size="sm"
            >
              <Lock className="w-4 h-4 mr-2" />
              {isSaved ? 'Update' : 'Save Securely'}
            </Button>

            <Button
              onClick={() => testOAuthConnection(platform.key)}
              disabled={!isSaved || testResults[platform.key] === 'testing'}
              size="sm"
            >
              <TestTube className="w-4 h-4 mr-2" />
              {testResults[platform.key] === 'testing' ? 'Testing...' : 'Test OAuth'}
            </Button>

            <Button
              onClick={() => clearCredentials(platform.key)}
              variant="outline"
              size="sm"
              disabled={!isSaved}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Shield className="w-6 h-6 text-green-600" />
          Secure OAuth Credentials Management
        </h1>
        <p className="text-muted-foreground">
          Enter your OAuth credentials to connect your social media accounts. All credentials are encrypted and stored securely in your database.
        </p>
      </div>

      {!isEncryptionSupported() && (
        <Alert className="mb-6" variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Encryption Not Supported:</strong> Your browser doesn't support the Web Crypto API.
            Please use a modern browser for secure credential storage.
          </AlertDescription>
        </Alert>
      )}

      {!isEncryptionReady && isEncryptionSupported() && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Initializing Encryption:</strong> Setting up secure credential storage...
          </AlertDescription>
        </Alert>
      )}

      <Alert className="mb-6">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Enterprise-Grade Security:</strong> Your OAuth credentials are encrypted client-side using AES-256-GCM
          before being stored in the database. Only you can decrypt and access your credentials.
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {platforms.map(platform => (
          <PlatformCredentialCard key={platform.key} platform={platform} />
        ))}
      </div>

      <Alert className="mt-6">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Production Ready:</strong> Your credentials are now securely stored and ready for production use.
          No need to manage .env files or worry about credential security - everything is handled automatically.
        </AlertDescription>
      </Alert>
    </div>
  );
};

export default OAuthCredentialsTest;

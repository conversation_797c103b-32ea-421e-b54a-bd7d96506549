# AutoMarketer Command Center - Production Setup Guide

This guide will help you deploy the AutoMarketer Command Center for production use with real social media APIs and full functionality.

## 🚀 Quick Start

1. **Clone and Setup**
   ```bash
   git clone <your-repo>
   cd automarketer-command-center
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your actual credentials
   ```

3. **Build and Deploy**
   ```bash
   npm run build
   npm run preview  # Test production build locally
   ```

## 🔧 Environment Configuration

### Required OAuth Credentials

#### Instagram (via Facebook)
1. Go to [Facebook Developers](https://developers.facebook.com/apps/)
2. Create a new app or select existing
3. Add "Instagram Basic Display" product
4. Configure OAuth redirect URI: `https://yourdomain.com/auth/instagram/callback`
5. Copy Client ID and Secret to `.env`

#### Facebook
1. Go to [Facebook Developers](https://developers.facebook.com/apps/)
2. Add "Facebook Login" product
3. Configure OAuth redirect URI: `https://yourdomain.com/auth/facebook/callback`
4. Copy App ID and Secret to `.env`

#### LinkedIn
1. Go to [LinkedIn Developers](https://www.linkedin.com/developers/apps)
2. Create a new app
3. Add "Sign In with LinkedIn" product
4. Configure OAuth redirect URI: `https://yourdomain.com/auth/linkedin/callback`
5. Copy Client ID and Secret to `.env`

#### Twitter/X
1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
2. Create a new project and app
3. Enable OAuth 2.0 in User authentication settings
4. Configure callback URI: `https://yourdomain.com/auth/twitter/callback`
5. Copy Client ID and Secret to `.env`

### Feature Flags

```env
# Enable real posting to social media
VITE_ENABLE_REAL_TIME_POSTING=true

# Enable analytics features
VITE_ENABLE_ANALYTICS=true

# Enable AI content generation
VITE_ENABLE_AI_CONTENT=true
```

## 🏗️ Backend API Setup

The frontend is designed to work with a backend API. Here's what you need:

### API Endpoints Required

```
POST /api/auth/login
POST /api/auth/register
GET  /api/social-accounts
POST /api/social-accounts/{platform}/connect
DELETE /api/social-accounts/{platform}
GET  /api/posts
POST /api/posts/schedule
GET  /api/analytics/dashboard
POST /api/content/generate
```

### Database Schema

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Social accounts table
CREATE TABLE social_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  platform VARCHAR(50) NOT NULL,
  platform_user_id VARCHAR(255),
  username VARCHAR(255),
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP,
  connected BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Posts table
CREATE TABLE posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  social_account_id UUID REFERENCES social_accounts(id),
  content TEXT NOT NULL,
  platform VARCHAR(50) NOT NULL,
  scheduled_for TIMESTAMP,
  published_at TIMESTAMP,
  status VARCHAR(20) DEFAULT 'scheduled',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Analytics table
CREATE TABLE post_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES posts(id),
  likes INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  reach INTEGER DEFAULT 0,
  impressions INTEGER DEFAULT 0,
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔐 Security Considerations

### OAuth Security
- Store client secrets server-side only
- Use HTTPS in production
- Implement proper CORS policies
- Validate OAuth state parameters

### API Security
- Implement JWT authentication
- Use rate limiting
- Validate all inputs
- Encrypt sensitive data at rest

### Environment Variables
```env
# Never commit these to version control
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=your-32-character-encryption-key
DATABASE_URL=postgresql://user:pass@host:port/db
```

## 📊 Analytics Integration

### Real-time Analytics
The app supports real-time analytics when connected to your backend:

```typescript
// Example analytics data structure
interface AnalyticsData {
  totalPosts: number;
  connectedAccounts: number;
  engagementRate: number;
  scheduledPosts: number;
  platformMetrics: {
    [platform: string]: {
      followers: number;
      engagement: number;
      growth: number;
    };
  };
}
```

## 🤖 AI Content Generation

### OpenAI Integration
```env
VITE_OPENAI_API_KEY=your-openai-api-key
```

### Backend Implementation
```javascript
// Example AI content generation endpoint
app.post('/api/content/generate', async (req, res) => {
  const { topic, platform } = req.body;
  
  const completion = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{
      role: "system",
      content: `Generate engaging ${platform} content about ${topic}`
    }],
    max_tokens: 500
  });
  
  res.json({
    success: true,
    data: {
      content: completion.choices[0].message.content,
      hashtags: extractHashtags(completion.choices[0].message.content)
    }
  });
});
```

## 🚀 Deployment Options

### Vercel (Recommended for Frontend)
```bash
npm install -g vercel
vercel --prod
```

### Netlify
```bash
npm run build
# Upload dist/ folder to Netlify
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 8080
CMD ["npm", "run", "preview"]
```

### Traditional Hosting
```bash
npm run build
# Upload dist/ folder to your web server
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - run: npm run test
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📈 Monitoring & Maintenance

### Error Tracking
```env
SENTRY_DSN=your-sentry-dsn
```

### Performance Monitoring
- Use Lighthouse for performance audits
- Monitor Core Web Vitals
- Set up uptime monitoring

### Regular Maintenance
- Update OAuth tokens before expiration
- Monitor API rate limits
- Review and update dependencies
- Backup user data regularly

## 🆘 Troubleshooting

### Common Issues

1. **OAuth Callback Errors**
   - Check redirect URIs match exactly
   - Verify HTTPS in production
   - Check OAuth app permissions

2. **API Connection Issues**
   - Verify CORS settings
   - Check API endpoint URLs
   - Validate authentication tokens

3. **Build Errors**
   - Clear node_modules and reinstall
   - Check environment variables
   - Verify all dependencies are installed

### Support
- Check the GitHub issues
- Review API documentation
- Test with demo mode first

## 📝 License & Legal

- Ensure compliance with platform APIs
- Review terms of service for each platform
- Implement proper data privacy measures
- Consider GDPR/CCPA compliance if applicable

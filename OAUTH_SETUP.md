# OAuth Setup Guide for PulseBuzz.AI

This comprehensive guide will help you set up OAuth authentication for social media platforms in PulseBuzz.AI.

## 🚀 Quick Start

PulseBuzz.AI works in **Demo Mode** without OAuth setup. To enable real social media integration, follow this guide to configure OAuth for each platform.

## 📋 Overview

PulseBuzz.AI supports OAuth integration with:
- **Instagram** (via Facebook Developers)
- **Facebook** (Pages and Business accounts)
- **LinkedIn** (Personal and Company pages)
- **Twitter/X** (Personal accounts)

## ✅ Prerequisites

Before setting up OAuth, ensure you have:
1. A PulseBuzz.AI account
2. Developer accounts on the social media platforms you want to integrate
3. Access to your application's environment variables
4. A deployed application with HTTPS (for production)

## 🔧 Platform-Specific Setup

### 1. Create OAuth Applications

Create developer applications for each platform you want to support:

#### Instagram
- Go to [Facebook Developers](https://developers.facebook.com/apps/)
- Create a new app or select existing
- Add "Instagram Basic Display" product
- Set redirect URI: `http://localhost:8081/auth/instagram/callback`
- Copy the Instagram App ID

#### Facebook
- Go to [Facebook Developers](https://developers.facebook.com/apps/)
- Create a new app or select existing
- Add "Facebook Login" product
- Set redirect URI: `http://localhost:8081/auth/facebook/callback`
- Copy the App ID

#### LinkedIn
- Go to [LinkedIn Developers](https://www.linkedin.com/developers/apps)
- Create a new app
- Add "Sign In with LinkedIn" product
- Set redirect URI: `http://localhost:8081/auth/linkedin/callback`
- Copy the Client ID

#### Twitter
- Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
- Create a new project and app
- Enable OAuth 2.0 in User authentication settings
- Set callback URI: `http://localhost:8081/auth/twitter/callback`
- Copy the Client ID

### 2. Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Fill in your OAuth credentials:
   ```env
   REACT_APP_INSTAGRAM_CLIENT_ID=your-actual-instagram-client-id
   REACT_APP_FACEBOOK_CLIENT_ID=your-actual-facebook-client-id
   REACT_APP_LINKEDIN_CLIENT_ID=your-actual-linkedin-client-id
   REACT_APP_TWITTER_CLIENT_ID=your-actual-twitter-client-id
   ```

3. Restart the development server:
   ```bash
   npm run dev
   ```

### 3. Test OAuth Connections

1. Click "Show OAuth Setup" in the app to see the setup guide
2. Click "Connect Account" on any platform
3. You'll be redirected to the platform's OAuth flow
4. After authorization, you'll be redirected back with real account data

## Security Notes

⚠️ **Important for Production:**

- Never expose client secrets in frontend code
- Implement proper server-side token exchange
- Use HTTPS for all OAuth redirects
- Store tokens securely (not in localStorage)
- Implement token refresh mechanisms

## Troubleshooting

### "Popup blocked" error
- Allow popups for your localhost domain
- Try clicking the connect button again

### "Invalid redirect URI" error
- Make sure your OAuth app redirect URIs exactly match the callback URLs
- Check for trailing slashes or protocol mismatches

### "Demo Mode" message
- This means your OAuth credentials aren't configured
- Check your `.env` file and restart the dev server

### CORS errors
- Some platforms require server-side token exchange
- Consider implementing a backend proxy for production

## Development vs Production

**Development (localhost):**
- Use `http://localhost:8081/auth/*/callback` for redirect URIs
- OAuth credentials can be in `.env` file

**Production:**
- Use `https://yourdomain.com/auth/*/callback` for redirect URIs
- Implement server-side OAuth flow
- Use environment variables on your hosting platform
- Never commit real credentials to version control

## Need Help?

1. Check the in-app OAuth Setup Guide (click "Show OAuth Setup")
2. Verify your redirect URIs match exactly
3. Check browser console for detailed error messages
4. Ensure your OAuth apps have the correct permissions/scopes

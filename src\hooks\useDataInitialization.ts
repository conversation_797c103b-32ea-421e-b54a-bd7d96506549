import { useEffect, useState } from 'react';
import { useAppStore } from '@/store/appStore';
import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';

export const useDataInitialization = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);
  
  const {
    loadWorkspace,
    loadSocialAccounts,
    loadPosts,
    loadCategories,
    loadMediaItems,
    loadInboxItems,
    addNotification
  } = useAppStore();

  useEffect(() => {
    let mounted = true;

    const initializeData = async () => {
      try {
        // Check if user is authenticated
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError) {
          throw new Error('Authentication error: ' + authError.message);
        }

        if (!user) {
          // User not authenticated, don't initialize data
          setIsInitialized(true);
          return;
        }

        // Load data in sequence to handle dependencies
        console.log('Initializing app data...');
        
        // 1. Load workspace first (required for other data)
        await loadWorkspace();
        
        // 2. Load social accounts (independent)
        await loadSocialAccounts();
        
        // 3. Load workspace-dependent data in parallel
        await Promise.all([
          loadPosts(),
          loadCategories(),
          loadMediaItems(),
          loadInboxItems()
        ]);

        if (mounted) {
          setIsInitialized(true);
          console.log('App data initialized successfully');
        }
      } catch (error) {
        console.error('Failed to initialize app data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        
        if (mounted) {
          setInitError(errorMessage);
          addNotification('error', 'Failed to load app data: ' + errorMessage);
        }
      }
    };

    initializeData();

    // Cleanup function
    return () => {
      mounted = false;
    };
  }, [loadWorkspace, loadSocialAccounts, loadPosts, loadCategories, loadMediaItems, loadInboxItems, addNotification]);

  // Set up real-time subscriptions for data updates
  useEffect(() => {
    if (!isInitialized) return;

    const subscriptions: Array<{ unsubscribe: () => void }> = [];

    // Subscribe to workspace changes
    const workspaceSubscription = supabase
      .channel('workspace_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'workspaces' },
        (payload) => {
          console.log('Workspace change detected:', payload);
          loadWorkspace();
        }
      )
      .subscribe();

    // Subscribe to social accounts changes
    const accountsSubscription = supabase
      .channel('accounts_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'social_accounts' },
        (payload) => {
          console.log('Social accounts change detected:', payload);
          loadSocialAccounts();
        }
      )
      .subscribe();

    // Subscribe to posts changes
    const postsSubscription = supabase
      .channel('posts_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'social_posts' },
        (payload) => {
          console.log('Posts change detected:', payload);
          loadPosts();
        }
      )
      .subscribe();

    // Subscribe to categories changes
    const categoriesSubscription = supabase
      .channel('categories_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'content_categories' },
        (payload) => {
          console.log('Categories change detected:', payload);
          loadCategories();
        }
      )
      .subscribe();

    // Subscribe to media changes
    const mediaSubscription = supabase
      .channel('media_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'media_library' },
        (payload) => {
          console.log('Media change detected:', payload);
          loadMediaItems();
        }
      )
      .subscribe();

    // Subscribe to inbox changes
    const inboxSubscription = supabase
      .channel('inbox_changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'social_inbox' },
        (payload) => {
          console.log('Inbox change detected:', payload);
          loadInboxItems();
        }
      )
      .subscribe();

    subscriptions.push(
      workspaceSubscription,
      accountsSubscription,
      postsSubscription,
      categoriesSubscription,
      mediaSubscription,
      inboxSubscription
    );

    // Cleanup subscriptions
    return () => {
      subscriptions.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
    };
  }, [isInitialized, loadWorkspace, loadSocialAccounts, loadPosts, loadCategories, loadMediaItems, loadInboxItems]);

  return {
    isInitialized,
    initError,
    retry: () => {
      setInitError(null);
      setIsInitialized(false);
      // The useEffect will trigger re-initialization
    }
  };
};

// Hook for checking if user has a workspace
export const useWorkspaceCheck = () => {
  const { workspace, createWorkspace, isLoading } = useAppStore();
  const [needsWorkspace, setNeedsWorkspace] = useState(false);

  useEffect(() => {
    // Check if user needs to create a workspace
    if (!isLoading && !workspace) {
      setNeedsWorkspace(true);
    } else {
      setNeedsWorkspace(false);
    }
  }, [workspace, isLoading]);

  const handleCreateWorkspace = async (name: string, description?: string) => {
    try {
      await createWorkspace({ name, description });
      setNeedsWorkspace(false);
    } catch (error) {
      console.error('Failed to create workspace:', error);
      throw error;
    }
  };

  return {
    needsWorkspace,
    createWorkspace: handleCreateWorkspace,
    isLoading
  };
};

// Hook for managing authentication state
export const useAuthState = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      setIsLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        setIsLoading(false);
        
        if (event === 'SIGNED_OUT') {
          // Clear app state when user signs out
          window.location.reload();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return { user, isLoading };
};

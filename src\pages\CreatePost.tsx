import React, { useState, useEffect } from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import PageHeader from '@/components/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import {
  PenTool,
  Calendar,
  Clock,
  Hash,
  Image,
  Video,
  FileText,
  Send,
  Save,
  Users,
  Target,
  Zap,
  Upload,
  X,
  Plus,
  AlertCircle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { useAppStore } from '@/store/appStore';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import ErrorBoundary from '@/components/ErrorBoundary';

interface MediaFile {
  id: string;
  file: File;
  preview: string;
  type: 'image' | 'video';
}

const CreatePost: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // Access the app store with proper error handling
  const store = useAppStore();
  const { createPost, categories = [], workspace, socialAccounts = [] } = store;

  // Show loading if workspace is not ready
  if (!workspace) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <TopHeader />
            <main className="flex-1 flex items-center justify-center">
              <Card className="max-w-md">
                <CardHeader>
                  <CardTitle>Loading Workspace...</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                  <p className="text-center text-sm text-muted-foreground">
                    Setting up your workspace...
                  </p>
                </CardContent>
              </Card>
            </main>
          </div>
        </div>
      </SidebarProvider>
    );
  }

  // Form state
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [content, setContent] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [hashtags, setHashtags] = useState('');
  const [mentions, setMentions] = useState('');
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const [isEvergreen, setIsEvergreen] = useState(false);
  const [maxRecycles, setMaxRecycles] = useState(3);
  const [needsApproval, setNeedsApproval] = useState(false);
  const [postType, setPostType] = useState<'now' | 'scheduled' | 'draft'>('scheduled');

  // Available platforms
  const availablePlatforms = [
    { id: 'instagram', name: 'Instagram', icon: '📷', limit: 2200, color: 'bg-pink-100 text-pink-800' },
    { id: 'twitter', name: 'X (Twitter)', icon: '🐦', limit: 280, color: 'bg-blue-100 text-blue-800' },
    { id: 'facebook', name: 'Facebook', icon: '📘', limit: 63206, color: 'bg-blue-100 text-blue-800' },
    { id: 'linkedin', name: 'LinkedIn', icon: '💼', limit: 3000, color: 'bg-blue-100 text-blue-800' },
    { id: 'tiktok', name: 'TikTok', icon: '🎵', limit: 2200, color: 'bg-black text-white' },
    { id: 'youtube', name: 'YouTube', icon: '📺', limit: 5000, color: 'bg-red-100 text-red-800' }
  ];

  // Set default date and time
  useEffect(() => {
    const now = new Date();
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    setScheduledDate(tomorrow.toISOString().split('T')[0]);
    setScheduledTime('09:00');
  }, []);

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platformId) 
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const mediaFile: MediaFile = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        file,
        preview: URL.createObjectURL(file),
        type: file.type.startsWith('video/') ? 'video' : 'image'
      };
      setMediaFiles(prev => [...prev, mediaFile]);
    });
  };

  const removeMediaFile = (id: string) => {
    setMediaFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  };

  const getCharacterCount = () => {
    if (selectedPlatforms.length === 0) return '';
    const minLimit = Math.min(...selectedPlatforms.map(id => 
      availablePlatforms.find(p => p.id === id)?.limit || 280
    ));
    return `${content.length}/${minLimit}`;
  };

  const isContentValid = () => {
    if (selectedPlatforms.length === 0) return false;
    const minLimit = Math.min(...selectedPlatforms.map(id => 
      availablePlatforms.find(p => p.id === id)?.limit || 280
    ));
    return content.length <= minLimit;
  };

  const isFormValid = () => {
    const hasContent = content.trim().length > 0;
    const hasPlatforms = selectedPlatforms.length > 0;
    const hasValidContent = isContentValid();
    
    if (postType === 'scheduled') {
      return hasContent && hasPlatforms && hasValidContent && scheduledDate && scheduledTime;
    }
    
    return hasContent && hasPlatforms && hasValidContent;
  };

  const handleSubmit = async (action: 'draft' | 'schedule' | 'publish') => {
    if (!isFormValid()) {
      toast({
        title: "Form Validation Error",
        description: "Please fill in all required fields and ensure content meets platform limits.",
        variant: "destructive",
      });
      return;
    }

    try {
      const hashtagArray = hashtags.split(',').map(tag => tag.trim()).filter(Boolean);
      const mentionArray = mentions.split(',').map(mention => mention.trim()).filter(Boolean);
      
      for (const platformId of selectedPlatforms) {
        const platform = availablePlatforms.find(p => p.id === platformId);
        if (!platform) continue;

        let status = 'draft';
        let approvalStatus = 'approved';
        let scheduledFor = null;

        if (action === 'schedule') {
          status = 'scheduled';
          scheduledFor = `${scheduledDate}T${scheduledTime}:00`;
        } else if (action === 'publish') {
          status = 'published';
        }

        if (needsApproval && action !== 'draft') {
          approvalStatus = 'pending';
          status = 'pending_approval';
        }

        if (createPost) {
          await createPost({
            platform: platform.name,
            content: content.trim(),
            scheduled_for: scheduledFor,
            status,
            approval_status: approvalStatus,
            category_id: selectedCategory || null,
            media_urls: mediaFiles.map(f => f.preview),
            hashtags: hashtagArray,
            mentions: mentionArray,
            is_evergreen: isEvergreen,
            recycle_count: 0,
            max_recycles: maxRecycles,
            external_post_id: null,
            published_at: action === 'publish' ? new Date().toISOString() : null
          });
        }
      }

      // Reset form
      setContent('');
      setSelectedPlatforms([]);
      setHashtags('');
      setMentions('');
      setMediaFiles([]);
      setSelectedCategory('');

      const actionText = action === 'draft' ? 'saved as draft' : 
                        action === 'schedule' ? 'scheduled' : 'published';
      
      toast({
        title: "Success!",
        description: `Post ${actionText} successfully for ${selectedPlatforms.length} platform(s).`,
      });

      navigate('/');
    } catch (error) {
      console.error('Error creating post:', error);
      toast({
        title: "Error",
        description: "Failed to create post. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <ErrorBoundary>
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-background">
          <AppSidebar />
          <div className="flex-1 flex flex-col">
            <TopHeader />
            <main className="flex-1 overflow-auto">
              <PageHeader
                title="Create New Post"
                description="Create and schedule content across multiple social media platforms"
                icon={<PenTool className="w-8 h-8" />}
              />
              <div className="px-6 pb-6">
                <div className="max-w-6xl mx-auto">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Main Content Area */}
                    <div className="lg:col-span-2 space-y-6">
                      {/* Post Type Selection */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Target className="w-5 h-5" />
                            Post Type
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <Tabs value={postType} onValueChange={(value: 'now' | 'scheduled' | 'draft') => setPostType(value)}>
                            <TabsList className="grid w-full grid-cols-3">
                              <TabsTrigger value="now" className="flex items-center gap-2">
                                <Zap className="w-4 h-4" />
                                Post Now
                              </TabsTrigger>
                              <TabsTrigger value="scheduled" className="flex items-center gap-2">
                                <Calendar className="w-4 h-4" />
                                Schedule
                              </TabsTrigger>
                              <TabsTrigger value="draft" className="flex items-center gap-2">
                                <Save className="w-4 h-4" />
                                Save Draft
                              </TabsTrigger>
                            </TabsList>
                          </Tabs>
                        </CardContent>
                      </Card>

                      {/* Platform Selection */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Users className="w-5 h-5" />
                            Select Platforms
                          </CardTitle>
                          <CardDescription>
                            Choose which social media platforms to post to
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {availablePlatforms.map((platform) => (
                              <div
                                key={platform.id}
                                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                  selectedPlatforms.includes(platform.id)
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}
                                onClick={() => handlePlatformToggle(platform.id)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <span className="text-lg">{platform.icon}</span>
                                    <span className="font-medium">{platform.name}</span>
                                  </div>
                                  {selectedPlatforms.includes(platform.id) && (
                                    <CheckCircle className="w-5 h-5 text-blue-500" />
                                  )}
                                </div>
                                <div className="mt-2">
                                  <Badge variant="secondary" className="text-xs">
                                    {platform.limit} chars
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                          {selectedPlatforms.length === 0 && (
                            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                              <div className="flex items-center gap-2 text-amber-800">
                                <AlertCircle className="w-4 h-4" />
                                <span className="text-sm">Please select at least one platform</span>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      {/* Content Creation */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileText className="w-5 h-5" />
                            Content
                          </CardTitle>
                          <CardDescription>
                            Write your post content and add media
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* Content Textarea */}
                          <div className="space-y-2">
                            <Label htmlFor="content">Post Content *</Label>
                            <Textarea
                              id="content"
                              value={content}
                              onChange={(e) => setContent(e.target.value)}
                              placeholder="What's on your mind? Share your thoughts, updates, or insights..."
                              className={`min-h-[120px] resize-none ${
                                !isContentValid() && content.length > 0 ? 'border-red-500' : ''
                              }`}
                            />
                            <div className="flex justify-between items-center">
                              <span className={`text-sm ${
                                !isContentValid() && content.length > 0 ? 'text-red-500' : 'text-muted-foreground'
                              }`}>
                                {getCharacterCount()}
                              </span>
                              {!isContentValid() && content.length > 0 && (
                                <span className="text-sm text-red-500">Content exceeds platform limits</span>
                              )}
                            </div>
                          </div>

                          {/* Media Upload */}
                          <div className="space-y-2">
                            <Label htmlFor="media">Media Files</Label>
                            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                              <input
                                id="media"
                                type="file"
                                multiple
                                accept="image/*,video/*"
                                onChange={handleMediaUpload}
                                className="hidden"
                              />
                              <label htmlFor="media" className="cursor-pointer">
                                <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                                <p className="text-sm text-gray-600">
                                  Click to upload images or videos
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  Supports JPG, PNG, GIF, MP4, MOV
                                </p>
                              </label>
                            </div>

                            {/* Media Preview */}
                            {mediaFiles.length > 0 && (
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-4">
                                {mediaFiles.map((file) => (
                                  <div key={file.id} className="relative group">
                                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                                      {file.type === 'image' ? (
                                        <img
                                          src={file.preview}
                                          alt="Preview"
                                          className="w-full h-full object-cover"
                                        />
                                      ) : (
                                        <video
                                          src={file.preview}
                                          className="w-full h-full object-cover"
                                          muted
                                        />
                                      )}
                                    </div>
                                    <button
                                      onClick={() => removeMediaFile(file.id)}
                                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                      <X className="w-3 h-3" />
                                    </button>
                                    <div className="absolute bottom-1 left-1">
                                      <Badge variant="secondary" className="text-xs">
                                        {file.type === 'image' ? <Image className="w-3 h-3" /> : <Video className="w-3 h-3" />}
                                      </Badge>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Hashtags and Mentions */}
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="hashtags">Hashtags</Label>
                              <div className="relative">
                                <Hash className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                <Input
                                  id="hashtags"
                                  value={hashtags}
                                  onChange={(e) => setHashtags(e.target.value)}
                                  placeholder="marketing, socialmedia, business"
                                  className="pl-10"
                                />
                              </div>
                              <p className="text-xs text-muted-foreground">
                                Separate hashtags with commas
                              </p>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="mentions">Mentions</Label>
                              <div className="relative">
                                <Users className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                <Input
                                  id="mentions"
                                  value={mentions}
                                  onChange={(e) => setMentions(e.target.value)}
                                  placeholder="@username, @company"
                                  className="pl-10"
                                />
                              </div>
                              <p className="text-xs text-muted-foreground">
                                Separate mentions with commas
                              </p>
                            </div>
                          </div>

                          {/* Category Selection */}
                          <div className="space-y-2">
                            <Label htmlFor="category">Category</Label>
                            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a category (optional)" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="">No category</SelectItem>
                                {Array.isArray(categories) && categories.map((category) => (
                                  <SelectItem key={category.id} value={category.id}>
                                    {category.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Scheduling Options */}
                      {postType === 'scheduled' && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Calendar className="w-5 h-5" />
                              Schedule Settings
                            </CardTitle>
                            <CardDescription>
                              Set when your post should be published
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="scheduled-date">Date *</Label>
                                <Input
                                  id="scheduled-date"
                                  type="date"
                                  value={scheduledDate}
                                  onChange={(e) => setScheduledDate(e.target.value)}
                                  min={new Date().toISOString().split('T')[0]}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="scheduled-time">Time *</Label>
                                <Input
                                  id="scheduled-time"
                                  type="time"
                                  value={scheduledTime}
                                  onChange={(e) => setScheduledTime(e.target.value)}
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Advanced Options */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Settings className="w-5 h-5" />
                            Advanced Options
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Evergreen Content</Label>
                              <p className="text-sm text-muted-foreground">
                                Allow this post to be recycled automatically
                              </p>
                            </div>
                            <Switch
                              checked={isEvergreen}
                              onCheckedChange={setIsEvergreen}
                            />
                          </div>

                          {isEvergreen && (
                            <div className="space-y-2">
                              <Label htmlFor="max-recycles">Maximum Recycles</Label>
                              <Input
                                id="max-recycles"
                                type="number"
                                min="1"
                                max="10"
                                value={maxRecycles}
                                onChange={(e) => setMaxRecycles(parseInt(e.target.value) || 3)}
                              />
                            </div>
                          )}

                          <div className="flex items-center justify-between">
                            <div className="space-y-0.5">
                              <Label>Requires Approval</Label>
                              <p className="text-sm text-muted-foreground">
                                Post needs approval before publishing
                              </p>
                            </div>
                            <Switch
                              checked={needsApproval}
                              onCheckedChange={setNeedsApproval}
                            />
                          </div>
                        </CardContent>
                      </Card>

                      {/* Action Buttons */}
                      <Card>
                        <CardContent className="pt-6">
                          <div className="flex justify-end gap-3">
                            <Button variant="outline" onClick={() => navigate('/')}>
                              Cancel
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => handleSubmit('draft')}
                              disabled={!content.trim() || selectedPlatforms.length === 0}
                            >
                              <Save className="w-4 h-4 mr-2" />
                              Save Draft
                            </Button>
                            {postType === 'scheduled' ? (
                              <Button
                                onClick={() => handleSubmit('schedule')}
                                disabled={!isFormValid()}
                              >
                                <Calendar className="w-4 h-4 mr-2" />
                                Schedule Post
                              </Button>
                            ) : (
                              <Button
                                onClick={() => handleSubmit('publish')}
                                disabled={!isFormValid()}
                              >
                                <Send className="w-4 h-4 mr-2" />
                                Publish Now
                              </Button>
                            )}
                          </div>

                          {!isFormValid() && (
                            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                              <div className="flex items-start gap-2 text-amber-800">
                                <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                                <div className="text-sm">
                                  <p className="font-medium mb-1">Please complete the following:</p>
                                  <ul className="list-disc list-inside space-y-1">
                                    {!content.trim() && <li>Add post content</li>}
                                    {selectedPlatforms.length === 0 && <li>Select at least one platform</li>}
                                    {!isContentValid() && <li>Ensure content meets platform character limits</li>}
                                    {postType === 'scheduled' && !scheduledDate && <li>Set scheduled date</li>}
                                    {postType === 'scheduled' && !scheduledTime && <li>Set scheduled time</li>}
                                  </ul>
                                </div>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </div>

                    {/* AI Assistant Sidebar */}
                    <div className="lg:col-span-1">
                      <div className="sticky top-6">
                        <Card>
                          <CardHeader>
                            <CardTitle>AI Content Assistant</CardTitle>
                            <CardDescription>Get AI-powered content suggestions</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground mb-4">
                              AI content generation is coming soon! For now, use the manual content creation tools.
                            </p>
                            <Button variant="outline" className="w-full" disabled>
                              <Plus className="w-4 h-4 mr-2" />
                              Generate Content
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </SidebarProvider>
    </ErrorBoundary>
  );
};

export default CreatePost;

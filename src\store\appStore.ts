import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  WorkspaceService, 
  SocialAccountService, 
  PostService, 
  CategoryService, 
  MediaService, 
  AnalyticsService, 
  InboxService,
  ProfileService 
} from '@/services/supabaseService';
import { Database } from '@/integrations/supabase/types';

type Tables = Database['public']['Tables'];

// Use database types directly
type SocialAccount = Tables['social_accounts']['Row'];
type Workspace = Tables['workspaces']['Row'] & {
  members?: Tables['workspace_members']['Row'][];
};
type Post = Tables['social_posts']['Row'];
type Category = Tables['content_categories']['Row'];
type MediaItem = Tables['media_library']['Row'];
type InboxItem = Tables['social_inbox']['Row'];

// Export types for use in components
export type { SocialAccount, Workspace, Post, Category, MediaItem, InboxItem };

// Additional types for UI state
interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  timestamp: string;
}

interface AppState {
  // Data state
  socialAccounts: SocialAccount[];
  workspace: Workspace | null;
  posts: Post[];
  categories: Category[];
  mediaItems: MediaItem[];
  inboxItems: InboxItem[];
  
  // UI state
  isLoading: boolean;
  notifications: NotificationState[];
  
  // Data loading actions
  loadWorkspace: () => Promise<void>;
  loadSocialAccounts: () => Promise<void>;
  loadPosts: () => Promise<void>;
  loadCategories: () => Promise<void>;
  loadMediaItems: () => Promise<void>;
  loadInboxItems: () => Promise<void>;
  
  // Social Accounts actions
  connectAccount: (account: Omit<SocialAccount, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  disconnectAccount: (accountId: string) => Promise<void>;
  updateAccountStatus: (accountId: string, status: string) => Promise<void>;
  
  // Workspace actions
  createWorkspace: (workspace: { name: string; description?: string }) => Promise<void>;
  updateWorkspace: (updates: Partial<Workspace>) => Promise<void>;
  inviteMember: (email: string, role: string) => Promise<void>;
  updateMemberRole: (memberId: string, role: string) => Promise<void>;
  removeMember: (memberId: string) => Promise<void>;
  
  // Posts actions
  createPost: (post: Omit<Post, 'id' | 'author_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updatePost: (id: string, updates: Partial<Post>) => Promise<void>;
  deletePost: (id: string) => Promise<void>;
  
  // Categories actions
  createCategory: (category: Omit<Category, 'id' | 'created_at' | 'updated_at'>) => Promise<void>;
  updateCategory: (id: string, updates: Partial<Category>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  
  // Media actions
  uploadMedia: (file: File, altText?: string, tags?: string[]) => Promise<void>;
  deleteMedia: (id: string) => Promise<void>;
  
  // Inbox actions
  markAsRead: (id: string) => Promise<void>;
  markAsReplied: (id: string) => Promise<void>;
  updateInboxPriority: (id: string, priority: string) => Promise<void>;
  addInboxNote: (id: string, note: string) => Promise<void>;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  addNotification: (type: NotificationState['type'], message: string) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Computed getters
  connectedAccounts: SocialAccount[];
  scheduledPosts: Post[];
  unreadInboxCount: number;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      socialAccounts: [],
      workspace: null,
      posts: [],
      categories: [],
      mediaItems: [],
      inboxItems: [],
      isLoading: false,
      notifications: [],
      
      // Data loading actions
      loadWorkspace: async () => {
        try {
          set({ isLoading: true });
          let workspace = await WorkspaceService.getCurrentUserWorkspace();

          // If no workspace exists, create a default one
          if (!workspace) {
            console.log('No workspace found, creating default workspace');
            workspace = await WorkspaceService.createWorkspace({
              name: 'My Workspace',
              description: 'Default workspace for PulseBuzz.AI'
            });
          }

          set({ workspace });
        } catch (error) {
          console.error('Failed to load workspace:', error);

          // Check if this is a database setup issue
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          if (errorMessage.includes('relation') && errorMessage.includes('does not exist')) {
            get().addNotification('error', 'Database setup required. Please visit /setup to configure your database.');
          } else {
            get().addNotification('error', 'Failed to load workspace');
          }

          // Create a fallback workspace object to prevent crashes
          const fallbackWorkspace = {
            id: 'fallback-workspace',
            name: 'My Workspace',
            description: 'Default workspace',
            user_id: 'current-user',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            members: []
          };
          set({ workspace: fallbackWorkspace });
        } finally {
          set({ isLoading: false });
        }
      },
      
      loadSocialAccounts: async () => {
        try {
          const accounts = await SocialAccountService.getUserSocialAccounts();
          set({ socialAccounts: accounts });
        } catch (error) {
          console.error('Failed to load social accounts:', error);
          get().addNotification('error', 'Failed to load social accounts');
        }
      },
      
      loadPosts: async () => {
        try {
          const { workspace } = get();
          if (!workspace) return;
          
          const posts = await PostService.getWorkspacePosts(workspace.id);
          set({ posts });
        } catch (error) {
          console.error('Failed to load posts:', error);
          get().addNotification('error', 'Failed to load posts');
        }
      },
      
      loadCategories: async () => {
        try {
          const { workspace } = get();
          if (!workspace) return;

          const categories = await CategoryService.getWorkspaceCategories(workspace.id);
          set({ categories });
        } catch (error) {
          console.error('Failed to load categories:', error);

          // Set fallback categories if database tables don't exist
          const { workspace } = get();
          const fallbackCategories = [
            {
              id: 'cat-1',
              workspace_id: workspace?.id || 'fallback',
              name: 'Marketing',
              description: 'Marketing and promotional content',
              color: '#3B82F6',
              post_count: 0,
              is_active: true,
              recycle_enabled: false,
              recycle_interval: 30,
              max_recycles: 3,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'cat-2',
              workspace_id: workspace?.id || 'fallback',
              name: 'Educational',
              description: 'Educational and informative content',
              color: '#10B981',
              post_count: 0,
              is_active: true,
              recycle_enabled: false,
              recycle_interval: 30,
              max_recycles: 3,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'cat-3',
              workspace_id: workspace?.id || 'fallback',
              name: 'Behind the Scenes',
              description: 'Company culture and behind-the-scenes content',
              color: '#F59E0B',
              post_count: 0,
              is_active: true,
              recycle_enabled: false,
              recycle_interval: 30,
              max_recycles: 3,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            {
              id: 'cat-4',
              workspace_id: workspace?.id || 'fallback',
              name: 'News & Updates',
              description: 'Company news and product updates',
              color: '#EF4444',
              post_count: 0,
              is_active: true,
              recycle_enabled: false,
              recycle_interval: 30,
              max_recycles: 3,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          ];

          set({ categories: fallbackCategories });
        }
      },
      
      loadMediaItems: async () => {
        try {
          const { workspace } = get();
          if (!workspace) return;
          
          const mediaItems = await MediaService.getWorkspaceMedia(workspace.id);
          set({ mediaItems });
        } catch (error) {
          console.error('Failed to load media items:', error);
          get().addNotification('error', 'Failed to load media items');
        }
      },
      
      loadInboxItems: async () => {
        try {
          const { workspace } = get();
          if (!workspace) return;
          
          const inboxItems = await InboxService.getWorkspaceInbox(workspace.id);
          set({ inboxItems });
        } catch (error) {
          console.error('Failed to load inbox items:', error);
          get().addNotification('error', 'Failed to load inbox items');
        }
      },
      
      // Social Accounts actions
      connectAccount: async (accountData) => {
        try {
          set({ isLoading: true });
          console.log('Connecting account with data:', accountData);

          const account = await SocialAccountService.connectAccount({
            ...accountData,
            is_connected: accountData.is_connected ?? true,
            status: accountData.status ?? 'active',
            last_sync: accountData.last_sync ?? new Date().toISOString()
          });

          console.log('Account connected successfully:', account);

          set((state) => ({
            socialAccounts: [...(state.socialAccounts || []), account]
          }));

          get().addNotification('success', `${account.platform} account connected successfully!`);
          return account;
        } catch (error) {
          console.error('Failed to connect account:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to connect account';
          get().addNotification('error', errorMessage);
          throw error; // Re-throw to allow caller to handle
        } finally {
          set({ isLoading: false });
        }
      },
      
      disconnectAccount: async (accountId) => {
        try {
          await SocialAccountService.disconnectAccount(accountId);
          
          set((state) => ({
            socialAccounts: (state.socialAccounts || []).filter(account => account && account.id !== accountId)
          }));
          
          get().addNotification('success', 'Account disconnected successfully');
        } catch (error) {
          console.error('Failed to disconnect account:', error);
          get().addNotification('error', 'Failed to disconnect account');
        }
      },
      
      updateAccountStatus: async (accountId, status) => {
        try {
          const updatedAccount = await SocialAccountService.updateAccount(accountId, { 
            status,
            last_sync: new Date().toISOString()
          });
          
          set((state) => ({
            socialAccounts: (state.socialAccounts || []).map(account =>
              account && account.id === accountId ? updatedAccount : account
            )
          }));
        } catch (error) {
          console.error('Failed to update account status:', error);
          get().addNotification('error', 'Failed to update account status');
        }
      },
      
      // Workspace actions
      createWorkspace: async (workspaceData) => {
        try {
          set({ isLoading: true });
          const workspace = await WorkspaceService.createWorkspace(workspaceData);
          set({ workspace });
          get().addNotification('success', 'Workspace created successfully');
        } catch (error) {
          console.error('Failed to create workspace:', error);
          get().addNotification('error', 'Failed to create workspace');
        } finally {
          set({ isLoading: false });
        }
      },
      
      updateWorkspace: async (updates) => {
        try {
          const { workspace } = get();
          if (!workspace) return;

          const updatedWorkspace = await WorkspaceService.updateWorkspace(workspace.id, updates);
          set({ workspace: updatedWorkspace });
          get().addNotification('success', 'Workspace updated successfully');
        } catch (error) {
          console.error('Failed to update workspace:', error);
          get().addNotification('error', 'Failed to update workspace');
        }
      },

      inviteMember: async (email, role) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          // For now, just simulate the invitation
          console.log(`Inviting ${email} as ${role} to workspace ${workspace.id}`);
          get().addNotification('success', `Invitation sent to ${email}`);

          // In a real implementation, this would call a workspace service
          // const member = await WorkspaceService.inviteMember(workspace.id, email, role);
        } catch (error) {
          console.error('Failed to invite member:', error);
          get().addNotification('error', 'Failed to invite member');
        }
      },

      updateMemberRole: async (memberId, role) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          // For now, just simulate the role update
          console.log(`Updating member ${memberId} role to ${role}`);
          get().addNotification('success', 'Member role updated successfully');

          // In a real implementation, this would call a workspace service
          // const updatedMember = await WorkspaceService.updateMemberRole(workspace.id, memberId, role);
        } catch (error) {
          console.error('Failed to update member role:', error);
          get().addNotification('error', 'Failed to update member role');
        }
      },

      removeMember: async (memberId) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          // For now, just simulate the member removal
          console.log(`Removing member ${memberId} from workspace ${workspace.id}`);
          get().addNotification('success', 'Member removed successfully');

          // In a real implementation, this would call a workspace service
          // await WorkspaceService.removeMember(workspace.id, memberId);
        } catch (error) {
          console.error('Failed to remove member:', error);
          get().addNotification('error', 'Failed to remove member');
        }
      },

      // Posts actions
      createPost: async (postData) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          const post = await PostService.createPost({
            ...postData,
            workspace_id: workspace.id
          });

          set((state) => ({
            posts: [post, ...state.posts]
          }));

          get().addNotification('success', 'Post created successfully');
        } catch (error) {
          console.error('Failed to create post:', error);
          get().addNotification('error', 'Failed to create post');
        }
      },

      updatePost: async (id, updates) => {
        try {
          const updatedPost = await PostService.updatePost(id, updates);

          set((state) => ({
            posts: state.posts.map(post =>
              post.id === id ? updatedPost : post
            )
          }));

          get().addNotification('success', 'Post updated successfully');
        } catch (error) {
          console.error('Failed to update post:', error);
          get().addNotification('error', 'Failed to update post');
        }
      },

      deletePost: async (id) => {
        try {
          await PostService.deletePost(id);

          set((state) => ({
            posts: state.posts.filter(post => post.id !== id)
          }));

          get().addNotification('success', 'Post deleted successfully');
        } catch (error) {
          console.error('Failed to delete post:', error);
          get().addNotification('error', 'Failed to delete post');
        }
      },

      // Categories actions
      createCategory: async (categoryData) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          const category = await CategoryService.createCategory({
            ...categoryData,
            workspace_id: workspace.id
          });

          set((state) => ({
            categories: [...state.categories, category]
          }));

          get().addNotification('success', 'Category created successfully');
        } catch (error) {
          console.error('Failed to create category:', error);
          get().addNotification('error', 'Failed to create category');
        }
      },

      updateCategory: async (id, updates) => {
        try {
          const updatedCategory = await CategoryService.updateCategory(id, updates);

          set((state) => ({
            categories: state.categories.map(category =>
              category.id === id ? updatedCategory : category
            )
          }));

          get().addNotification('success', 'Category updated successfully');
        } catch (error) {
          console.error('Failed to update category:', error);
          get().addNotification('error', 'Failed to update category');
        }
      },

      deleteCategory: async (id) => {
        try {
          await CategoryService.deleteCategory(id);

          set((state) => ({
            categories: state.categories.filter(category => category.id !== id)
          }));

          get().addNotification('success', 'Category deleted successfully');
        } catch (error) {
          console.error('Failed to delete category:', error);
          get().addNotification('error', 'Failed to delete category');
        }
      },

      // Media actions
      uploadMedia: async (file, altText, tags) => {
        try {
          const { workspace } = get();
          if (!workspace) throw new Error('No workspace selected');

          set({ isLoading: true });
          const mediaItem = await MediaService.uploadMedia(file, workspace.id, altText, tags);

          set((state) => ({
            mediaItems: [mediaItem, ...state.mediaItems]
          }));

          get().addNotification('success', 'Media uploaded successfully');
        } catch (error) {
          console.error('Failed to upload media:', error);
          get().addNotification('error', 'Failed to upload media');
        } finally {
          set({ isLoading: false });
        }
      },

      deleteMedia: async (id) => {
        try {
          await MediaService.deleteMedia(id);

          set((state) => ({
            mediaItems: state.mediaItems.filter(item => item.id !== id)
          }));

          get().addNotification('success', 'Media deleted successfully');
        } catch (error) {
          console.error('Failed to delete media:', error);
          get().addNotification('error', 'Failed to delete media');
        }
      },

      // Inbox actions
      markAsRead: async (id) => {
        try {
          await InboxService.markAsRead(id);

          set((state) => ({
            inboxItems: state.inboxItems.map(item =>
              item.id === id ? { ...item, is_read: true } : item
            )
          }));
        } catch (error) {
          console.error('Failed to mark as read:', error);
          get().addNotification('error', 'Failed to mark as read');
        }
      },

      markAsReplied: async (id) => {
        try {
          await InboxService.markAsReplied(id);

          set((state) => ({
            inboxItems: state.inboxItems.map(item =>
              item.id === id ? { ...item, is_replied: true } : item
            )
          }));

          get().addNotification('success', 'Marked as replied');
        } catch (error) {
          console.error('Failed to mark as replied:', error);
          get().addNotification('error', 'Failed to mark as replied');
        }
      },

      updateInboxPriority: async (id, priority) => {
        try {
          await InboxService.updatePriority(id, priority);

          set((state) => ({
            inboxItems: state.inboxItems.map(item =>
              item.id === id ? { ...item, priority } : item
            )
          }));
        } catch (error) {
          console.error('Failed to update priority:', error);
          get().addNotification('error', 'Failed to update priority');
        }
      },

      addInboxNote: async (id, note) => {
        try {
          await InboxService.addInternalNote(id, note);

          set((state) => ({
            inboxItems: state.inboxItems.map(item =>
              item.id === id
                ? { ...item, internal_notes: [...(item.internal_notes || []), note] }
                : item
            )
          }));

          get().addNotification('success', 'Note added successfully');
        } catch (error) {
          console.error('Failed to add note:', error);
          get().addNotification('error', 'Failed to add note');
        }
      },

      // UI actions
      setLoading: (loading) => set({ isLoading: loading }),

      addNotification: (type, message) => {
        const notification: NotificationState = {
          id: Date.now().toString(),
          type,
          message,
          timestamp: new Date().toISOString()
        };

        set((state) => ({
          notifications: [...state.notifications, notification]
        }));

        // Auto-remove after 5 seconds
        setTimeout(() => {
          get().removeNotification(notification.id);
        }, 5000);
      },

      removeNotification: (id) => {
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        }));
      },

      clearNotifications: () => set({ notifications: [] }),

      // Computed getters
      get connectedAccounts() {
        const accounts = get().socialAccounts;
        return accounts ? accounts.filter(account => account && account.is_connected) : [];
      },

      get scheduledPosts() {
        const posts = get().posts;
        return posts ? posts.filter(post => post && post.status === 'scheduled') : [];
      },

      get unreadInboxCount() {
        const items = get().inboxItems;
        return items ? items.filter(item => item && !item.is_read).length : 0;
      }
    }),
    {
      name: 'pulsebuzz-app-store',
      partialize: (state) => ({
        // Only persist UI preferences, not data
        notifications: state.notifications
      })
    }
  )
);

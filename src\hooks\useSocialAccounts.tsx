
import { useState } from "react";

export interface SocialAccount {
  platform: string;
  connected: boolean;
  username: string;
  followers: string;
  accessToken?: string;
  userId?: string;
  pageId?: string; // For Facebook Pages
  authUrl?: string;
  authType: 'oauth' | 'token' | 'manual';
}

const initialAccounts: SocialAccount[] = [
  { 
    platform: "Instagram", 
    connected: true, 
    username: "@yourbrand", 
    followers: "2.4k",
    authType: 'oauth'
  },
  { 
    platform: "X (Twitter)", 
    connected: true, 
    username: "@yourbrand", 
    followers: "1.8k",
    authType: 'oauth'
  },
  { 
    platform: "Facebook", 
    connected: false, 
    username: "", 
    followers: "",
    authType: 'oauth'
  },
  { 
    platform: "LinkedIn", 
    connected: false, 
    username: "", 
    followers: "",
    authType: 'oauth'
  },
];

export const useSocialAccounts = () => {
  const [accounts, setAccounts] = useState<SocialAccount[]>(initialAccounts);

  const getAuthUrl = (platform: string): string => {
    const baseUrls = {
      "Instagram": "https://api.instagram.com/oauth/authorize",
      "Facebook": "https://www.facebook.com/v18.0/dialog/oauth",
      "LinkedIn": "https://www.linkedin.com/oauth/v2/authorization",
      "X (Twitter)": "https://twitter.com/i/oauth2/authorize"
    };
    
    const clientIds = {
      "Instagram": "YOUR_INSTAGRAM_CLIENT_ID",
      "Facebook": "YOUR_FACEBOOK_APP_ID", 
      "LinkedIn": "YOUR_LINKEDIN_CLIENT_ID",
      "X (Twitter)": "YOUR_TWITTER_CLIENT_ID"
    };

    const redirectUri = encodeURIComponent(window.location.origin + "/auth/callback");
    const clientId = clientIds[platform as keyof typeof clientIds];
    
    const scopes = {
      "Instagram": "user_profile,user_media",
      "Facebook": "pages_manage_posts,pages_read_engagement,pages_show_list",
      "LinkedIn": "openid,profile,email,w_member_social",
      "X (Twitter)": "tweet.read,tweet.write,users.read"
    };

    const scope = encodeURIComponent(scopes[platform as keyof typeof scopes]);
    const baseUrl = baseUrls[platform as keyof typeof baseUrls];
    
    return `${baseUrl}?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code`;
  };

  const connectAccount = (platform: string, credentials: { username: string; accessToken: string }) => {
    setAccounts(prev => prev.map(account => 
      account.platform === platform 
        ? { 
            ...account, 
            connected: true, 
            username: credentials.username,
            followers: generateRandomFollowers(),
            accessToken: credentials.accessToken
          }
        : account
    ));
  };

  const disconnectAccount = (platform: string) => {
    setAccounts(prev => prev.map(account =>
      account.platform === platform
        ? { ...account, connected: false, username: "", followers: "", accessToken: undefined }
        : account
    ));
  };

  const generateRandomFollowers = () => {
    const count = Math.floor(Math.random() * 9000) + 1000;
    return count > 1000 ? `${(count / 1000).toFixed(1)}k` : count.toString();
  };

  return {
    accounts,
    connectAccount,
    disconnectAccount,
    getAuthUrl,
  };
};

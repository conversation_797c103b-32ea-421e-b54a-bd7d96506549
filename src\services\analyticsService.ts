import { config, isFeatureEnabled } from '@/config/environment';
import { analyticsApi, ApiError } from './apiClient';

export interface DashboardStats {
  totalPosts: number;
  connectedAccounts: number;
  engagementRate: number;
  scheduledPosts: number;
  totalReach: number;
  totalImpressions: number;
  growthRate: number;
}

export interface PostAnalytics {
  postId: string;
  platform: string;
  publishedAt: string;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    reach: number;
    impressions: number;
    clicks: number;
    saves?: number; // Instagram specific
    retweets?: number; // Twitter specific
  };
  engagementRate: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

export interface PlatformAnalytics {
  platform: string;
  dateRange: string;
  followers: {
    current: number;
    growth: number;
    growthRate: number;
  };
  engagement: {
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    averageEngagementRate: number;
  };
  topPosts: PostAnalytics[];
  bestPostingTimes: string[];
  audienceDemographics?: {
    ageGroups: Record<string, number>;
    genders: Record<string, number>;
    locations: Record<string, number>;
  };
}

export interface EngagementMetrics {
  period: string;
  data: Array<{
    date: string;
    likes: number;
    comments: number;
    shares: number;
    reach: number;
    impressions: number;
  }>;
}

// Mock data generators for fallback
const generateMockDashboardStats = (): DashboardStats => ({
  totalPosts: Math.floor(Math.random() * 100) + 50,
  connectedAccounts: Math.floor(Math.random() * 4) + 1,
  engagementRate: Math.round((Math.random() * 5 + 2) * 100) / 100, // 2-7%
  scheduledPosts: Math.floor(Math.random() * 20) + 5,
  totalReach: Math.floor(Math.random() * 50000) + 10000,
  totalImpressions: Math.floor(Math.random() * 100000) + 25000,
  growthRate: Math.round((Math.random() * 20 + 5) * 100) / 100, // 5-25%
});

const generateMockPostAnalytics = (postId: string): PostAnalytics => {
  const platforms = ['Instagram', 'X (Twitter)', 'Facebook', 'LinkedIn'];
  const platform = platforms[Math.floor(Math.random() * platforms.length)];
  
  const likes = Math.floor(Math.random() * 1000) + 10;
  const comments = Math.floor(Math.random() * 100) + 1;
  const shares = Math.floor(Math.random() * 50) + 1;
  const reach = Math.floor(Math.random() * 5000) + 500;
  const impressions = reach + Math.floor(Math.random() * 2000);
  
  const totalEngagement = likes + comments + shares;
  const engagementRate = Math.round((totalEngagement / reach) * 10000) / 100;
  
  let performance: PostAnalytics['performance'] = 'average';
  if (engagementRate > 5) performance = 'excellent';
  else if (engagementRate > 3) performance = 'good';
  else if (engagementRate < 1) performance = 'poor';

  return {
    postId,
    platform,
    publishedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    metrics: {
      likes,
      comments,
      shares,
      reach,
      impressions,
      clicks: Math.floor(Math.random() * 200) + 10,
      ...(platform === 'Instagram' && { saves: Math.floor(Math.random() * 50) }),
      ...(platform === 'X (Twitter)' && { retweets: shares }),
    },
    engagementRate,
    performance,
  };
};

const generateMockPlatformAnalytics = (platform: string): PlatformAnalytics => {
  const currentFollowers = Math.floor(Math.random() * 10000) + 1000;
  const growth = Math.floor(Math.random() * 500) + 50;
  
  return {
    platform,
    dateRange: 'last_30_days',
    followers: {
      current: currentFollowers,
      growth,
      growthRate: Math.round((growth / currentFollowers) * 10000) / 100,
    },
    engagement: {
      totalLikes: Math.floor(Math.random() * 5000) + 500,
      totalComments: Math.floor(Math.random() * 1000) + 100,
      totalShares: Math.floor(Math.random() * 500) + 50,
      averageEngagementRate: Math.round((Math.random() * 4 + 2) * 100) / 100,
    },
    topPosts: Array.from({ length: 5 }, (_, i) => 
      generateMockPostAnalytics(`top-post-${i + 1}`)
    ),
    bestPostingTimes: ['9:00 AM', '1:00 PM', '6:00 PM', '8:00 PM'],
    audienceDemographics: {
      ageGroups: {
        '18-24': 25,
        '25-34': 35,
        '35-44': 25,
        '45-54': 10,
        '55+': 5,
      },
      genders: {
        'Male': 45,
        'Female': 52,
        'Other': 3,
      },
      locations: {
        'United States': 40,
        'United Kingdom': 15,
        'Canada': 12,
        'Australia': 8,
        'Other': 25,
      },
    },
  };
};

const generateMockEngagementMetrics = (): EngagementMetrics => {
  const data = [];
  const now = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    
    data.push({
      date: date.toISOString().split('T')[0],
      likes: Math.floor(Math.random() * 200) + 50,
      comments: Math.floor(Math.random() * 50) + 5,
      shares: Math.floor(Math.random() * 30) + 2,
      reach: Math.floor(Math.random() * 1000) + 200,
      impressions: Math.floor(Math.random() * 2000) + 500,
    });
  }
  
  return {
    period: 'last_30_days',
    data,
  };
};

// Main analytics functions
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    if (config.isProduction || isFeatureEnabled('analytics')) {
      const response = await analyticsApi.getDashboardStats();
      if (response.success && response.data) {
        return response.data;
      }
    }
    
    // Fallback to mock data
    return generateMockDashboardStats();
  } catch (error) {
    console.warn('Failed to fetch dashboard stats from API, using mock data:', error);
    return generateMockDashboardStats();
  }
};

export const getPostAnalytics = async (postId: string): Promise<PostAnalytics> => {
  try {
    if (config.isProduction || isFeatureEnabled('analytics')) {
      const response = await analyticsApi.getPostAnalytics(postId);
      if (response.success && response.data) {
        return response.data;
      }
    }
    
    // Fallback to mock data
    return generateMockPostAnalytics(postId);
  } catch (error) {
    console.warn('Failed to fetch post analytics from API, using mock data:', error);
    return generateMockPostAnalytics(postId);
  }
};

export const getPlatformAnalytics = async (
  platform: string, 
  dateRange: string = 'last_30_days'
): Promise<PlatformAnalytics> => {
  try {
    if (config.isProduction || isFeatureEnabled('analytics')) {
      const response = await analyticsApi.getPlatformAnalytics(platform, dateRange);
      if (response.success && response.data) {
        return response.data;
      }
    }
    
    // Fallback to mock data
    return generateMockPlatformAnalytics(platform);
  } catch (error) {
    console.warn('Failed to fetch platform analytics from API, using mock data:', error);
    return generateMockPlatformAnalytics(platform);
  }
};

interface EngagementParams {
  startDate?: string;
  endDate?: string;
  platform?: string;
  postId?: string;
}

export const getEngagementMetrics = async (params?: EngagementParams): Promise<EngagementMetrics> => {
  try {
    if (config.isProduction || isFeatureEnabled('analytics')) {
      const response = await analyticsApi.getEngagementMetrics(params);
      if (response.success && response.data) {
        return response.data;
      }
    }
    
    // Fallback to mock data
    return generateMockEngagementMetrics();
  } catch (error) {
    console.warn('Failed to fetch engagement metrics from API, using mock data:', error);
    return generateMockEngagementMetrics();
  }
};

// Utility functions
export const calculateEngagementRate = (metrics: PostAnalytics['metrics']): number => {
  const totalEngagement = metrics.likes + metrics.comments + metrics.shares;
  return Math.round((totalEngagement / metrics.reach) * 10000) / 100;
};

export const getPerformanceColor = (performance: PostAnalytics['performance']): string => {
  switch (performance) {
    case 'excellent': return 'text-green-600 bg-green-100';
    case 'good': return 'text-blue-600 bg-blue-100';
    case 'average': return 'text-yellow-600 bg-yellow-100';
    case 'poor': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

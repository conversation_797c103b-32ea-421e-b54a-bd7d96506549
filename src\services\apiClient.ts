import { config, getApiUrl } from '@/config/environment';

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API Error class
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// HTTP Client class
class HttpClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      ...this.defaultHeaders,
      ...options.headers,
    };

    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(
          data.message || data.error || 'Request failed',
          response.status,
          data.code
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Check if it's a network error (backend not running)
      if (error instanceof TypeError && error.message.includes('fetch')) {
        console.warn(`API endpoint ${url} not available, using mock response`);
        return this.getMockResponse<T>(endpoint, options.method || 'GET');
      }

      // Network or parsing error
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error occurred'
      );
    }
  }

  private getMockResponse<T>(endpoint: string, method: string): ApiResponse<T> {
    // Mock responses for development when backend is not available
    const mockResponses: Record<string, ApiResponse<unknown>> = {
      '/auth/login': {
        success: true,
        data: {
          token: 'mock-jwt-token',
          user: {
            id: '1',
            email: '<EMAIL>',
            name: 'Demo User'
          }
        }
      },
      '/auth/profile': {
        success: true,
        data: {
          id: '1',
          email: '<EMAIL>',
          name: 'Demo User',
          avatar: null
        }
      },
      '/analytics/dashboard': {
        success: true,
        data: {
          totalPosts: 42,
          totalEngagement: 1250,
          totalReach: 8500,
          growthRate: 12.5
        }
      }
    };

    const mockResponse = mockResponses[endpoint] || {
      success: true,
      data: null,
      message: 'Mock response - backend not available'
    };

    return mockResponse;
  }

  async get<T>(endpoint: string, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    const url = params ? `${endpoint}?${new URLSearchParams(params)}` : endpoint;
    return this.request<T>(url, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  async patch<T>(endpoint: string, data?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// Create API client instance
export const apiClient = new HttpClient(config.apiBaseUrl);

// Specific API endpoints
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    apiClient.post('/auth/login', credentials),
  
  register: (userData: { email: string; password: string; name: string }) =>
    apiClient.post('/auth/register', userData),
  
  logout: () => apiClient.post('/auth/logout'),
  
  refreshToken: () => apiClient.post('/auth/refresh'),
  
  getProfile: () => apiClient.get('/auth/profile'),
};

export const socialAccountsApi = {
  getAccounts: () => apiClient.get('/social-accounts'),
  
  connectAccount: (platform: string, authData: Record<string, unknown>) =>
    apiClient.post(`/social-accounts/${platform}/connect`, authData),
  
  disconnectAccount: (platform: string) =>
    apiClient.delete(`/social-accounts/${platform}`),
  
  getAccountStats: (platform: string) =>
    apiClient.get(`/social-accounts/${platform}/stats`),
};

export const postsApi = {
  getPosts: (params?: { page?: number; limit?: number; platform?: string }) =>
    apiClient.get('/posts', params),
  
  createPost: (postData: Record<string, unknown>) => apiClient.post('/posts', postData),

  updatePost: (id: string, postData: Record<string, unknown>) =>
    apiClient.put(`/posts/${id}`, postData),

  deletePost: (id: string) => apiClient.delete(`/posts/${id}`),

  schedulePost: (postData: Record<string, unknown>) => apiClient.post('/posts/schedule', postData),
  
  publishPost: (id: string) => apiClient.post(`/posts/${id}/publish`),
};

export const analyticsApi = {
  getDashboardStats: () => apiClient.get('/analytics/dashboard'),
  
  getPostAnalytics: (id: string) => apiClient.get(`/analytics/posts/${id}`),
  
  getPlatformAnalytics: (platform: string, dateRange?: string) =>
    apiClient.get(`/analytics/platforms/${platform}`, { dateRange }),
  
  getEngagementMetrics: (params?: Record<string, string | number | boolean>) =>
    apiClient.get('/analytics/engagement', params),
};

export const contentApi = {
  generateContent: (prompt: string, platform: string) =>
    apiClient.post('/content/generate', { prompt, platform }),
  
  getContentTemplates: () => apiClient.get('/content/templates'),
  
  saveTemplate: (template: Record<string, unknown>) => apiClient.post('/content/templates', template),
};

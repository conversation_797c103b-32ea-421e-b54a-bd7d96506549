import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useAppStore } from '@/store/appStore';

interface OAuthCallbackProps {
  platform: string;
}

const OAuthCallback = ({ platform }: OAuthCallbackProps) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { workspace } = useAppStore();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const processOAuthCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');
      const errorDescription = searchParams.get('error_description');

      if (error) {
        setStatus('error');
        setMessage(errorDescription || error);
        toast({
          title: "OAuth Error",
          description: `${platform} authorization failed: ${errorDescription || error}`,
          variant: "destructive",
        });

        // Redirect to OAuth test page after 3 seconds
        setTimeout(() => {
          navigate('/oauth-test');
        }, 3000);
        return;
      }

      if (!code) {
        setStatus('error');
        setMessage('No authorization code received');
        toast({
          title: "OAuth Error",
          description: "No authorization code received from " + platform,
          variant: "destructive",
        });

        setTimeout(() => {
          navigate('/oauth-test');
        }, 3000);
        return;
      }

      try {
        setStatus('processing');
        setMessage('Processing authorization code...');

        // Store the authorization code for immediate use
        sessionStorage.setItem(`OAUTH_CODE_${platform.toUpperCase()}`, code);
        if (state) {
          sessionStorage.setItem(`OAUTH_STATE_${platform.toUpperCase()}`, state);
        }

        // For LinkedIn, we can immediately try to exchange for access token
        if (platform.toLowerCase() === 'linkedin') {
          try {
            setMessage('Exchanging authorization code for access token...');
            const { linkedinService } = await import('@/services/linkedinService');
            const redirectUri = `${window.location.origin}/auth/linkedin/callback`;
            const tokenResponse = await linkedinService.exchangeCodeForToken(code, redirectUri);

            // Store the access token
            sessionStorage.setItem('LINKEDIN_ACCESS_TOKEN', tokenResponse.access_token);
            setMessage('Successfully obtained access token!');
          } catch (error) {
            console.error('Token exchange failed:', error);
            setMessage('Authorization code stored (token exchange can be done later)');
          }
        }

        // Small delay for user feedback
        await new Promise(resolve => setTimeout(resolve, 1000));

        setStatus('success');
        setMessage('Successfully connected to ' + platform);

        toast({
          title: "OAuth Success",
          description: `Successfully connected to ${platform}! You can now use this platform for posting.`,
        });

        // Redirect to OAuth test page to show success
        setTimeout(() => {
          navigate('/oauth-test');
        }, 2000);

      } catch (error) {
        console.error('OAuth processing error:', error);
        setStatus('error');
        setMessage('Failed to process authorization');

        toast({
          title: "Processing Error",
          description: "Failed to process " + platform + " authorization",
          variant: "destructive",
        });

        setTimeout(() => {
          navigate('/oauth-test');
        }, 3000);
      }
    };

    processOAuthCallback();
  }, [searchParams, platform, navigate, toast]);

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>;
      case 'success':
        return <div className="rounded-full h-12 w-12 bg-green-100 flex items-center justify-center mx-auto mb-4">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>;
      case 'error':
        return <div className="rounded-full h-12 w-12 bg-red-100 flex items-center justify-center mx-auto mb-4">
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </div>;
      default:
        return null;
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case 'processing':
        return `Connecting to ${platform}...`;
      case 'success':
        return `Successfully Connected!`;
      case 'error':
        return `Connection Failed`;
      default:
        return `Connecting to ${platform}...`;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return message || 'Please wait while we complete the authentication process.';
      case 'success':
        return message || `You have successfully connected your ${platform} account. Redirecting...`;
      case 'error':
        return message || 'There was an error connecting your account. Redirecting...';
      default:
        return 'Please wait while we complete the authentication process.';
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        {getStatusIcon()}
        <h2 className={`text-xl font-semibold mb-2 ${
          status === 'success' ? 'text-green-900' :
          status === 'error' ? 'text-red-900' :
          'text-gray-900'
        }`}>
          {getStatusTitle()}
        </h2>
        <p className={`${
          status === 'success' ? 'text-green-600' :
          status === 'error' ? 'text-red-600' :
          'text-gray-600'
        }`}>
          {getStatusMessage()}
        </p>

        {status !== 'processing' && (
          <div className="mt-6">
            <button
              onClick={() => navigate('/oauth-test')}
              className={`px-4 py-2 rounded-md text-white font-medium ${
                status === 'success' ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              Return to OAuth Setup
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OAuthCallback;

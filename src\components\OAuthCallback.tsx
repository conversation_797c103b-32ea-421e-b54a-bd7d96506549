import { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

interface OAuthCallbackProps {
  platform: string;
}

const OAuthCallback = ({ platform }: OAuthCallbackProps) => {
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      // Send error to parent window
      window.opener?.postMessage({
        type: 'OAUTH_ERROR',
        error: errorDescription || error,
        platform
      }, window.location.origin);
    } else if (code && state) {
      // Send success to parent window
      window.opener?.postMessage({
        type: 'OAUTH_SUCCESS',
        code,
        state,
        platform
      }, window.location.origin);
    } else {
      // Send generic error
      window.opener?.postMessage({
        type: 'OAUTH_ERROR',
        error: 'Invalid callback parameters',
        platform
      }, window.location.origin);
    }

    // Close the popup window
    window.close();
  }, [searchParams, platform]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Connecting to {platform}...
        </h2>
        <p className="text-gray-600">
          Please wait while we complete the authentication process.
        </p>
      </div>
    </div>
  );
};

export default OAuthCallback;

// Test utility for social accounts functionality
import { SocialAccountService } from '@/services/supabaseService';
import type { SocialAccount } from '@/store/appStore';

export interface TestSocialAccount {
  platform: string;
  username: string;
  display_name: string;
  followers: number;
  profile_url: string;
  avatar_url: string;
}

// Test data for different platforms
export const testAccounts: TestSocialAccount[] = [
  {
    platform: 'Instagram',
    username: '@pulsebuzz_demo',
    display_name: 'PulseBuzz Demo',
    followers: 1250,
    profile_url: 'https://instagram.com/pulsebuzz_demo',
    avatar_url: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=64&h=64&fit=crop&crop=face'
  },
  {
    platform: 'X (Twitter)',
    username: '@pulsebuzz_ai',
    display_name: 'PulseBuzz AI',
    followers: 890,
    profile_url: 'https://twitter.com/pulsebuzz_ai',
    avatar_url: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=64&h=64&fit=crop&crop=face'
  },
  {
    platform: 'LinkedIn',
    username: 'pulsebuzz-ai',
    display_name: 'PulseBuzz AI',
    followers: 567,
    profile_url: 'https://linkedin.com/company/pulsebuzz-ai',
    avatar_url: 'https://images.unsplash.com/photo-*************-2616b612b786?w=64&h=64&fit=crop&crop=face'
  },
  {
    platform: 'Facebook',
    username: 'PulseBuzzAI',
    display_name: 'PulseBuzz AI',
    followers: 2340,
    profile_url: 'https://facebook.com/PulseBuzzAI',
    avatar_url: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=64&h=64&fit=crop&crop=face'
  }
];

// Test function to create a demo social account
export const createTestSocialAccount = async (platform: string): Promise<SocialAccount> => {
  const testAccount = testAccounts.find(acc => acc.platform === platform);
  
  if (!testAccount) {
    throw new Error(`No test data available for platform: ${platform}`);
  }

  try {
    // Create account using the service
    const account = await SocialAccountService.connectAccount({
      platform: testAccount.platform,
      username: testAccount.username,
      display_name: testAccount.display_name,
      followers: testAccount.followers,
      profile_url: testAccount.profile_url,
      avatar_url: testAccount.avatar_url,
      is_connected: true,
      status: 'active',
      last_sync: new Date().toISOString(),
      access_token: `demo_token_${Date.now()}`,
      refresh_token: null,
      token_expires_at: null
    });

    console.log(`✅ Successfully created test account for ${platform}:`, account);
    return account;
  } catch (error) {
    console.error(`❌ Failed to create test account for ${platform}:`, error);
    throw error;
  }
};

// Test function to verify account connection
export const testAccountConnection = async (platform: string): Promise<boolean> => {
  try {
    console.log(`🧪 Testing ${platform} account connection...`);
    
    // Create test account
    const account = await createTestSocialAccount(platform);
    
    // Verify account was created
    const accounts = await SocialAccountService.getUserSocialAccounts();
    const createdAccount = accounts.find(acc => acc.platform === platform);
    
    if (createdAccount && createdAccount.is_connected) {
      console.log(`✅ ${platform} account connection test PASSED`);
      return true;
    } else {
      console.log(`❌ ${platform} account connection test FAILED`);
      return false;
    }
  } catch (error) {
    console.error(`❌ ${platform} account connection test ERROR:`, error);
    return false;
  }
};

// Test all platforms
export const testAllPlatforms = async (): Promise<{ [key: string]: boolean }> => {
  const results: { [key: string]: boolean } = {};
  
  for (const testAccount of testAccounts) {
    results[testAccount.platform] = await testAccountConnection(testAccount.platform);
  }
  
  return results;
};

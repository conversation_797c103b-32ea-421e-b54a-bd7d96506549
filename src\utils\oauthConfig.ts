// OAuth configuration utilities

export interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  scope: string;
  authUrl: string;
  tokenUrl: string;
}

export type SupportedPlatform = 'instagram' | 'facebook' | 'linkedin' | 'x';

// Get safe origin for OAuth redirects
const getOrigin = (): string => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return 'http://localhost:8080'; // fallback for SSR
};

// Check if OAuth credentials are configured for a platform
export const hasValidOAuthCredentials = (platform: SupportedPlatform): boolean => {
  const envVars = {
    instagram: {
      clientId: import.meta.env?.VITE_INSTAGRAM_CLIENT_ID,
      clientSecret: import.meta.env?.VITE_INSTAGRAM_CLIENT_SECRET,
    },
    facebook: {
      clientId: import.meta.env?.VITE_FACEBOOK_CLIENT_ID,
      clientSecret: import.meta.env?.VITE_FACEBOOK_CLIENT_SECRET,
    },
    linkedin: {
      clientId: import.meta.env?.VITE_LINKEDIN_CLIENT_ID,
      clientSecret: import.meta.env?.VITE_LINKEDIN_CLIENT_SECRET,
    },
    x: {
      clientId: import.meta.env?.VITE_X_CLIENT_ID,
      clientSecret: import.meta.env?.VITE_X_CLIENT_SECRET,
    },
  };

  const platformVars = envVars[platform];
  return !!(platformVars?.clientId && platformVars?.clientSecret);
};

// Get OAuth configuration for a platform
export const getOAuthConfig = (platform: SupportedPlatform): OAuthConfig | null => {
  const origin = getOrigin();

  const configs: Record<SupportedPlatform, OAuthConfig> = {
    instagram: {
      clientId: import.meta.env?.VITE_INSTAGRAM_CLIENT_ID || '',
      clientSecret: import.meta.env?.VITE_INSTAGRAM_CLIENT_SECRET || '',
      redirectUri: `${origin}/auth/instagram/callback`,
      scope: 'user_profile,user_media',
      authUrl: 'https://api.instagram.com/oauth/authorize',
      tokenUrl: 'https://api.instagram.com/oauth/access_token',
    },
    facebook: {
      clientId: import.meta.env?.VITE_FACEBOOK_CLIENT_ID || '',
      clientSecret: import.meta.env?.VITE_FACEBOOK_CLIENT_SECRET || '',
      redirectUri: `${origin}/auth/facebook/callback`,
      scope: 'pages_manage_posts,pages_read_engagement,pages_show_list',
      authUrl: 'https://www.facebook.com/v18.0/dialog/oauth',
      tokenUrl: 'https://graph.facebook.com/v18.0/oauth/access_token',
    },
    linkedin: {
      clientId: import.meta.env?.VITE_LINKEDIN_CLIENT_ID || '',
      clientSecret: import.meta.env?.VITE_LINKEDIN_CLIENT_SECRET || '',
      redirectUri: `${origin}/auth/linkedin/callback`,
      scope: 'openid,profile,email,w_member_social',
      authUrl: 'https://www.linkedin.com/oauth/v2/authorization',
      tokenUrl: 'https://www.linkedin.com/oauth/v2/accessToken',
    },
    x: {
      clientId: import.meta.env?.VITE_X_CLIENT_ID || '',
      clientSecret: import.meta.env?.VITE_X_CLIENT_SECRET || '',
      redirectUri: `${origin}/auth/x/callback`,
      scope: 'tweet.read,tweet.write,users.read',
      authUrl: 'https://twitter.com/i/oauth2/authorize',
      tokenUrl: 'https://api.twitter.com/2/oauth2/token',
    },
  };

  const config = configs[platform];
  if (!config.clientId || !config.clientSecret) {
    return null;
  }

  return config;
};

// Generate OAuth URL for a platform
export const generateOAuthUrl = (platform: SupportedPlatform): string | null => {
  const config = getOAuthConfig(platform);
  if (!config) {
    return null;
  }

  const state = Math.random().toString(36).substring(2, 15);
  
  // Store state for verification (in a real app, you'd use a more secure method)
  if (typeof window !== 'undefined') {
    localStorage.setItem(`oauth_state_${platform}`, state);
  }

  const params = new URLSearchParams({
    client_id: config.clientId,
    redirect_uri: config.redirectUri,
    scope: config.scope,
    response_type: 'code',
    state: state,
  });

  return `${config.authUrl}?${params.toString()}`;
};

// Validate OAuth state parameter
export const validateOAuthState = (platform: SupportedPlatform, state: string): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  const storedState = localStorage.getItem(`oauth_state_${platform}`);
  if (storedState === state) {
    localStorage.removeItem(`oauth_state_${platform}`);
    return true;
  }

  return false;
};

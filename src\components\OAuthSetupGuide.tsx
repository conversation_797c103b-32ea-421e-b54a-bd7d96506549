import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, ExternalLink, Copy, CheckCircle, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const OAuthSetupGuide = () => {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({ ...prev, [section]: !prev[section] }));
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${label} copied to clipboard`,
    });
  };

  const getOrigin = () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return 'http://localhost:8080'; // fallback for SSR
  };

  const platforms = [
    {
      name: "Instagram",
      envVar: "VITE_INSTAGRAM_CLIENT_ID",
      setupUrl: "https://developers.facebook.com/apps/",
      redirectUri: `${getOrigin()}/auth/instagram/callback`,
      scopes: "user_profile,user_media",
      setupTime: "~10 min",
      difficulty: "Easy",
      steps: [
        "Go to Facebook Developers Console",
        "Create a new app or select existing app",
        "Add Instagram Basic Display product",
        "Go to Instagram Basic Display > Basic Display",
        "Add OAuth Redirect URI",
        "Copy the Instagram App ID"
      ]
    },
    {
      name: "Facebook",
      envVar: "VITE_FACEBOOK_CLIENT_ID",
      setupUrl: "https://developers.facebook.com/apps/",
      redirectUri: `${getOrigin()}/auth/facebook/callback`,
      scopes: "pages_manage_posts,pages_read_engagement",
      setupTime: "~8 min",
      difficulty: "Easy",
      steps: [
        "Go to Facebook Developers Console",
        "Create a new app or select existing app",
        "Add Facebook Login product",
        "Go to Facebook Login > Settings",
        "Add OAuth Redirect URI",
        "Copy the App ID"
      ]
    },
    {
      name: "LinkedIn",
      envVar: "VITE_LINKEDIN_CLIENT_ID",
      setupUrl: "https://www.linkedin.com/developers/apps",
      redirectUri: `${getOrigin()}/auth/linkedin/callback`,
      scopes: "openid,profile,email,w_member_social",
      setupTime: "~12 min",
      difficulty: "Medium",
      steps: [
        "Go to LinkedIn Developers",
        "Create a new app",
        "Add Sign In with LinkedIn product",
        "Go to Auth tab",
        "Add OAuth 2.0 redirect URL",
        "Copy the Client ID"
      ]
    },
    {
      name: "Twitter",
      envVar: "VITE_TWITTER_CLIENT_ID",
      setupUrl: "https://developer.twitter.com/en/portal/dashboard",
      redirectUri: `${getOrigin()}/auth/twitter/callback`,
      scopes: "tweet.read,tweet.write,users.read",
      setupTime: "~15 min",
      difficulty: "Medium",
      steps: [
        "Go to Twitter Developer Portal",
        "Create a new project and app",
        "Go to App Settings > User authentication settings",
        "Enable OAuth 2.0",
        "Add Callback URI",
        "Copy the Client ID"
      ]
    }
  ];

  const hasCredentials = (envVar: string) => {
    const value = import.meta.env?.[envVar];
    return value && !value.startsWith('your-');
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>OAuth Setup Guide</span>
          <Badge variant="outline">Development</Badge>
        </CardTitle>
        <CardDescription>
          Configure OAuth credentials to connect real social media accounts to PulseBuzz.AI.
          Without these, the app will run in demo mode with mock data.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {platforms.map((platform) => (
          <Collapsible
            key={platform.name}
            open={openSections[platform.name]}
            onOpenChange={() => toggleSection(platform.name)}
          >
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="w-full justify-between p-4 h-auto"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-left">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">{platform.name}</span>
                      {hasCredentials(platform.envVar) ? (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Configured
                        </Badge>
                      ) : (
                        <Badge variant="outline">Not Configured</Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
                      <Clock className="h-3 w-3" />
                      <span>Setup: {platform.setupTime || 'N/A'}</span>
                      <span>•</span>
                      <span>{platform.difficulty || 'Medium'}</span>
                    </div>
                  </div>
                </div>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="px-4 pb-4">
              <div className="space-y-4 border-l-2 border-gray-200 pl-4">
                <div>
                  <h4 className="font-medium mb-2">Setup Steps:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
                    {platform.steps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Developer Console
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(platform.setupUrl, '_blank')}
                      className="w-full justify-start"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open Console
                    </Button>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Environment Variable
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(platform.envVar, "Environment variable")}
                      className="w-full justify-start"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      {platform.envVar}
                    </Button>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Redirect URI (copy this to your OAuth app)
                  </label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(platform.redirectUri, "Redirect URI")}
                    className="w-full justify-start text-xs"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {platform.redirectUri}
                  </Button>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Required Scopes
                  </label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(platform.scopes, "Scopes")}
                    className="w-full justify-start text-xs"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {platform.scopes}
                  </Button>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        ))}
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Quick Start</h4>
          <p className="text-sm text-blue-700 mb-3">
            To test with real OAuth, create a .env file in your project root with your credentials:
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(`VITE_INSTAGRAM_CLIENT_ID=your-actual-client-id
VITE_FACEBOOK_CLIENT_ID=your-actual-client-id
VITE_LINKEDIN_CLIENT_ID=your-actual-client-id
VITE_TWITTER_CLIENT_ID=your-actual-client-id`, ".env template")}
            className="text-xs"
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy .env Template
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default OAuthSetupGuide;

import React from 'react';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';
import OAuthSetupWizard from '@/components/OAuthSetupWizard';

const OAuthSetup: React.FC = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          <main className="flex-1 p-6">
            <OAuthSetupWizard />
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default OAuthSetup;

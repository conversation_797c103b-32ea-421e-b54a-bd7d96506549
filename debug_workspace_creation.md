# 🔍 Debug Workspace Creation Issue

## Step 1: Check Database Setup

1. **Go to Supabase Dashboard** → Your Project → SQL Editor
2. **Run this query** to check if tables exist:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('workspaces', 'workspace_members', 'profiles');
```

**Expected Result**: Should return 3 rows with table names
**If empty**: You need to run the database migrations

## Step 2: Apply Database Migrations

**Copy and run these SQL files in Supabase SQL Editor:**

### Migration 1: Core Schema
```sql
-- Run the content from: supabase/migrations/20250618000000_create_production_schema.sql
```

### Migration 2: Storage Bucket
```sql
-- Run the content from: supabase/migrations/20250618000001_create_storage_bucket.sql
```

## Step 3: Check Browser Console

1. **Open Developer Tools** (F12)
2. **Go to Console tab**
3. **Try creating workspace again**
4. **Look for error messages**

Common errors:
- `relation "workspaces" does not exist` → Run migrations
- `JWT expired` → Re-authenticate
- `RLS policy violation` → Check user permissions

## Step 4: Test Authentication

**Run this in browser console:**
```javascript
// Check if user is authenticated
console.log('Auth status:', await window.supabase?.auth.getUser());
```

## Step 5: Manual Workspace Check

**Run this query in Supabase SQL Editor:**
```sql
-- Replace 'your-user-id' with actual user ID from auth
SELECT * FROM workspaces WHERE owner_id = 'your-user-id';
SELECT * FROM workspace_members WHERE user_id = 'your-user-id';
```

## Expected Behavior

### New User Flow:
1. **Sign in with Google** → Redirected to app
2. **No workspace exists** → Shows workspace creation form
3. **Fill form + click "Create Workspace"** → Creates workspace in database
4. **Success** → Redirected to main dashboard
5. **Future logins** → Goes directly to dashboard (no form)

### Existing User Flow:
1. **Sign in with Google** → Redirected to app
2. **Workspace exists** → Goes directly to dashboard
3. **Never sees creation form again**

## Troubleshooting Checklist

- [ ] Database migrations applied
- [ ] Environment variables set correctly
- [ ] User is authenticated (check browser console)
- [ ] No console errors during form submission
- [ ] Supabase project is active and accessible
- [ ] RLS policies are properly configured

## Quick Fix Commands

If you need to reset and try again:

```sql
-- Delete test workspace (if any)
DELETE FROM workspace_members WHERE workspace_id IN (
  SELECT id FROM workspaces WHERE name = 'My Company'
);
DELETE FROM workspaces WHERE name = 'My Company';
```

Then try creating the workspace again.

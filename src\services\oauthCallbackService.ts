import { config } from '@/config/oauth';

export interface OAuthCallbackResult {
  success: boolean;
  platform: string;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  userInfo?: {
    id: string;
    username: string;
    displayName: string;
    profileUrl?: string;
    avatarUrl?: string;
    followers?: number;
  };
  error?: string;
}

// Handle OAuth callback and exchange code for access token
export const handleOAuthCallback = async (
  platform: keyof typeof config.oauth,
  code: string,
  state?: string
): Promise<OAuthCallbackResult> => {
  try {
    const platformConfig = config.oauth[platform];
    
    if (!platformConfig) {
      throw new Error(`OAuth configuration not found for platform: ${platform}`);
    }

    // Exchange authorization code for access token
    const tokenResponse = await exchangeCodeForToken(platform, code);
    
    if (!tokenResponse.success || !tokenResponse.accessToken) {
      throw new Error(tokenResponse.error || 'Failed to exchange code for token');
    }

    // Fetch user profile information
    const userInfo = await fetchUserProfile(platform, tokenResponse.accessToken);

    return {
      success: true,
      platform,
      accessToken: tokenResponse.accessToken,
      refreshToken: tokenResponse.refreshToken,
      expiresIn: tokenResponse.expiresIn,
      userInfo
    };
  } catch (error) {
    console.error(`OAuth callback error for ${platform}:`, error);
    return {
      success: false,
      platform,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

// Exchange authorization code for access token
const exchangeCodeForToken = async (
  platform: keyof typeof config.oauth,
  code: string
): Promise<{
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  error?: string;
}> => {
  const platformConfig = config.oauth[platform];
  
  try {
    const tokenData = new URLSearchParams({
      client_id: platformConfig.clientId,
      client_secret: platformConfig.clientSecret,
      code: code,
      grant_type: 'authorization_code',
      redirect_uri: platformConfig.redirectUri
    });

    const response = await fetch(platformConfig.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: tokenData
    });

    if (!response.ok) {
      throw new Error(`Token exchange failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error_description || data.error);
    }

    return {
      success: true,
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresIn: data.expires_in
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token exchange failed'
    };
  }
};

// Fetch user profile information from each platform
const fetchUserProfile = async (
  platform: keyof typeof config.oauth,
  accessToken: string
): Promise<OAuthCallbackResult['userInfo']> => {
  try {
    switch (platform) {
      case 'instagram':
        return await fetchInstagramProfile(accessToken);
      case 'facebook':
        return await fetchFacebookProfile(accessToken);
      case 'linkedin':
        return await fetchLinkedInProfile(accessToken);
      case 'twitter':
        return await fetchTwitterProfile(accessToken);
      default:
        throw new Error(`Profile fetching not implemented for platform: ${platform}`);
    }
  } catch (error) {
    console.error(`Failed to fetch profile for ${platform}:`, error);
    // Return basic info if profile fetch fails
    return {
      id: 'unknown',
      username: `${platform}_user`,
      displayName: `${platform} User`
    };
  }
};

// Platform-specific profile fetchers
const fetchInstagramProfile = async (accessToken: string): Promise<OAuthCallbackResult['userInfo']> => {
  const response = await fetch(
    `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${accessToken}`
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch Instagram profile');
  }
  
  const data = await response.json();
  
  return {
    id: data.id,
    username: `@${data.username}`,
    displayName: data.username,
    followers: data.media_count || 0,
    profileUrl: `https://instagram.com/${data.username}`
  };
};

const fetchFacebookProfile = async (accessToken: string): Promise<OAuthCallbackResult['userInfo']> => {
  const response = await fetch(
    `https://graph.facebook.com/me?fields=id,name,picture&access_token=${accessToken}`
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch Facebook profile');
  }
  
  const data = await response.json();
  
  return {
    id: data.id,
    username: data.name,
    displayName: data.name,
    avatarUrl: data.picture?.data?.url,
    profileUrl: `https://facebook.com/${data.id}`
  };
};

const fetchLinkedInProfile = async (accessToken: string): Promise<OAuthCallbackResult['userInfo']> => {
  const response = await fetch(
    'https://api.linkedin.com/v2/people/~:(id,firstName,lastName,profilePicture(displayImage~:playableStreams))',
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    }
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch LinkedIn profile');
  }
  
  const data = await response.json();
  
  const firstName = data.firstName?.localized?.en_US || '';
  const lastName = data.lastName?.localized?.en_US || '';
  const displayName = `${firstName} ${lastName}`.trim();
  
  return {
    id: data.id,
    username: displayName.toLowerCase().replace(/\s+/g, '-'),
    displayName,
    avatarUrl: data.profilePicture?.displayImage?.elements?.[0]?.identifiers?.[0]?.identifier,
    profileUrl: `https://linkedin.com/in/${data.id}`
  };
};

const fetchTwitterProfile = async (accessToken: string): Promise<OAuthCallbackResult['userInfo']> => {
  const response = await fetch(
    'https://api.twitter.com/2/users/me?user.fields=id,username,name,profile_image_url,public_metrics',
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    }
  );
  
  if (!response.ok) {
    throw new Error('Failed to fetch Twitter profile');
  }
  
  const data = await response.json();
  const user = data.data;
  
  return {
    id: user.id,
    username: `@${user.username}`,
    displayName: user.name,
    avatarUrl: user.profile_image_url,
    followers: user.public_metrics?.followers_count || 0,
    profileUrl: `https://twitter.com/${user.username}`
  };
};

// Handle OAuth errors
export const handleOAuthError = (error: string, description?: string): OAuthCallbackResult => {
  console.error('OAuth Error:', error, description);
  
  const errorMessages: Record<string, string> = {
    'access_denied': 'User denied access to the application',
    'invalid_request': 'Invalid OAuth request parameters',
    'unauthorized_client': 'Client is not authorized for this request',
    'unsupported_response_type': 'OAuth response type not supported',
    'invalid_scope': 'Requested scope is invalid or unknown',
    'server_error': 'OAuth server encountered an error',
    'temporarily_unavailable': 'OAuth service is temporarily unavailable'
  };

  return {
    success: false,
    platform: 'unknown',
    error: errorMessages[error] || description || error || 'OAuth authentication failed'
  };
};

// Validate OAuth state parameter (CSRF protection)
export const validateOAuthState = (receivedState: string, expectedState?: string): boolean => {
  if (!expectedState) {
    // If no expected state, we can't validate
    console.warn('OAuth state validation skipped - no expected state provided');
    return true;
  }
  
  return receivedState === expectedState;
};

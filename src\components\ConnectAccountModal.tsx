import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { ExternalLink, X } from "lucide-react";
import { getPlatformIcon } from "@/utils/socialIcons";

interface ConnectAccountModalProps {
  platform: string;
  isOpen: boolean;
  onClose: () => void;
  onConnect: (platform: string, credentials: { username: string; accessToken: string }) => void;
  authUrl?: string;
}

const ConnectAccountModal = ({ platform, isOpen, onClose, onConnect, authUrl }: ConnectAccountModalProps) => {
  const [username, setUsername] = useState("");
  const [accessToken, setAccessToken] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [authMethod, setAuthMethod] = useState<'oauth' | 'manual'>('oauth');
  const { toast } = useToast();



  const handleOAuthConnect = () => {
    if (authUrl) {
      // Open OAuth URL in new window
      const authWindow = window.open(authUrl, 'oauth', 'width=600,height=600');
      
      // Listen for OAuth callback
      const checkClosed = setInterval(() => {
        if (authWindow?.closed) {
          clearInterval(checkClosed);
          // Simulate successful OAuth (in real app, you'd get the token from callback)
          const mockCredentials = {
            username: `@${platform.toLowerCase()}user`,
            accessToken: `mock_token_${Date.now()}`
          };
          
          onConnect(platform, mockCredentials);
          toast({
            title: "Account Connected!",
            description: `Your ${platform} account has been successfully connected via OAuth.`,
          });
          onClose();
        }
      }, 1000);
    }
  };

  const handleManualConnect = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username || !accessToken) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    setIsConnecting(true);

    // Simulate API validation
    setTimeout(() => {
      onConnect(platform, { username, accessToken });
      toast({
        title: "Account Connected!",
        description: `Your ${platform} account has been successfully connected.`,
      });
      setIsConnecting(false);
      onClose();
      setUsername("");
      setAccessToken("");
    }, 1500);
  };

  const getPlatformInstructions = (platform: string) => {
    const instructions = {
      "Instagram": "Connect through Facebook's Graph API. You'll need to create a Facebook app and get Instagram Basic Display permissions.",
      "Facebook": "Create a Facebook app and get a Page Access Token with pages_manage_posts permission.",
      "LinkedIn": "Create a LinkedIn app and get an access token with w_member_social scope.",
      "X (Twitter)": "Create a Twitter app and get API keys with tweet.write permissions."
    };
    
    return instructions[platform as keyof typeof instructions] || "Follow the platform's API documentation to get your access token.";
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getPlatformIcon(platform, 'lg')}
              <CardTitle>Connect {platform}</CardTitle>
            </div>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
            <p className="font-medium mb-1">Platform Requirements:</p>
            <p>{getPlatformInstructions(platform)}</p>
          </div>

          <div className="flex space-x-2">
            <Button
              variant={authMethod === 'oauth' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setAuthMethod('oauth')}
              className="flex-1"
            >
              OAuth (Recommended)
            </Button>
            <Button
              variant={authMethod === 'manual' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setAuthMethod('manual')}
              className="flex-1"
            >
              Manual Token
            </Button>
          </div>

          {authMethod === 'oauth' ? (
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Connect securely using {platform}'s official OAuth flow.
              </p>
              <Button
                onClick={handleOAuthConnect}
                className="w-full flex items-center space-x-2"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Connect with {platform}</span>
              </Button>
            </div>
          ) : (
            <form onSubmit={handleManualConnect} className="space-y-4">
              <div>
                <Label htmlFor="username">Username/Handle</Label>
                <Input
                  id="username"
                  type="text"
                  placeholder={`@your${platform.toLowerCase()}handle`}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="accessToken">Access Token</Label>
                <Input
                  id="accessToken"
                  type="password"
                  placeholder="Enter your access token"
                  value={accessToken}
                  onChange={(e) => setAccessToken(e.target.value)}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Get this from your {platform} developer settings
                </p>
              </div>
              <div className="flex space-x-2 pt-4">
                <Button
                  type="submit"
                  disabled={isConnecting}
                  className="flex-1"
                >
                  {isConnecting ? "Connecting..." : "Connect Account"}
                </Button>
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ConnectAccountModal;

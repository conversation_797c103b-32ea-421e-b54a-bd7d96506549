
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import PageHeader from '@/components/PageHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TopHeader } from '@/components/TopHeader';
import ScheduledPostsList from '@/components/ScheduledPostsList';
import { useAppStore } from '@/store/appStore';
import {
  Plus,
  TrendingUp,
  Users,
  MessageSquare,
  Calendar,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

const Index: React.FC = () => {
  const { posts, socialAccounts, inboxItems, workspace } = useAppStore();
  const navigate = useNavigate();

  const handleCreatePost = () => {
    console.log('Creating new post...');
    navigate('/create');
  };

  const handleSchedulePost = () => {
    console.log('Scheduling new post...');
    navigate('/scheduled');
  };

  // Calculate real stats from app data
  const totalPosts = posts.length;
  const scheduledPosts = posts.filter(post => post.status === 'scheduled').length;
  const publishedPosts = posts.filter(post => post.status === 'published').length;
  const unreadMessages = inboxItems.filter(item => !item.is_read).length;
  const connectedAccounts = socialAccounts.length;

  // Calculate engagement rate (simplified - would need real analytics data)
  const engagementRate = publishedPosts > 0 ?
    (posts.reduce((sum, post) => sum + (post.engagement_metrics?.total_engagements || 0), 0) / publishedPosts).toFixed(1) :
    '0.0';

  const stats = [
    {
      title: "Total Posts",
      value: totalPosts.toString(),
      change: scheduledPosts > 0 ? `${scheduledPosts} scheduled` : "No scheduled posts",
      icon: BarChart3,
      color: "text-blue-600"
    },
    {
      title: "Engagement Rate",
      value: `${engagementRate}%`,
      change: publishedPosts > 0 ? "Based on published posts" : "No published posts yet",
      icon: TrendingUp,
      color: "text-green-600"
    },
    {
      title: "Connected Accounts",
      value: connectedAccounts.toString(),
      change: connectedAccounts > 0 ? "Accounts connected" : "Connect your accounts",
      icon: Users,
      color: "text-purple-600"
    },
    {
      title: "Messages",
      value: unreadMessages.toString(),
      change: unreadMessages > 0 ? `${unreadMessages} unread` : "All caught up",
      icon: MessageSquare,
      color: "text-orange-600"
    }
  ];

  // Use real posts from the app store, or show empty state
  const recentPosts = posts.length > 0 ? posts.slice(0, 3).map(post => ({
    id: post.id,
    content: post.content,
    platform: post.platform,
    scheduledFor: post.scheduled_for || post.created_at,
    status: post.status,
    engagement: post.engagement_metrics?.total_engagements || 0
  })) : [
    {
      id: 'empty-1',
      content: "Welcome to PulseBuzz.AI! Create your first post to get started.",
      platform: "Getting Started",
      scheduledFor: new Date().toISOString(),
      status: "draft",
      engagement: 0
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return CheckCircle;
      case 'scheduled':
        return Clock;
      case 'draft':
        return AlertCircle;
      default:
        return AlertCircle;
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Dashboard"
              description="Overview of your social media performance and activity"
              icon={<BarChart3 className="w-8 h-8" />}
              actions={
                <div className="flex items-center space-x-2">
                  <Button className="bg-white text-blue-600 hover:bg-white/90" onClick={handleSchedulePost}>
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Post
                  </Button>
                  <Button className="bg-white text-blue-600 hover:bg-white/90" onClick={handleCreatePost}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Post
                  </Button>
                </div>
              }
            />

            <div className="px-6 pb-6 space-y-6">

          {/* Stats Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {stat.title}
                    </CardTitle>
                    <IconComponent className={`h-4 w-4 ${stat.color}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stat.value}</div>
                    <p className="text-xs text-muted-foreground">
                      {stat.change}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Recent Activity */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Posts</CardTitle>
                <CardDescription>
                  Your latest social media activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentPosts.map((post) => {
                    const StatusIcon = getStatusIcon(post.status);
                    return (
                      <div key={post.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <StatusIcon className="h-8 w-8 text-muted-foreground" />
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {post.content}
                          </p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{post.platform}</Badge>
                            <Badge className={getStatusColor(post.status)}>
                              {post.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {post.status === 'published' 
                              ? `${post.engagement} engagements` 
                              : `Scheduled for ${new Date(post.scheduledFor).toLocaleDateString()}`
                            }
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common tasks and shortcuts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScheduledPostsList onCreatePost={handleCreatePost} />
              </CardContent>
            </Card>
          </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default Index;

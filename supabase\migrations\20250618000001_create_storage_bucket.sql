-- Create storage bucket for media files
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media',
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/webm', 'video/quicktime']
);

-- Create storage policies for media bucket
CREATE POLICY "Users can view media files" 
  ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'media');

CREATE POLICY "Authenticated users can upload media files" 
  ON storage.objects 
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'media' AND 
    auth.role() = 'authenticated' AND
    (storage.foldername(name))[1] IN (
      SELECT w.id::text 
      FROM public.workspaces w 
      WHERE w.owner_id = auth.uid() OR 
            w.id IN (
              SELECT wm.workspace_id 
              FROM public.workspace_members wm 
              WHERE wm.user_id = auth.uid()
            )
    )
  );

CREATE POLICY "Users can update their workspace media files" 
  ON storage.objects 
  FOR UPDATE 
  USING (
    bucket_id = 'media' AND 
    auth.role() = 'authenticated' AND
    (storage.foldername(name))[1] IN (
      SELECT w.id::text 
      FROM public.workspaces w 
      WHERE w.owner_id = auth.uid() OR 
            w.id IN (
              SELECT wm.workspace_id 
              FROM public.workspace_members wm 
              WHERE wm.user_id = auth.uid() AND 
              wm.role IN ('owner', 'admin', 'editor')
            )
    )
  );

CREATE POLICY "Users can delete their workspace media files" 
  ON storage.objects 
  FOR DELETE 
  USING (
    bucket_id = 'media' AND 
    auth.role() = 'authenticated' AND
    (storage.foldername(name))[1] IN (
      SELECT w.id::text 
      FROM public.workspaces w 
      WHERE w.owner_id = auth.uid() OR 
            w.id IN (
              SELECT wm.workspace_id 
              FROM public.workspace_members wm 
              WHERE wm.user_id = auth.uid() AND 
              wm.role IN ('owner', 'admin', 'editor')
            )
    )
  );

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useToast } from '@/hooks/use-toast';

interface SecurityContextType {
  isSecure: boolean;
  forceHTTPS: () => void;
  checkSecurityStatus: () => SecurityStatus;
}

interface SecurityStatus {
  isHTTPS: boolean;
  hasSecureHeaders: boolean;
  isLocalhost: boolean;
  securityScore: number;
  warnings: string[];
  recommendations: string[];
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

export const useSecurityContext = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurityContext must be used within a SecurityProvider');
  }
  return context;
};

interface SecurityProviderProps {
  children: React.ReactNode;
  enforceHTTPS?: boolean;
  showSecurityWarnings?: boolean;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({
  children,
  enforceHTTPS = true,
  showSecurityWarnings = true
}) => {
  const [isSecure, setIsSecure] = useState(false);
  const { toast } = useToast();

  const checkSecurityStatus = (): SecurityStatus => {
    const isHTTPS = window.location.protocol === 'https:';
    const isLocalhost = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname === '::1';
    
    const warnings: string[] = [];
    const recommendations: string[] = [];
    let securityScore = 0;

    // Check HTTPS
    if (isHTTPS) {
      securityScore += 40;
    } else if (!isLocalhost) {
      // Only warn about HTTP in production
      warnings.push('Connection is not encrypted (HTTP)');
      recommendations.push('Enable HTTPS for secure data transmission');
    }

    // Check if running on localhost (development)
    if (isLocalhost) {
      securityScore += 40; // Give full HTTPS score for localhost
      if (!isHTTPS) {
        // Don't show warnings for HTTP in development
        recommendations.push('Use "npm run dev:https" for HTTPS development');
      }
    } else if (!isHTTPS) {
      warnings.push('Production site should always use HTTPS');
      securityScore -= 20;
    }

    // Check for mixed content
    if (isHTTPS) {
      const hasInsecureContent = Array.from(document.querySelectorAll('img, script, link')).some(
        (element) => {
          const src = element.getAttribute('src') || element.getAttribute('href');
          return src && src.startsWith('http://') && !src.includes('localhost');
        }
      );

      if (hasInsecureContent) {
        warnings.push('Mixed content detected (HTTPS page loading HTTP resources)');
        recommendations.push('Ensure all resources use HTTPS or relative URLs');
        securityScore -= 15;
      } else {
        securityScore += 20;
      }
    }

    // Check for security headers (basic check)
    const hasSecureHeaders = document.querySelector('meta[http-equiv="Content-Security-Policy"]') !== null ||
                            document.querySelector('meta[http-equiv="X-Frame-Options"]') !== null;
    
    if (hasSecureHeaders) {
      securityScore += 20;
    } else {
      recommendations.push('Add security headers (CSP, X-Frame-Options, etc.)');
    }

    // Ensure score is between 0 and 100
    securityScore = Math.max(0, Math.min(100, securityScore));

    return {
      isHTTPS,
      hasSecureHeaders,
      isLocalhost,
      securityScore,
      warnings,
      recommendations
    };
  };

  const forceHTTPS = () => {
    if (window.location.protocol === 'http:' && !window.location.hostname.includes('localhost')) {
      const httpsUrl = window.location.href.replace('http://', 'https://');
      window.location.replace(httpsUrl);
    }
  };

  useEffect(() => {
    const status = checkSecurityStatus();
    setIsSecure(status.isHTTPS || status.isLocalhost);

    // Force HTTPS in production
    if (enforceHTTPS && !status.isLocalhost && !status.isHTTPS) {
      forceHTTPS();
      return;
    }

    // Show security warnings if enabled (only in production)
    if (showSecurityWarnings && status.warnings.length > 0 && !status.isLocalhost) {
      status.warnings.forEach((warning) => {
        toast({
          title: "Security Warning",
          description: warning,
          variant: "destructive",
        });
      });
    }

    // Show security recommendations for low scores (only in production)
    if (showSecurityWarnings && status.securityScore < 60 && status.recommendations.length > 0 && !status.isLocalhost) {
      toast({
        title: `Security Score: ${status.securityScore}/100`,
        description: status.recommendations[0],
        variant: "default",
      });
    }

    // Add security meta tags if not present (only in production)
    if (!status.isLocalhost) {
      if (!document.querySelector('meta[http-equiv="X-Content-Type-Options"]')) {
        const meta = document.createElement('meta');
        meta.setAttribute('http-equiv', 'X-Content-Type-Options');
        meta.setAttribute('content', 'nosniff');
        document.head.appendChild(meta);
      }

      if (!document.querySelector('meta[http-equiv="X-Frame-Options"]')) {
        const meta = document.createElement('meta');
        meta.setAttribute('http-equiv', 'X-Frame-Options');
        meta.setAttribute('content', 'SAMEORIGIN');
        document.head.appendChild(meta);
      }

      if (!document.querySelector('meta[http-equiv="Referrer-Policy"]')) {
        const meta = document.createElement('meta');
        meta.setAttribute('http-equiv', 'Referrer-Policy');
        meta.setAttribute('content', 'strict-origin-when-cross-origin');
        document.head.appendChild(meta);
      }

      // Add CSP meta tag for additional security (only in production)
      if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
        const meta = document.createElement('meta');
        meta.setAttribute('http-equiv', 'Content-Security-Policy');
        meta.setAttribute('content',
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; " +
          "style-src 'self' 'unsafe-inline' https:; " +
          "font-src 'self' https: data:; " +
          "img-src 'self' data: https: blob:; " +
          "connect-src 'self' https: wss: ws:; " +
          "frame-ancestors 'self';"
        );
        document.head.appendChild(meta);
      }
    }

  }, [enforceHTTPS, showSecurityWarnings, toast]);

  const contextValue: SecurityContextType = {
    isSecure,
    forceHTTPS,
    checkSecurityStatus
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

// Security status component for debugging
export const SecurityStatus: React.FC = () => {
  const { checkSecurityStatus } = useSecurityContext();
  const [status, setStatus] = useState<SecurityStatus | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    setStatus(checkSecurityStatus());
  }, [checkSecurityStatus]);

  if (!status || import.meta.env.PROD) {
    return null;
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-3 max-w-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${status.isHTTPS ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm font-medium">
              Security: <span className={getScoreColor(status.securityScore)}>{status.securityScore}/100</span>
            </span>
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {showDetails ? 'Hide' : 'Details'}
          </button>
        </div>
        
        {showDetails && (
          <div className="mt-2 text-xs space-y-1">
            <div className="flex justify-between">
              <span>HTTPS:</span>
              <span className={status.isHTTPS ? 'text-green-600' : 'text-red-600'}>
                {status.isHTTPS ? '✓' : '✗'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Headers:</span>
              <span className={status.hasSecureHeaders ? 'text-green-600' : 'text-yellow-600'}>
                {status.hasSecureHeaders ? '✓' : '~'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Environment:</span>
              <span className="text-blue-600">
                {status.isLocalhost ? 'Development' : 'Production'}
              </span>
            </div>
            
            {status.warnings.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="text-red-600 font-medium">Warnings:</div>
                {status.warnings.map((warning, index) => (
                  <div key={index} className="text-red-600">• {warning}</div>
                ))}
              </div>
            )}
            
            {status.recommendations.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="text-yellow-600 font-medium">Recommendations:</div>
                {status.recommendations.slice(0, 2).map((rec, index) => (
                  <div key={index} className="text-yellow-600">• {rec}</div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

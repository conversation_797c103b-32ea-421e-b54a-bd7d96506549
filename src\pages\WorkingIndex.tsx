import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Plus } from "lucide-react";
import DashboardHeader from "@/components/DashboardHeader";
import OAuthSetupGuide from "@/components/OAuthSetupGuide";

const WorkingIndex = () => {
  const [showSetupGuide, setShowSetupGuide] = useState(false);
  const [isLoading] = useState(false);

  // Mock data for testing
  const mockAccounts = [
    { id: "1", platform: "Instagram", connected: false, username: "", followers: "" },
    { id: "2", platform: "X (Twitter)", connected: false, username: "", followers: "" },
    { id: "3", platform: "Facebook", connected: false, username: "", followers: "" },
    { id: "4", platform: "LinkedIn", connected: false, username: "", followers: "" },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />
      
      <main className="container mx-auto px-4 py-8 space-y-8">
        {/* OAuth Setup Guide */}
        {showSetupGuide && (
          <section>
            <OAuthSetupGuide />
          </section>
        )}

        {/* Social Accounts Section */}
        <section>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Connected Accounts</h2>
            <Button
              variant="outline"
              onClick={() => setShowSetupGuide(!showSetupGuide)}
              className="text-sm"
            >
              {showSetupGuide ? "Hide" : "Show"} OAuth Setup
            </Button>
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {mockAccounts.map((account) => (
                <Card key={account.id} className="hover:shadow-lg transition-all duration-200 border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-5 w-5 bg-gray-400 rounded"></div>
                        <CardTitle className="text-sm font-medium text-gray-800">{account.platform}</CardTitle>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600">Connect your {account.platform} account to start posting</p>
                      <Button 
                        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 text-white"
                        onClick={() => alert(`Demo: Would connect to ${account.platform}`)}
                      >
                        Connect Account
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </section>

        {/* AI Content Generation Section */}
        <section>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Create New Post</h2>
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl text-gray-800">AI Content Generator</CardTitle>
              <CardDescription>
                Select a platform and topic to generate engaging social media content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Platform
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Choose platform</option>
                    <option value="Instagram">Instagram</option>
                    <option value="X (Twitter)">X (Twitter)</option>
                    <option value="Facebook">Facebook</option>
                    <option value="LinkedIn">LinkedIn</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Topic
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., Digital Marketing, Leadership"
                  />
                </div>
              </div>
              
              <div>
                <Button 
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                  onClick={() => alert("Demo: Would generate content")}
                >
                  Generate Content
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Scheduled Posts Section */}
        <section>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Scheduled Posts</h2>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Post
            </Button>
          </div>
          
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm p-8 text-center">
            <p className="text-gray-500">No posts scheduled yet. Create your first post above!</p>
          </Card>
        </section>
      </main>
    </div>
  );
};

export default WorkingIndex;

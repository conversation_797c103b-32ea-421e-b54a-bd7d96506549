import { toast } from "@/hooks/use-toast";
import { config as envConfig, isFeatureEnabled } from '@/config/environment';
import { config as oauthConfig, hasValidOAuthCredentials, generateOAuthUrl, getPlatformInfo } from '@/config/oauth';
import { handleOAuthCallback } from './oauthCallbackService';
import { socialAccountsApi, postsApi, analyticsApi, contentApi, ApiError } from './apiClient';

// Types for our social media functionality
export type SocialPlatform = "Instagram" | "X (Twitter)" | "Facebook" | "LinkedIn";

// OAuth configuration for each platform
interface OAuthConfig {
  clientId: string;
  redirectUri: string;
  scope: string;
  authUrl: string;
  tokenUrl: string;
}

// Platform mapping for OAuth configuration
const platformMapping: Record<SocialPlatform, keyof typeof oauthConfig.oauth> = {
  "Instagram": "instagram",
  "X (Twitter)": "twitter",
  "Facebook": "facebook",
  "LinkedIn": "linkedin"
};

export interface SocialAccount {
  id: string;
  platform: SocialPlatform;
  connected: boolean;
  username: string;
  displayName?: string;
  followers: string | number;
  profileUrl?: string;
  avatarUrl?: string;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiresAt?: string;
}

export interface SocialPost {
  id: number;
  platform: SocialPlatform;
  content: string;
  scheduledFor: string;
  status: "scheduled" | "posted" | "failed";
}

// Mock storage for connected accounts (in a real app, this would be server-side)
const connectedAccounts: SocialAccount[] = [
  { id: "1", platform: "Instagram", connected: false, username: "", followers: 0 },
  { id: "2", platform: "X (Twitter)", connected: false, username: "", followers: 0 },
  { id: "3", platform: "Facebook", connected: false, username: "", followers: 0 },
  { id: "4", platform: "LinkedIn", connected: false, username: "", followers: 0 },
];

// Mock storage for posts
let posts: SocialPost[] = [];

// Get all social accounts
export const getSocialAccounts = async (): Promise<SocialAccount[]> => {
  try {
    if (config.isProduction || isFeatureEnabled('realTimePosting')) {
      // Use real API in production or when real-time posting is enabled
      const response = await socialAccountsApi.getAccounts();
      if (response.success && response.data) {
        return response.data;
      }
    }

    // Fallback to mock data for development
    return connectedAccounts;
  } catch (error) {
    console.warn('Failed to fetch social accounts from API, using mock data:', error);
    return connectedAccounts;
  }
};

// Generate OAuth URL for a platform using the imported function
const generatePlatformOAuthUrl = (platform: SocialPlatform): string => {
  const platformKey = platformMapping[platform];
  const state = Math.random().toString(36).substring(2, 15);

  // Store state for verification
  localStorage.setItem(`oauth_state_${platform}`, state);

  return generateOAuthUrl(platformKey, state);
};

// Connect to a social media platform using real OAuth
export const connectSocialAccount = async (platform: SocialPlatform): Promise<SocialAccount> => {
  try {
    const platformKey = platformMapping[platform];

    // Check if we have valid OAuth credentials for this platform
    if (!hasValidOAuthCredentials(platformKey)) {
      // Fallback to demo mode if no real credentials
      toast({
        title: "Demo Mode",
        description: `${platform} connection is in demo mode. Add your OAuth credentials to .env file for real authentication.`,
        variant: "default",
      });

      return connectDemoAccount(platform);
    }

    // Generate OAuth URL
    const authUrl = generatePlatformOAuthUrl(platform);

    // Open OAuth popup
    const popup = window.open(
      authUrl,
      `Connect to ${platform}`,
      "width=600,height=700,scrollbars=yes,resizable=yes"
    );

    if (!popup) {
      throw new Error("Popup blocked. Please allow popups for this site.");
    }

    // Return a promise that resolves when authentication is complete
    return new Promise((resolve, reject) => {
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new Error("Authentication cancelled by user"));
        }
      }, 1000);

      // Listen for the OAuth callback
      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'OAUTH_SUCCESS') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();

          // Process the OAuth response
          handleOAuthSuccess(platform, event.data.code, event.data.state)
            .then(resolve)
            .catch(reject);
        } else if (event.data.type === 'OAUTH_ERROR') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          popup.close();
          reject(new Error(event.data.error || 'Authentication failed'));
        }
      };

      window.addEventListener('message', messageHandler);

      // Timeout after 5 minutes
      setTimeout(() => {
        clearInterval(checkClosed);
        window.removeEventListener('message', messageHandler);
        if (!popup.closed) {
          popup.close();
        }
        reject(new Error('Authentication timeout'));
      }, 300000);
    });
  } catch (error) {
    console.error("Error connecting account:", error);
    toast({
      title: "Connection Failed",
      description: error instanceof Error ? error.message : `Could not connect to ${platform}. Please try again.`,
      variant: "destructive",
    });
    throw error;
  }
};

// Handle OAuth success and exchange code for token
const handleOAuthSuccess = async (platform: SocialPlatform, code: string, state: string): Promise<SocialAccount> => {
  // Verify state parameter
  const storedState = localStorage.getItem(`oauth_state_${platform}`);
  if (state !== storedState) {
    throw new Error("Invalid state parameter");
  }

  // Clean up stored state
  localStorage.removeItem(`oauth_state_${platform}`);

  const platformKey = platformMapping[platform];
  const config = oauthConfig.oauth[platformKey];

  try {
    // Exchange authorization code for access token
    const tokenResponse = await fetch(config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: config.clientId,
        client_secret: import.meta.env?.VITE_CLIENT_SECRET || '',
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: config.redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error(`Token exchange failed: ${tokenResponse.statusText}`);
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;

    // Fetch user profile information
    const userInfo = await fetchUserProfile(platform, accessToken);

    // Update the account in our storage
    const accountIndex = connectedAccounts.findIndex(acc => acc.platform === platform);
    if (accountIndex >= 0) {
      connectedAccounts[accountIndex] = {
        ...connectedAccounts[accountIndex],
        connected: true,
        username: userInfo.username,
        followers: userInfo.followers,
        accessToken: accessToken
      };

      // Store token securely (in a real app, this would be server-side)
      localStorage.setItem(`${platform}_token`, accessToken);

      return connectedAccounts[accountIndex];
    } else {
      throw new Error("Platform not found");
    }
  } catch (error) {
    console.error(`Error handling OAuth for ${platform}:`, error);
    throw error;
  }
};

// Fetch user profile from each platform
const fetchUserProfile = async (platform: SocialPlatform, accessToken: string) => {
  switch (platform) {
    case "Instagram":
      return fetchInstagramProfile(accessToken);
    case "Facebook":
      return fetchFacebookProfile(accessToken);
    case "LinkedIn":
      return fetchLinkedInProfile(accessToken);
    case "X (Twitter)":
      return fetchTwitterProfile(accessToken);
    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }
};

// Platform-specific profile fetchers
const fetchInstagramProfile = async (accessToken: string) => {
  const response = await fetch(`https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${accessToken}`);
  const data = await response.json();
  return {
    username: `@${data.username}`,
    followers: `${data.media_count || 0} posts`
  };
};

const fetchFacebookProfile = async (accessToken: string) => {
  const response = await fetch(`https://graph.facebook.com/me?fields=id,name&access_token=${accessToken}`);
  const data = await response.json();
  return {
    username: data.name,
    followers: "Facebook Page"
  };
};

const fetchLinkedInProfile = async (accessToken: string) => {
  const response = await fetch('https://api.linkedin.com/v2/userinfo', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  return {
    username: `${data.given_name || ''} ${data.family_name || ''}`.trim() ||
              `${data.firstName?.localized?.en_US || ''} ${data.lastName?.localized?.en_US || ''}`.trim(),
    followers: "LinkedIn Profile"
  };
};

const fetchTwitterProfile = async (accessToken: string) => {
  const response = await fetch('https://api.twitter.com/2/users/me?user.fields=public_metrics', {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });
  const data = await response.json();
  return {
    username: `@${data.data?.username || 'user'}`,
    followers: `${data.data?.public_metrics?.followers_count || 0} followers`
  };
};

// Demo mode fallback
const connectDemoAccount = async (platform: SocialPlatform): Promise<SocialAccount> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const accountIndex = connectedAccounts.findIndex(acc => acc.platform === platform);
      if (accountIndex >= 0) {
        const platformData = {
          'Instagram': { username: '@pulsebuzz_demo', followers: 1250, avatar: 'photo-*************-5658abf4ff4e' },
          'X (Twitter)': { username: '@pulsebuzz_ai', followers: 890, avatar: 'photo-*************-0a1dd7228f2d' },
          'LinkedIn': { username: 'pulsebuzz-ai', followers: 567, avatar: 'photo-*************-2616b612b786' },
          'Facebook': { username: 'PulseBuzzAI', followers: 2340, avatar: 'photo-*************-6461ffad8d80' }
        };

        const data = platformData[platform as keyof typeof platformData] || {
          username: `@demo_${platform.toLowerCase().replace(/\s+/g, '')}`,
          followers: Math.floor(Math.random() * 5000) + 500,
          avatar: 'photo-*************-5658abf4ff4e'
        };

        connectedAccounts[accountIndex] = {
          ...connectedAccounts[accountIndex],
          connected: true,
          username: data.username,
          displayName: `${platform} Demo Account`,
          followers: data.followers,
          profileUrl: `https://${platform.toLowerCase().replace(/\s+/g, '').replace('(twitter)', 'twitter')}.com/${data.username.replace('@', '')}`,
          avatarUrl: `https://images.unsplash.com/${data.avatar}?w=64&h=64&fit=crop&crop=face`,
          accessToken: "demo-token-" + Math.random().toString(36).substring(2)
        };

        resolve(connectedAccounts[accountIndex]);
      }
    }, 1500);
  });
};

// Disconnect a social media account
export const disconnectSocialAccount = async (platform: SocialPlatform): Promise<void> => {
  try {
    const account = connectedAccounts.find(acc => acc.platform === platform);

    if (account?.accessToken && !account.accessToken.startsWith('demo-token')) {
      // Revoke the access token for real accounts
      try {
        await revokeAccessToken(platform, account.accessToken);
      } catch (error) {
        console.warn(`Failed to revoke token for ${platform}:`, error);
        // Continue with disconnection even if revocation fails
      }
    }

    // Remove stored token
    localStorage.removeItem(`${platform}_token`);

    // Update our storage
    const accountIndex = connectedAccounts.findIndex(acc => acc.platform === platform);
    if (accountIndex >= 0) {
      connectedAccounts[accountIndex] = {
        ...connectedAccounts[accountIndex],
        connected: false,
        username: "",
        displayName: undefined,
        followers: 0,
        profileUrl: undefined,
        avatarUrl: undefined,
        accessToken: undefined,
        refreshToken: undefined,
        tokenExpiresAt: undefined
      };
    }

    toast({
      title: "Account Disconnected",
      description: `Your ${platform} account has been disconnected successfully.`,
    });
  } catch (error) {
    console.error("Error disconnecting account:", error);
    toast({
      title: "Disconnection Failed",
      description: `Could not disconnect from ${platform}. Please try again.`,
      variant: "destructive",
    });
    throw error;
  }
};

// Revoke access token for each platform
const revokeAccessToken = async (platform: SocialPlatform, accessToken: string): Promise<void> => {
  switch (platform) {
    case "Instagram":
    case "Facebook":
      // Facebook/Instagram token revocation
      await fetch(`https://graph.facebook.com/me/permissions?access_token=${accessToken}`, {
        method: 'DELETE'
      });
      break;
    case "LinkedIn":
      // LinkedIn doesn't have a public revocation endpoint
      // Token will expire naturally
      break;
    case "X (Twitter)":
      // Twitter token revocation
      await fetch('https://api.twitter.com/2/oauth2/revoke', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          token: accessToken,
          client_id: oauthConfig.oauth[platformMapping[platform]].clientId,
        }),
      });
      break;
  }
};

// Post to social media
export const createSocialPost = async (
  platform: SocialPlatform,
  content: string,
  scheduledFor: string
): Promise<SocialPost> => {
  try {
    // Check if account is connected
    const accounts = await getSocialAccounts();
    const account = accounts.find(acc => acc.platform === platform);
    if (!account || !account.connected) {
      throw new Error(`You need to connect your ${platform} account first`);
    }

    const postData = {
      platform,
      content,
      scheduledFor,
      status: "scheduled" as const
    };

    if (config.isProduction || isFeatureEnabled('realTimePosting')) {
      // Use real API in production
      try {
        const response = await postsApi.schedulePost(postData);
        if (response.success && response.data) {
          toast({
            title: "Post Scheduled!",
            description: `Your post has been scheduled to ${platform}.`,
          });
          return response.data;
        }
      } catch (apiError) {
        console.warn('API post creation failed, falling back to mock:', apiError);
      }
    }

    // Fallback to mock storage for development
    const newPost: SocialPost = {
      id: Date.now(),
      ...postData
    };

    posts = [...posts, newPost];

    toast({
      title: "Post Scheduled! (Demo Mode)",
      description: `Your post has been scheduled to ${platform} in demo mode.`,
    });

    return newPost;
  } catch (error) {
    console.error("Error creating post:", error);
    toast({
      title: "Post Creation Failed",
      description: error instanceof Error ? error.message : "Could not create post. Please try again.",
      variant: "destructive",
    });
    throw error;
  }
};

// Get all posts
export const getPosts = async (): Promise<SocialPost[]> => {
  try {
    if (config.isProduction || isFeatureEnabled('realTimePosting')) {
      // Use real API in production
      const response = await postsApi.getPosts();
      if (response.success && response.data) {
        return response.data;
      }
    }

    // Fallback to mock data for development
    return posts;
  } catch (error) {
    console.warn('Failed to fetch posts from API, using mock data:', error);
    return posts;
  }
};

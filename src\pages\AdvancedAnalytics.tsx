
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import PageHeader from '@/components/PageHeader';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { TopHeader } from '@/components/TopHeader';
import AdvancedAnalytics from '@/components/AdvancedAnalytics';
import {
  TrendingUp,
  Users,
  Eye,
  Download,
  BarChart3
} from 'lucide-react';

const AdvancedAnalyticsPage: React.FC = () => {
  const [loading] = useState(false);

  const handleGenerateReport = () => {
    console.log('Generating comprehensive analytics report');
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />

          <main className="flex-1 overflow-auto">
            <PageHeader
              title="Advanced Analytics"
              description="Comprehensive insights and detailed performance metrics"
              icon={<BarChart3 className="w-8 h-8" />}
              actions={
                <Button onClick={handleGenerateReport} disabled={loading} className="bg-white text-blue-600 hover:bg-white/90">
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </Button>
              }
            />

            <div className="px-6 pb-6 space-y-6">
              {/* Quick Stats */}
              <div className="grid gap-4 md:grid-cols-3">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Impressions
                    </CardTitle>
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">245.7K</div>
                    <p className="text-xs text-muted-foreground">
                      +20.1% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Engagement Rate
                    </CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">5.4%</div>
                    <p className="text-xs text-muted-foreground">
                      +0.8% from last month
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      Follower Growth
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">+1,234</div>
                    <p className="text-xs text-muted-foreground">
                      +18.2% from last month
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Advanced Analytics Component */}
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Analytics</CardTitle>
                  <CardDescription>
                    Comprehensive insights into your social media performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <AdvancedAnalytics onGenerateReport={handleGenerateReport} />
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AdvancedAnalyticsPage;

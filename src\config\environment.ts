// Environment configuration for production-ready setup
export interface EnvironmentConfig {
  apiBaseUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
  oauth: {
    instagram: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
    };
    facebook: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
    };
    linkedin: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
    };
    twitter: {
      clientId: string;
      clientSecret: string;
      redirectUri: string;
    };
  };
  features: {
    realTimePosting: boolean;
    analytics: boolean;
    aiContentGeneration: boolean;
  };
}

const getEnvironmentConfig = (): EnvironmentConfig => {
  const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:8080';
  const isDevelopment = import.meta.env.DEV;
  const isProduction = import.meta.env.PROD;

  return {
    apiBaseUrl: import.meta.env.VITE_API_BASE_URL || (isDevelopment ? 'http://localhost:3001/api' : '/api'),
    isDevelopment,
    isProduction,
    oauth: {
      instagram: {
        clientId: import.meta.env.VITE_INSTAGRAM_CLIENT_ID || '',
        clientSecret: import.meta.env.VITE_INSTAGRAM_CLIENT_SECRET || '',
        redirectUri: `${origin}/auth/instagram/callback`,
      },
      facebook: {
        clientId: import.meta.env.VITE_FACEBOOK_CLIENT_ID || '',
        clientSecret: import.meta.env.VITE_FACEBOOK_CLIENT_SECRET || '',
        redirectUri: `${origin}/auth/facebook/callback`,
      },
      linkedin: {
        clientId: import.meta.env.VITE_LINKEDIN_CLIENT_ID || '',
        clientSecret: import.meta.env.VITE_LINKEDIN_CLIENT_SECRET || '',
        redirectUri: `${origin}/auth/linkedin/callback`,
      },
      twitter: {
        clientId: import.meta.env.VITE_TWITTER_CLIENT_ID || '',
        clientSecret: import.meta.env.VITE_TWITTER_CLIENT_SECRET || '',
        redirectUri: `${origin}/auth/twitter/callback`,
      },
    },
    features: {
      realTimePosting: import.meta.env.VITE_ENABLE_REAL_TIME_POSTING === 'true',
      analytics: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
      aiContentGeneration: import.meta.env.VITE_ENABLE_AI_CONTENT === 'true',
    },
  };
};

export const config = getEnvironmentConfig();

// Helper functions
export const hasValidOAuthCredentials = (platform: keyof typeof config.oauth): boolean => {
  const platformConfig = config.oauth[platform];
  return !!(platformConfig.clientId && 
           platformConfig.clientSecret && 
           !platformConfig.clientId.startsWith('your-') &&
           !platformConfig.clientSecret.startsWith('your-'));
};

export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature];
};

export const getApiUrl = (endpoint: string): string => {
  return `${config.apiBaseUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
};

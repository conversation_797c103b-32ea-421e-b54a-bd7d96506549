import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { FaInstagram, FaFacebookF, FaLinkedinIn, FaXTwitter } from 'react-icons/fa6';
import { useState } from "react";
import { SocialPlatform, connectSocialAccount, disconnectSocialAccount } from "@/services/socialMediaService";
import { useToast } from "@/hooks/use-toast";

interface SocialAccountCardProps {
  id: string;
  platform: SocialPlatform;
  connected: boolean;
  username: string;
  followers: string;
  onAccountUpdated: () => void;
}

const SocialAccountCard: React.FC<SocialAccountCardProps> = ({
  platform,
  connected,
  username,
  followers,
  onAccountUpdated
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const getPlatformIcon = (platform: SocialPlatform) => {
    const iconProps = { size: 20, style: { display: 'inline-block' } };

    switch (platform) {
      case "Instagram":
        return <FaInstagram {...iconProps} className="text-pink-600" />;
      case "X (Twitter)":
        return <FaXTwitter {...iconProps} className="text-gray-900" />;
      case "Facebook":
        return <FaFacebookF {...iconProps} className="text-blue-600" />;
      case "LinkedIn":
        return <FaLinkedinIn {...iconProps} className="text-blue-700" />;
      default:
        return <div className="h-5 w-5 bg-gray-400 rounded"></div>;
    }
  };

  const getPlatformColor = (platform: SocialPlatform) => {
    switch (platform) {
      case "Instagram":
        return "from-pink-500 to-purple-600";
      case "X (Twitter)":
        return "from-gray-700 to-gray-900";
      case "Facebook":
        return "from-blue-600 to-blue-700";
      case "LinkedIn":
        return "from-blue-700 to-blue-800";
      default:
        return "from-gray-500 to-gray-600";
    }
  };

  const handleConnect = async () => {
    setIsLoading(true);
    try {
      await connectSocialAccount(platform);
      toast({
        title: "Account Connected",
        description: `Your ${platform} account has been successfully connected.`,
      });
      onAccountUpdated();
    } catch (error) {
      // Error is already handled in the service
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setIsLoading(true);
    try {
      await disconnectSocialAccount(platform);
      toast({
        title: "Account Disconnected",
        description: `Your ${platform} account has been disconnected.`,
      });
      onAccountUpdated();
    } catch (error) {
      // Error is already handled in the service
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="hover:shadow-lg transition-all duration-200 border-0 bg-white/80 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getPlatformIcon(platform)}
            <CardTitle className="text-sm font-medium text-gray-800">{platform}</CardTitle>
          </div>
          {connected ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-orange-500" />
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {connected ? (
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-600">Account</p>
              <p className="font-medium text-gray-800">{username}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Followers</p>
              <p className="font-medium text-gray-800">{followers}</p>
            </div>
            <div className="flex justify-between items-center">
              <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                Connected
              </Badge>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleDisconnect}
                disabled={isLoading}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : "Disconnect"}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">Connect your {platform} account to start posting</p>
            <Button 
              className={`w-full bg-gradient-to-r ${getPlatformColor(platform)} hover:opacity-90 text-white`}
              onClick={handleConnect}
              disabled={isLoading}
            >
              {isLoading ? (
                <React.Fragment><Loader2 className="h-4 w-4 mr-2 animate-spin" /> Connecting...</React.Fragment>
              ) : (
                "Connect Account"
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SocialAccountCard;

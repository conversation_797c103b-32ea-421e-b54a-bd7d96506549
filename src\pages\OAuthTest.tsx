import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  CheckCircle, 
  AlertCircle, 
  Save,
  Eye,
  EyeOff,
  Database,
  TestTube,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/SupabaseAuthContext';
import { useAppStore } from '@/store/appStore';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { TopHeader } from '@/components/TopHeader';

interface PlatformCredentials {
  clientId: string;
  clientSecret: string;
  showSecret: boolean;
}

const OAuthTest: React.FC = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { workspace } = useAppStore();
  const [credentials, setCredentials] = useState<Record<string, PlatformCredentials>>({});
  const [saving, setSaving] = useState<string | null>(null);

  const platforms = [
    { id: 'instagram', name: 'Instagram', icon: '📷' },
    { id: 'facebook', name: 'Facebook', icon: '📘' },
    { id: 'linkedin', name: 'LinkedIn', icon: '💼' },
    { id: 'twitter', name: 'X (Twitter)', icon: '🐦' }
  ];

  // Initialize credentials state
  useEffect(() => {
    const initialCredentials: Record<string, PlatformCredentials> = {};
    platforms.forEach(platform => {
      initialCredentials[platform.id] = {
        clientId: '',
        clientSecret: '',
        showSecret: false
      };
    });
    setCredentials(initialCredentials);
  }, []);

  const handleCredentialChange = (platform: string, field: 'clientId' | 'clientSecret', value: string) => {
    setCredentials(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        [field]: value
      }
    }));
  };

  const toggleSecretVisibility = (platform: string) => {
    setCredentials(prev => ({
      ...prev,
      [platform]: {
        ...prev[platform],
        showSecret: !prev[platform]?.showSecret
      }
    }));
  };

  const saveCredentials = async (platform: string) => {
    const creds = credentials[platform];
    if (!creds?.clientId || !creds?.clientSecret) {
      toast({
        title: "Missing Information",
        description: "Please enter both Client ID and Client Secret",
        variant: "destructive",
      });
      return;
    }

    setSaving(platform);
    
    try {
      // Simulate saving to database (replace with actual service call)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For now, save to sessionStorage as fallback
      sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`, creds.clientId);
      sessionStorage.setItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`, creds.clientSecret);

      toast({
        title: "Credentials Saved",
        description: `${platforms.find(p => p.id === platform)?.name} credentials saved successfully`,
      });

      // Clear input fields
      setCredentials(prev => ({
        ...prev,
        [platform]: {
          clientId: '',
          clientSecret: '',
          showSecret: false
        }
      }));
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save credentials. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(null);
    }
  };

  const isConfigured = (platform: string) => {
    const clientId = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`);
    const clientSecret = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`);
    return !!(clientId && clientSecret);
  };

  const getRedirectUri = (platform: string) => {
    const origin = window.location.origin;
    return `${origin}/auth/${platform}/callback`;
  };

  const testOAuthFlow = (platform: string) => {
    // Get stored credentials for the platform
    const clientId = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_ID`);
    const clientSecret = sessionStorage.getItem(`VITE_${platform.toUpperCase()}_CLIENT_SECRET`);

    if (!clientId || !clientSecret) {
      toast({
        title: "No Credentials",
        description: `Please configure ${platforms.find(p => p.id === platform)?.name} credentials first`,
        variant: "destructive",
      });
      return;
    }

    const redirectUri = encodeURIComponent(getRedirectUri(platform));
    const authUrls = {
      instagram: `https://api.instagram.com/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=user_profile,user_media&response_type=code`,
      facebook: `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=pages_manage_posts&response_type=code`,
      linkedin: `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=w_member_social`,
      twitter: `https://twitter.com/i/oauth2/authorize?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=tweet.read%20tweet.write%20users.read`
    };

    const url = authUrls[platform as keyof typeof authUrls];
    if (url) {
      // Show the exact redirect URI that will be used
      toast({
        title: "OAuth Configuration Check",
        description: `Make sure this redirect URI is configured in your ${platforms.find(p => p.id === platform)?.name} app: ${getRedirectUri(platform)}`,
        duration: 5000,
      });

      // Redirect to OAuth URL after showing the message
      setTimeout(() => {
        window.location.href = url;
      }, 2000);
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopHeader />
          <main className="flex-1 p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">OAuth Credentials Test</h1>
                <p className="text-gray-600">
                  Test OAuth credential storage and configuration for social media platforms.
                </p>
              </div>

              <Alert className="mb-6">
                <Database className="h-4 w-4" />
                <AlertDescription>
                  <strong>Test Mode:</strong> This page allows you to test OAuth credential storage. 
                  Credentials are temporarily stored in session storage for testing purposes.
                </AlertDescription>
              </Alert>

              <div className="grid gap-6">
                {platforms.map(platform => {
                  const creds = credentials[platform.id] || { clientId: '', clientSecret: '', showSecret: false };
                  const configured = isConfigured(platform.id);
                  const isSaving = saving === platform.id;

                  return (
                    <Card key={platform.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-2xl">{platform.icon}</span>
                            <div>
                              <CardTitle className="text-lg">{platform.name}</CardTitle>
                              <CardDescription>Configure OAuth credentials for {platform.name}</CardDescription>
                            </div>
                          </div>
                          <Badge variant={configured ? "default" : "secondary"}>
                            {configured ? (
                              <><CheckCircle className="w-3 h-3 mr-1" /> Configured</>
                            ) : (
                              <><AlertCircle className="w-3 h-3 mr-1" /> Not Configured</>
                            )}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {configured ? (
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="w-4 h-4 text-green-600" />
                                <span className="text-sm font-medium text-green-800">Credentials Configured</span>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => testOAuthFlow(platform.id)}
                              >
                                <TestTube className="w-4 h-4 mr-2" />
                                Test OAuth
                              </Button>
                            </div>
                            <div className="mt-3 space-y-2">
                              <p className="text-xs text-green-700">
                                OAuth credentials are stored and ready for use.
                              </p>
                              <div className="text-xs text-gray-600">
                                <strong>Redirect URI:</strong> {getRedirectUri(platform.id)}
                              </div>
                              <div className="text-xs text-gray-600">
                                <strong>Client ID:</strong> {sessionStorage.getItem(`VITE_${platform.id.toUpperCase()}_CLIENT_ID`)?.substring(0, 8)}...
                              </div>
                              <div className="text-xs text-orange-600 mt-2 p-2 bg-orange-50 rounded">
                                ⚠️ <strong>Important:</strong> Make sure this exact redirect URI is configured in your {platform.name} developer console!
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor={`${platform.id}-client-id`} className="text-sm font-medium">
                                Client ID
                              </Label>
                              <Input
                                id={`${platform.id}-client-id`}
                                value={creds.clientId}
                                onChange={(e) => handleCredentialChange(platform.id, 'clientId', e.target.value)}
                                placeholder="Enter your client ID"
                                className="text-sm"
                              />
                            </div>

                            <div>
                              <Label htmlFor={`${platform.id}-client-secret`} className="text-sm font-medium">
                                Client Secret
                              </Label>
                              <div className="flex items-center space-x-2">
                                <Input
                                  id={`${platform.id}-client-secret`}
                                  type={creds.showSecret ? "text" : "password"}
                                  value={creds.clientSecret}
                                  onChange={(e) => handleCredentialChange(platform.id, 'clientSecret', e.target.value)}
                                  placeholder="Enter your client secret"
                                  className="text-sm"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => toggleSecretVisibility(platform.id)}
                                >
                                  {creds.showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                </Button>
                              </div>
                            </div>

                            <Button
                              onClick={() => saveCredentials(platform.id)}
                              disabled={!creds.clientId || !creds.clientSecret || isSaving}
                              className="w-full"
                            >
                              {isSaving ? (
                                <>
                                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                  Saving...
                                </>
                              ) : (
                                <>
                                  <Save className="w-4 h-4 mr-2" />
                                  Save Credentials
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              <Card className="mt-6 border-orange-200 bg-orange-50">
                <CardHeader>
                  <CardTitle className="text-orange-800">🚨 Critical: Configure Redirect URIs</CardTitle>
                  <CardDescription className="text-orange-700">
                    You MUST add these exact redirect URIs to your platform developer consoles before testing OAuth.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {platforms.map(platform => (
                      <div key={platform.id} className="bg-white p-3 rounded border">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-lg">{platform.icon}</span>
                          <strong className="text-sm">{platform.name}</strong>
                        </div>
                        <div className="text-xs font-mono bg-gray-100 p-2 rounded">
                          {getRedirectUri(platform.id)}
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          Add this URL to your {platform.name} app's OAuth redirect URIs
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Next Steps</CardTitle>
                  <CardDescription>
                    After configuring your OAuth credentials, you can test the integration.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>1. <strong>Configure credentials</strong> for each platform above</p>
                    <p>2. <strong>Add redirect URIs</strong> to your platform developer consoles (see section above)</p>
                    <p>3. <strong>Test OAuth flows</strong> using the test buttons</p>
                    <p>4. <strong>Test social media posting</strong> in the Create Post page</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Debug Information</CardTitle>
                  <CardDescription>
                    Technical details for troubleshooting OAuth configuration.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-xs font-mono">
                    <div>
                      <strong>Current Origin:</strong> {window.location.origin}
                    </div>
                    <div>
                      <strong>User Authenticated:</strong> {user ? '✅ Yes' : '❌ No'}
                    </div>
                    <div>
                      <strong>Workspace ID:</strong> {workspace?.id || 'Not set'}
                    </div>
                    <div>
                      <strong>Stored Credentials:</strong>
                      <ul className="ml-4 mt-1">
                        {platforms.map(platform => (
                          <li key={platform.id}>
                            {platform.name}: {isConfigured(platform.id) ? '✅ Configured' : '❌ Not configured'}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default OAuthTest;
